"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const app_1 = require("./app");
const main_swagger_1 = require("./main.swagger");
const env_utils_1 = require("./shared/env/env.utils");
const bugsnag_service_1 = require("./shared/bugsnag/bugsnag.service");
const http_exception_filter_1 = require("./shared/filter/http-exception.filter");
const compression_1 = __importDefault(require("compression"));
const express_1 = require("express");
const i18n_service_1 = require("./shared/i18n/i18n.service");
const prisma_client_1 = require("./shared/prisma/prisma.client");
const seed_data_1 = require("./shared/seed/seed.data");
const seed_service_1 = require("./modules/seed/seed.service");
const express_session_1 = __importDefault(require("express-session"));
async function bootstrap() {
    const startTime = Date.now();
    console.log('Bootstrap starting at:', new Date().toISOString());
    console.log('NODE_STAGE : ', process.env.NODE_STAGE);
    console.time('initSeed');
    await (0, prisma_client_1.initSeed)(seed_data_1.loadSeed);
    console.timeEnd('initSeed');
    console.time('App initialization');
    const app = await (0, app_1.App)();
    console.timeEnd('App initialization');
    console.time('Middleware setup');
    const i18nService = app.get(i18n_service_1.I18nService);
    const bugsnagService = app.get(bugsnag_service_1.BugsnagService);
    const middleware = bugsnagService.client.getPlugin('express');
    app.use(middleware.requestHandler);
    app.use(middleware.errorHandler);
    app.useGlobalFilters(new http_exception_filter_1.HttpExceptionFilter(bugsnagService, i18nService));
    console.timeEnd('Middleware setup');
    process.on('uncaughtException', error => {
        bugsnagService.client.notify(error);
    });
    process.on('unhandledRejection', reason => {
        bugsnagService.client.notify(new Error(reason));
    });
    console.time('Express configuration');
    app.use('/payment/webhook', (0, express_1.raw)({ type: '*/*' }));
    app.use((0, express_1.json)({ limit: '1000mb' }));
    app.use((0, express_1.urlencoded)({ extended: true, limit: '1000mb' }));
    app.use((0, compression_1.default)());
    app.use((0, express_session_1.default)({
        secret: process.env.EXPRESS_SESSION_SECRET,
        resave: false,
        saveUninitialized: false,
    }));
    console.timeEnd('Express configuration');
    if (process.env.NODE_ENV !== 'production' && module.hot) {
        module.hot.accept();
        module.hot.dispose(() => app.close());
    }
    if (!(0, env_utils_1.isProductionEnv)()) {
        console.time('Swagger setup');
        await (0, main_swagger_1.AppSwagger)(app);
        console.timeEnd('Swagger setup');
    }
    const port = process.env.NEST_SERVER_PORT || 3000;
    const endTime = Date.now();
    console.log(`Total bootstrap time: ${endTime - startTime}ms`);
    if (!(0, env_utils_1.isSeedEnv)()) {
        console.time('Server startup');
        await app.listen(port);
        console.timeEnd('Server startup');
        console.log(`Server ready at: http://localhost:${port}/docs`);
    }
    else {
        console.time('App initialization (seed mode)');
        await app.init();
        const seedService = await app.resolve(seed_service_1.SeedService);
        await seedService.init();
        console.timeEnd('App initialization (seed mode)');
    }
}
bootstrap();
//# sourceMappingURL=main.js.map