"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.isSeed = exports.isReset = exports.isReseting = exports.prisma = exports.seedDumpPath = exports.seedSqlPath = exports.seedJsonPath = void 0;
exports.saveDb = saveDb;
exports.disconnect = disconnect;
exports.connect = connect;
const client_1 = require("@prisma/client");
const child_process_1 = require("child_process");
const path_1 = __importDefault(require("path"));
const root = process.cwd();
exports.seedJsonPath = path_1.default.join(root, 'seed.json');
exports.seedSqlPath = path_1.default.join(root, 'seed.sql');
exports.seedDumpPath = path_1.default.join(root, 'seed.dump');
exports.prisma = new client_1.PrismaClient();
exports.isReseting = false;
exports.isReset = false;
exports.isSeed = false;
const getDatabaseUrl = () => {
    return process.env.DATABASE_URL ? process.env.DATABASE_URL.split('?')[0] : '';
};
async function saveDb() {
    console.log('Saving database...');
    console.log(`pg_dump -Fc --dbname=${getDatabaseUrl()} > ${exports.seedDumpPath}`);
    await execPromise(`pg_dump -Fc --dbname="${getDatabaseUrl()}" > "${exports.seedDumpPath}"`);
    await execPromise(`pg_dump --dbname="${getDatabaseUrl()}" > "${exports.seedSqlPath}"`);
    console.log('Database saved');
}
async function disconnect() {
    await exports.prisma.$disconnect();
    return true;
}
const execPromise = async (cmd) => {
    return new Promise((resolve, reject) => {
        (0, child_process_1.exec)(cmd, (error, output, errorMessage) => {
            if (output && output.indexOf('command not found')) {
            }
            if (errorMessage && errorMessage.indexOf('command not found')) {
            }
            resolve(true);
        });
    });
};
async function connect() {
    await exports.prisma.$connect();
    return true;
}
exports.prisma.$connect();
saveDb().finally(() => {
    exports.prisma.$disconnect();
});
//# sourceMappingURL=dump-db.js.map