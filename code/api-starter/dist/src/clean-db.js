"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.prisma = void 0;
exports.eraseDb = eraseDb;
exports.buildDb = buildDb;
exports.disconnect = disconnect;
exports.connect = connect;
const client_1 = require("@prisma/client");
const child_process_1 = require("child_process");
const dotenv_1 = __importDefault(require("dotenv"));
if (process.env.DATABASE_URL === undefined) {
    dotenv_1.default.config();
}
const getDatabaseUrl = () => {
    return process.env.DATABASE_URL ? process.env.DATABASE_URL.split('?')[0] : '';
};
const root = process.cwd();
exports.prisma = new client_1.PrismaClient();
async function eraseDb() {
    console.log('Erasing database...');
    const tablenames = await exports.prisma.$queryRaw `SELECT tablename FROM pg_tables WHERE schemaname='public'`;
    for (const i in tablenames) {
        if (tablenames[i].tablename !== '_prisma_migrations') {
            try {
                await exports.prisma.$queryRawUnsafe(`DROP TABLE "public"."${tablenames[i].tablename}" CASCADE;`);
            }
            catch (error) {
                console.warn({ error });
            }
        }
    }
    console.log('Database erased');
}
async function buildDb() {
    console.log('Building database...');
    await execPromise(`npx prisma db push --accept-data-loss  --force-reset`);
    console.log('Database restored');
}
async function disconnect() {
    await exports.prisma.$disconnect();
    return true;
}
const execPromise = async (cmd) => {
    return new Promise((resolve, reject) => {
        (0, child_process_1.exec)(cmd, (error, output, errorMessage) => {
            if (output && output.indexOf('command not found')) {
                console.log('output', output.substring(0, 100));
                process.exit();
            }
            if (errorMessage && errorMessage.indexOf('command not found')) {
                console.log('error', errorMessage.substring(0, 100));
                process.exit();
            }
            if (error) {
                reject(error);
            }
            else {
                resolve(true);
            }
        });
    });
};
async function connect() {
    await exports.prisma.$connect();
    return true;
}
exports.prisma.$connect();
eraseDb().finally(() => {
    exports.prisma.$disconnect();
});
//# sourceMappingURL=clean-db.js.map