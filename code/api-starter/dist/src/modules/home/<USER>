"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HomeController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const acl_decorator_1 = require("../../acl/acl.decorator");
const acl_guard_1 = require("../../acl/acl.guard");
const auth_guard_1 = require("../auth/auth.guard");
const home_service_1 = require("./home.service");
const home_swagger_1 = require("./home.swagger");
const story = __importStar(require("../../../specs/story/home"));
const news_service_1 = require("../news/news.service");
const user_service_1 = require("../user/user.service");
let HomeController = class HomeController {
    homeService;
    newService;
    userService;
    constructor(homeService, newService, userService) {
        this.homeService = homeService;
        this.newService = newService;
        this.userService = userService;
    }
    async create(home) {
        return await this.homeService.create(home);
    }
    async find() {
        const sections = await this.homeService.find({});
        let data = {};
        await Promise.all(sections.map(async (section) => {
            const where = { id: section.value.id };
            switch (section.id) {
                case 'news':
                    data[section.id] = await this.newService.get(where);
                    break;
                case 'displayedUserCount':
                    data[section.id] =
                        section.value + (await this.userService.count({}));
                    break;
                default:
                    data[section.id] = section.value;
                    break;
            }
        }));
        return data;
    }
    async read(id) {
        const section = await this.homeService.get({ id });
        if (!section) {
            throw new common_1.NotFoundException();
        }
        return section;
    }
    async delete(id) {
        let home = false;
        try {
            home = await this.homeService.delete({ id });
        }
        catch (e) {
            console.warn(e);
        }
        if (!home) {
            throw new common_1.NotFoundException();
        }
        return { success: true };
    }
    async update(id, params) {
        let home = false;
        try {
            home = await this.homeService.update(id, params);
        }
        catch (e) {
            console.warn(e);
        }
        if (!home) {
            throw new common_1.NotFoundException();
        }
        return home;
    }
};
exports.HomeController = HomeController;
__decorate([
    (0, common_1.Post)(''),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.homeToCreate.access),
    (0, swagger_1.ApiOperation)(story.homeToCreate.operation),
    (0, swagger_1.ApiResponse)(story.homeToCreate.codes['201'].response),
    (0, swagger_1.ApiResponse)(story.homeToCreate.codes['401'].response),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [home_swagger_1.CreateHomeParams]),
    __metadata("design:returntype", Promise)
], HomeController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(''),
    (0, swagger_1.ApiOperation)(story.homeToFind.operation),
    (0, swagger_1.ApiResponse)(story.homeToFind.codes['200'].response),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], HomeController.prototype, "find", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)(story.homeToRead.operation),
    (0, swagger_1.ApiResponse)(story.homeToRead.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.homeToRead.codes['401'].response),
    (0, swagger_1.ApiResponse)(story.homeToRead.codes['404'].response),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], HomeController.prototype, "read", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.homeToDelete.access),
    (0, swagger_1.ApiOperation)(story.homeToDelete.operation),
    (0, swagger_1.ApiResponse)(story.homeToDelete.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.homeToDelete.codes['403'].response),
    (0, swagger_1.ApiResponse)(story.homeToDelete.codes['404'].response),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], HomeController.prototype, "delete", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.homeToUpdate.access),
    (0, swagger_1.ApiOperation)(story.homeToUpdate.operation),
    (0, swagger_1.ApiResponse)(story.homeToUpdate.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.homeToUpdate.codes['401'].response),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, home_swagger_1.UpdateHomeParams]),
    __metadata("design:returntype", Promise)
], HomeController.prototype, "update", null);
exports.HomeController = HomeController = __decorate([
    (0, common_1.Controller)('home'),
    (0, swagger_1.ApiTags)('home'),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [home_service_1.HomeService,
        news_service_1.NewService,
        user_service_1.UserService])
], HomeController);
//# sourceMappingURL=home.controller.js.map