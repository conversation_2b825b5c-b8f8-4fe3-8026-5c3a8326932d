import { Home } from '@prisma/client';
import { HomeService } from './home.service';
import { CreateHomeParams, UpdateHomeParams } from './home.swagger';
import { NewService } from '../news/news.service';
import { UserService } from '../user/user.service';
export declare class HomeController {
    private readonly homeService;
    private readonly newService;
    private readonly userService;
    constructor(homeService: HomeService, newService: NewService, userService: UserService);
    create(home: CreateHomeParams): Promise<Home>;
    find(): Promise<any>;
    read(id: string): Promise<any>;
    delete(id: string): Promise<any>;
    update(id: string, params: UpdateHomeParams): Promise<Home>;
}
