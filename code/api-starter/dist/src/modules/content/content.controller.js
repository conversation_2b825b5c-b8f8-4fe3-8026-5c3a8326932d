"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContentController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const acl_decorator_1 = require("../../acl/acl.decorator");
const acl_guard_1 = require("../../acl/acl.guard");
const auth_guard_1 = require("../auth/auth.guard");
const content_service_1 = require("./content.service");
const content_swagger_1 = require("./content.swagger");
const story = __importStar(require("../../../specs/story/content"));
const auth_decorator_1 = require("../auth/auth.decorator");
let ContentController = class ContentController {
    contentService;
    constructor(contentService) {
        this.contentService = contentService;
    }
    async create(admin, content) {
        const data = {
            ...content,
            owner: admin.id,
            admin: { connect: { id: admin.id } },
        };
        return await this.contentService.create(data);
    }
    whereToPrisma(params) {
        let where = {};
        if (params.name) {
            where.name = params.name;
        }
        return where;
    }
    async find(params) {
        const { take, skip, sortField = 'id', sortOrder = 'asc', ...where } = params;
        return await this.contentService.find({
            where: this.whereToPrisma(where),
            orderBy: { [sortField]: sortOrder },
            take,
            skip,
        });
    }
    async count(params) {
        return await this.contentService.count(this.whereToPrisma(params));
    }
    async read(id) {
        const content = await this.contentService.get({ id });
        if (!content) {
            throw new common_1.NotFoundException();
        }
        return content;
    }
    async delete(id) {
        const content = await this.contentService.get({ id });
        if (!content) {
            throw new common_1.NotFoundException();
        }
        await this.contentService.delete({ id });
        return { success: true };
    }
    async update(id, params) {
        let content = await this.contentService.get({ id });
        if (!content) {
            throw new common_1.NotFoundException();
        }
        content = await this.contentService.update({ id }, params);
        return content;
    }
};
exports.ContentController = ContentController;
__decorate([
    (0, common_1.Post)(''),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.contentToCreate.access),
    (0, swagger_1.ApiOperation)(story.contentToCreate.operation),
    (0, swagger_1.ApiResponse)(story.contentToCreate.codes['201'].response),
    (0, swagger_1.ApiResponse)(story.contentToCreate.codes['401'].response),
    __param(0, (0, auth_decorator_1.ReqAdmin)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, content_swagger_1.CreateContentParams]),
    __metadata("design:returntype", Promise)
], ContentController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('find'),
    (0, swagger_1.ApiOperation)(story.contentToFind.operation),
    (0, swagger_1.ApiResponse)(story.contentToFind.codes['201'].response),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [content_swagger_1.FindContentParams]),
    __metadata("design:returntype", Promise)
], ContentController.prototype, "find", null);
__decorate([
    (0, common_1.Post)('count'),
    (0, swagger_1.ApiOperation)(story.contentToCount.operation),
    (0, swagger_1.ApiResponse)(story.contentToCount.codes['201'].response),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [content_swagger_1.FindContentParams]),
    __metadata("design:returntype", Promise)
], ContentController.prototype, "count", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)(story.contentToRead.operation),
    (0, swagger_1.ApiResponse)(story.contentToRead.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.contentToRead.codes['401'].response),
    (0, swagger_1.ApiResponse)(story.contentToRead.codes['404'].response),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ContentController.prototype, "read", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.contentToDelete.access),
    (0, swagger_1.ApiOperation)(story.contentToDelete.operation),
    (0, swagger_1.ApiResponse)(story.contentToDelete.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.contentToDelete.codes['404'].response),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ContentController.prototype, "delete", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.contentToUpdate.access),
    (0, swagger_1.ApiOperation)(story.contentToUpdate.operation),
    (0, swagger_1.ApiResponse)(story.contentToUpdate.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.contentToUpdate.codes['401'].response),
    (0, swagger_1.ApiResponse)(story.contentToUpdate.codes['404'].response),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, content_swagger_1.UpdateContentParams]),
    __metadata("design:returntype", Promise)
], ContentController.prototype, "update", null);
exports.ContentController = ContentController = __decorate([
    (0, common_1.Controller)('content'),
    (0, swagger_1.ApiTags)('content'),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [content_service_1.ContentService])
], ContentController);
//# sourceMappingURL=content.controller.js.map