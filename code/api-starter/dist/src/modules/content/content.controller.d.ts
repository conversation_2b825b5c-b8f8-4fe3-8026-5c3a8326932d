import { Prisma, Content, Admin } from '@prisma/client';
import { ContentService } from './content.service';
import { CreateContentParams, FindContentParams, UpdateContentParams } from './content.swagger';
export declare class ContentController {
    private readonly contentService;
    constructor(contentService: ContentService);
    create(admin: Admin, content: CreateContentParams): Promise<Content>;
    whereToPrisma(params: any): Prisma.ContentWhereInput;
    find(params: FindContentParams): Promise<Content[]>;
    count(params: FindContentParams): Promise<number>;
    read(id: string): Promise<any>;
    delete(id: string): Promise<any>;
    update(id: string, params: UpdateContentParams): Promise<Content>;
}
