"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AudioWorkflowController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const acl_decorator_1 = require("../../../acl/acl.decorator");
const acl_guard_1 = require("../../../acl/acl.guard");
const auth_guard_1 = require("../../auth/auth.guard");
const auth_decorator_1 = require("../../auth/auth.decorator");
const story = __importStar(require("../../../../specs/story/audio"));
const platform_express_1 = require("@nestjs/platform-express");
const file_service_1 = require("../../../shared/file/file.service");
const notification_service_1 = require("../../../shared/notification/notification.service");
const moment_1 = __importDefault(require("moment"));
const workflow_service_1 = require("../workflow.service");
const swagger_decorator_1 = require("../../../shared/swagger/swagger.decorator");
let AudioWorkflowController = class AudioWorkflowController {
    fileService;
    notificationService;
    workflowService;
    constructor(fileService, notificationService, workflowService) {
        this.fileService = fileService;
        this.notificationService = notificationService;
        this.workflowService = workflowService;
    }
    async transcribe(user, media, fileUrl) {
        const fileUploaded = await this.fileService.uploadMedia({
            folder: 'audios',
            file: media || fileUrl,
        });
        const workflow = await this.workflowService.create(user.id, user.id, 'transcribeAudio', { user, input: fileUploaded.url, fileUploaded }, [
            {
                name: 'audioWorkflowService.transcribeAudioFromInput',
                uiDescription: 'Transcribing audio',
                dependencies: [],
                next: [
                    {
                        name: 'audioWorkflowService.storeAudioFromTranscribeAudioInput',
                        uiDescription: 'Storing the transcription',
                        dependencies: ['audioWorkflowService.transcribeAudioFromInput'],
                        connect: ['audioWorkflowService.transcribeAudioFromInput'],
                    },
                ],
            },
        ], {
            ownerId: user.id,
            actionType: 'transcribeAudio',
            ownerType: 'audio',
            objectName: 'audio' + (0, moment_1.default)(),
        });
        return workflow.workflowId;
    }
};
exports.AudioWorkflowController = AudioWorkflowController;
__decorate([
    (0, common_1.Post)('/transcribe'),
    (0, swagger_decorator_1.ApiFile)('file'),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.audioToUploadAndTranscribe.access),
    (0, swagger_1.ApiOperation)(story.audioToUploadAndTranscribe.operation),
    (0, swagger_1.ApiResponse)(story.audioToUploadAndTranscribe.codes['201'].response),
    (0, swagger_1.ApiResponse)(story.audioToUploadAndTranscribe.codes['401'].response),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file', { dest: '/tmp/' })),
    (0, acl_decorator_1.AccessCreditNeeded)({ audio: 2 }),
    __param(0, (0, auth_decorator_1.ReqUser)()),
    __param(1, (0, common_1.UploadedFile)('file')),
    __param(2, (0, common_1.Body)('fileUrl')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, String]),
    __metadata("design:returntype", Promise)
], AudioWorkflowController.prototype, "transcribe", null);
exports.AudioWorkflowController = AudioWorkflowController = __decorate([
    (0, common_1.Controller)('workflow/audio'),
    (0, swagger_1.ApiTags)('workflow'),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [file_service_1.FileService,
        notification_service_1.NotificationService,
        workflow_service_1.WorkflowService])
], AudioWorkflowController);
//# sourceMappingURL=audio.workflow.controller.js.map