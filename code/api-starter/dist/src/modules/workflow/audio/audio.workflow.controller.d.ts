import { User } from '@prisma/client';
import { Upload } from 'src/shared/file/file.interface';
import { FileService } from 'src/shared/file/file.service';
import { NotificationService } from 'src/shared/notification/notification.service';
import { WorkflowService } from '../workflow.service';
export declare class AudioWorkflowController {
    fileService: FileService;
    notificationService: NotificationService;
    private workflowService;
    constructor(fileService: FileService, notificationService: NotificationService, workflowService: WorkflowService);
    transcribe(user: User, media: Upload, fileUrl?: string): Promise<string>;
}
