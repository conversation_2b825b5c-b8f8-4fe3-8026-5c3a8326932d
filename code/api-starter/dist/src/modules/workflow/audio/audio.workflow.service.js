"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AudioWorkflowService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../../shared/prisma/prisma.service");
const file_service_1 = require("../../../shared/file/file.service");
const ai_service_1 = require("../../../shared/ai/ai.service");
let AudioWorkflowService = class AudioWorkflowService {
    prisma;
    fileService;
    aiService;
    constructor(prisma, fileService, aiService) {
        this.prisma = prisma;
        this.fileService = fileService;
        this.aiService = aiService;
    }
    async create(data) {
        return await this.prisma.audio.create({ data });
    }
    async get(where) {
        return await this.prisma.audio.findUnique({
            where,
        });
    }
    async delete(where) {
        try {
            return await this.prisma.audio.delete({ where });
        }
        catch (e) {
            return false;
        }
    }
    async update(where, params) {
        return await this.prisma.audio.update({
            data: params,
            where,
        });
    }
    async findAll() {
        return await this.prisma.audio.findMany();
    }
    async find({ where, orderBy, skip = 0, take = 10, }) {
        return await this.prisma.audio.findMany({ where, orderBy, skip, take });
    }
    async count(where) {
        return await this.prisma.audio.count({ where });
    }
    async transcribeAudioFromInput(inputs, taskId, workflowId) {
        const response = await this.aiService.run({
            user: inputs.user,
            model: 'openai/whisper:8099696689d249cf8b122d833c36ac3f75505c666a395ca40ef26f68e7d3d16e',
            provider: 'replicate',
            inputs: { audio: inputs['input'] },
        });
        return { input: response['transcription'] };
    }
};
exports.AudioWorkflowService = AudioWorkflowService;
exports.AudioWorkflowService = AudioWorkflowService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        file_service_1.FileService,
        ai_service_1.AiService])
], AudioWorkflowService);
//# sourceMappingURL=audio.workflow.service.js.map