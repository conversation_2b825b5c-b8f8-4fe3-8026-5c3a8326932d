"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateWorkflowController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const acl_decorator_1 = require("../../../acl/acl.decorator");
const acl_guard_1 = require("../../../acl/acl.guard");
const auth_guard_1 = require("../../auth/auth.guard");
const auth_decorator_1 = require("../../auth/auth.decorator");
const story = __importStar(require("../../../../specs/story/create"));
const create_workflow_swagger_1 = require("./create.workflow.swagger");
const create_workflow_service_1 = require("./create.workflow.service");
const workflow_service_1 = require("../workflow.service");
let CreateWorkflowController = class CreateWorkflowController {
    createWorkflowService;
    workflowService;
    constructor(createWorkflowService, workflowService) {
        this.createWorkflowService = createWorkflowService;
        this.workflowService = workflowService;
    }
    async create(user, create) {
        const workflow = await this.workflowService.create(user.id, user.id, 'generateCreate', { user, input: create, prompt: create.undefined }, undefined, {
            ownerId: user.id,
            actionType: 'generateCreate',
            ownerType: 'create',
            objectName: 'create' + moment(),
        });
        if (NOTIFICATION_CONFIG["commentCreatePostWorkflowParams"]) {
            const userId = NOTIFICATION_CONFIG["commentCreatePostWorkflowParams"]?.target === 'owner'
                ? currentData.userId
                : user.id;
            await this.notificationService.sendAndSave([userId], Notifications.commentCreatePostWorkflowParams());
        }
        return workflow.workflowId;
    }
};
exports.CreateWorkflowController = CreateWorkflowController;
__decorate([
    (0, common_1.Post)('/create'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.createToCreate.access),
    (0, swagger_1.ApiOperation)(story.createToCreate.operation),
    (0, swagger_1.ApiResponse)(story.createToCreate.codes['201'].response),
    (0, swagger_1.ApiResponse)(story.createToCreate.codes['401'].response),
    __param(0, (0, auth_decorator_1.ReqUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_workflow_swagger_1.CreateCreateParams]),
    __metadata("design:returntype", Promise)
], CreateWorkflowController.prototype, "create", null);
exports.CreateWorkflowController = CreateWorkflowController = __decorate([
    (0, common_1.Controller)("workflow/create"),
    (0, swagger_1.ApiTags)("workflow"),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [typeof (_a = typeof create_workflow_service_1.CreateWorkflowService !== "undefined" && create_workflow_service_1.CreateWorkflowService) === "function" ? _a : Object, workflow_service_1.WorkflowService])
], CreateWorkflowController);
//# sourceMappingURL=create.workflow.controller.js.map