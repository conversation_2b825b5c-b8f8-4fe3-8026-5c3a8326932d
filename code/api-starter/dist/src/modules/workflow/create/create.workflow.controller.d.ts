import { User } from '@prisma/client';
import { CreateCreateParams } from './create.workflow.swagger';
import { CreateWorkflowService } from './create.workflow.service';
import { WorkflowService } from '../workflow.service';
export declare class CreateWorkflowController {
    private createWorkflowService;
    private workflowService;
    constructor(createWorkflowService: CreateWorkflowService, workflowService: WorkflowService);
    create(user: User, create: CreateCreateParams): Promise<string>;
}
