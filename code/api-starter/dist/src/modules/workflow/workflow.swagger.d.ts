import { OwnerType, WorkflowStatus } from '@prisma/client';
export declare class CreateWorkflowParams {
    videoId: string;
    language: string;
}
export declare class getTaskParams {
    taskId: string;
    resetNbOfExecution: boolean;
}
export declare class getTasksParams {
    workflowId: string;
}
export declare class RequestTask {
    taskName: string;
    podId: string;
}
export declare class UpdateTaskStatus {
    taskId: string;
    status: WorkflowStatus;
    result?: any;
    error?: any;
}
export declare class FindWorkflowParams {
    ownerType: OwnerType;
}
