"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PostWorkflowController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const acl_decorator_1 = require("../../../acl/acl.decorator");
const acl_guard_1 = require("../../../acl/acl.guard");
const auth_guard_1 = require("../../auth/auth.guard");
const auth_decorator_1 = require("../../auth/auth.decorator");
const story = __importStar(require("../../../../specs/story/post"));
const post_workflow_swagger_1 = require("./post.workflow.swagger");
const ai_service_1 = require("../../../shared/ai/ai.service");
const config_service_1 = require("../../../shared/config/config.service");
const file_service_1 = require("../../../shared/file/file.service");
const notification_service_1 = require("../../../shared/notification/notification.service");
const notifications_1 = __importDefault(require("../../../shared/notification/notifications"));
const notification_config_1 = require("../../../shared/config/notification.config");
const moment_1 = __importDefault(require("moment"));
const post_workflow_service_1 = require("./post.workflow.service");
const workflow_service_1 = require("../workflow.service");
let PostWorkflowController = class PostWorkflowController {
    aiService;
    config;
    fileService;
    notificationService;
    postWorkflowService;
    workflowService;
    constructor(aiService, config, fileService, notificationService, postWorkflowService, workflowService) {
        this.aiService = aiService;
        this.config = config;
        this.fileService = fileService;
        this.notificationService = notificationService;
        this.postWorkflowService = postWorkflowService;
        this.workflowService = workflowService;
    }
    async create(user, post) {
        const workflow = await this.workflowService.create(user.id, user.id, 'generatePost', { user, input: post, prompt: post.topic }, [{ "name": "postWorkflowService.generatePostFromPromptInput", "uiDescription": "Generating your blog post using AI...", "dependencies": [], "next": [{ "name": "postWorkflowService.storePostFromPostInput", "uiDescription": "Saving your generated post...", "dependencies": ["postWorkflowService.generatePostFromPromptInput"], "connect": ["postWorkflowService.generatePostFromPromptInput"] }] }], {
            ownerId: user.id,
            actionType: 'generatePost',
            ownerType: 'post',
            objectName: 'post' + (0, moment_1.default)(),
        });
        if (notification_config_1.NOTIFICATION_CONFIG["workflowPostCreatePostWorkflow"]) {
            const userId = notification_config_1.NOTIFICATION_CONFIG["workflowPostCreatePostWorkflow"]?.target === 'owner'
                ? workflow.ownerId
                : user.id;
            await this.notificationService.sendAndSave([userId], notifications_1.default.workflowPostCreatePostWorkflow());
        }
        return workflow.workflowId;
    }
    whereToPrisma(params) {
        let where = {};
        const OR = [];
        if (params.topic) {
            OR.push({
                topic: {
                    contains: params.topic,
                    mode: "insensitive",
                },
            });
        }
        if (OR.length > 0) {
            where.OR = OR;
        }
        return where;
    }
    async find(user, params) {
        const { take, skip, sortField = 'id', sortOrder = 'asc', ...where } = params;
        const currentData = await this.postWorkflowService.find({
            where: this.whereToPrisma(where),
            orderBy: { [sortField]: sortOrder },
            take,
            skip,
        });
        if (currentData && currentData.length > 0) {
        }
        return currentData;
    }
    async read(user, id) {
        const currentData = await this.postWorkflowService.get({ id });
        if (!currentData) {
            throw new common_1.NotFoundException();
        }
        return currentData;
    }
};
exports.PostWorkflowController = PostWorkflowController;
__decorate([
    (0, common_1.Post)('/create'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.postToCreate.access),
    (0, swagger_1.ApiOperation)(story.postToCreate.operation),
    (0, swagger_1.ApiResponse)(story.postToCreate.codes['201'].response),
    (0, swagger_1.ApiResponse)(story.postToCreate.codes['401'].response),
    (0, acl_decorator_1.AccessCreditNeeded)("{}"),
    __param(0, (0, auth_decorator_1.ReqUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, post_workflow_swagger_1.CreatePostParams]),
    __metadata("design:returntype", Promise)
], PostWorkflowController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('find'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.postToFind.access),
    (0, swagger_1.ApiOperation)(story.postToFind.operation),
    (0, swagger_1.ApiResponse)(story.postToFind.codes['201'].response),
    __param(0, (0, auth_decorator_1.ReqUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, post_workflow_swagger_1.FindPostParams]),
    __metadata("design:returntype", Promise)
], PostWorkflowController.prototype, "find", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.postToRead.access),
    (0, swagger_1.ApiOperation)(story.postToRead.operation),
    (0, swagger_1.ApiResponse)(story.postToRead.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.postToRead.codes['401'].response),
    (0, swagger_1.ApiResponse)(story.postToRead.codes['404'].response),
    __param(0, (0, auth_decorator_1.ReqUser)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], PostWorkflowController.prototype, "read", null);
exports.PostWorkflowController = PostWorkflowController = __decorate([
    (0, common_1.Controller)("workflow/post"),
    (0, swagger_1.ApiTags)("workflow"),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [ai_service_1.AiService,
        config_service_1.ConfigService,
        file_service_1.FileService,
        notification_service_1.NotificationService,
        post_workflow_service_1.PostWorkflowService,
        workflow_service_1.WorkflowService])
], PostWorkflowController);
//# sourceMappingURL=post.workflow.controller.js.map