import { <PERSON>, Prisma, User } from '@prisma/client';
import { CreatePostParams, FindPostParams } from './post.workflow.swagger';
import { AiService } from 'src/shared/ai/ai.service';
import { ConfigService } from 'src/shared/config/config.service';
import { FileService } from 'src/shared/file/file.service';
import { NotificationService } from 'src/shared/notification/notification.service';
import { PostWorkflowService } from './post.workflow.service';
import { WorkflowService } from '../workflow.service';
export declare class PostWorkflowController {
    private aiService;
    private config;
    fileService: FileService;
    notificationService: NotificationService;
    private postWorkflowService;
    private workflowService;
    constructor(aiService: AiService, config: ConfigService, fileService: FileService, notificationService: NotificationService, postWorkflowService: PostWorkflowService, workflowService: WorkflowService);
    create(user: User, post: CreatePostParams): Promise<string>;
    whereToPrisma(params: any): Prisma.PostWhereInput;
    find(user: User, params: FindPostParams): Promise<Post[]>;
    read(user: User, id: string): Promise<Post>;
}
