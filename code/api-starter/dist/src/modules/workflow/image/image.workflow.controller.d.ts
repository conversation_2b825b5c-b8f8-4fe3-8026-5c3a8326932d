import { <PERSON>, Prisma, User } from '@prisma/client';
import { Upload } from 'src/shared/file/file.interface';
import { CreateImageParams, FindImageParams } from './image.workflow.swagger';
import { AiService } from 'src/shared/ai/ai.service';
import { ConfigService } from 'src/shared/config/config.service';
import { FileService } from 'src/shared/file/file.service';
import { NotificationService } from 'src/shared/notification/notification.service';
import { ImageWorkflowService } from './image.workflow.service';
import { WorkflowService } from '../workflow.service';
export declare class ImageWorkflowController {
    private aiService;
    private config;
    fileService: FileService;
    notificationService: NotificationService;
    private imageWorkflowService;
    private workflowService;
    constructor(aiService: AiService, config: ConfigService, fileService: FileService, notificationService: NotificationService, imageWorkflowService: ImageWorkflowService, workflowService: WorkflowService);
    create(user: User, params: CreateImageParams, media: Upload): Promise<string>;
    whereToPrisma(params: any): Prisma.ImageWhereInput;
    find(user: User, params: FindImageParams): Promise<Image[]>;
    read(id: string): Promise<Image>;
}
