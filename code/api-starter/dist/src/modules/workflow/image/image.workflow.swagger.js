"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadImageParams = exports.FindImageParams = exports.CreateImageParams = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const client_1 = require("@prisma/client");
class CreateImageParams {
    file;
    topic;
}
exports.CreateImageParams = CreateImageParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Image file to edit',
        type: 'string',
        format: 'binary',
    }),
    __metadata("design:type", File)
], CreateImageParams.prototype, "file", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'A topic or prompt related to the resource.',
        example: 'mock.word',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateImageParams.prototype, "topic", void 0);
class FindImageParams {
    sortField;
    sortOrder;
    take;
    skip;
}
exports.FindImageParams = FindImageParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Order by field',
        enum: Object.keys(client_1.Prisma.ImageScalarFieldEnum),
    }),
    (0, class_validator_1.IsEnum)(Object.keys(client_1.Prisma.ImageScalarFieldEnum)),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], FindImageParams.prototype, "sortField", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Order sort',
        enum: client_1.Prisma.SortOrder,
    }),
    (0, class_validator_1.IsEnum)(client_1.Prisma.SortOrder),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], FindImageParams.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Number of results to return',
        example: 10,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], FindImageParams.prototype, "take", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Number of results to skip',
        example: 0,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], FindImageParams.prototype, "skip", void 0);
class ReadImageParams {
}
exports.ReadImageParams = ReadImageParams;
//# sourceMappingURL=image.workflow.swagger.js.map