"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ImageWorkflowController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const acl_decorator_1 = require("../../../acl/acl.decorator");
const acl_guard_1 = require("../../../acl/acl.guard");
const auth_guard_1 = require("../../auth/auth.guard");
const auth_decorator_1 = require("../../auth/auth.decorator");
const story = __importStar(require("../../../../specs/story/image"));
const platform_express_1 = require("@nestjs/platform-express");
const image_workflow_swagger_1 = require("./image.workflow.swagger");
const ai_service_1 = require("../../../shared/ai/ai.service");
const config_service_1 = require("../../../shared/config/config.service");
const fs_1 = require("fs");
const file_service_1 = require("../../../shared/file/file.service");
const notification_service_1 = require("../../../shared/notification/notification.service");
const moment_1 = __importDefault(require("moment"));
const image_workflow_service_1 = require("./image.workflow.service");
const workflow_service_1 = require("../workflow.service");
let ImageWorkflowController = class ImageWorkflowController {
    aiService;
    config;
    fileService;
    notificationService;
    imageWorkflowService;
    workflowService;
    constructor(aiService, config, fileService, notificationService, imageWorkflowService, workflowService) {
        this.aiService = aiService;
        this.config = config;
        this.fileService = fileService;
        this.notificationService = notificationService;
        this.imageWorkflowService = imageWorkflowService;
        this.workflowService = workflowService;
    }
    async create(user, params, media) {
        const fileContent = await fs_1.promises.readFile(media.path);
        const base64Image = fileContent.toString('base64');
        const mimeType = media.mimetype;
        const base64WithMimeType = `data:${mimeType};base64,${base64Image}`;
        const workflow = await this.workflowService.create(user.id, user.id, 'editImage', { user, input: base64WithMimeType, prompt: params.topic }, [
            {
                name: 'imageWorkflowService.editImageFromInput',
                uiDescription: 'Editing image',
                dependencies: [],
                next: [
                    {
                        name: 'imageWorkflowService.storeImageFromEditImageInput',
                        uiDescription: 'Storing the image',
                        dependencies: ['imageWorkflowService.editImageFromInput'],
                        connect: ['imageWorkflowService.editImageFromInput'],
                    },
                ],
            },
        ], {
            ownerId: user.id,
            actionType: 'editImage',
            ownerType: 'image',
            objectName: 'image' + (0, moment_1.default)(),
        });
        return workflow.workflowId;
    }
    whereToPrisma(params) {
        let where = {};
        const OR = [];
        if (params.file) {
            OR.push({
                file: {
                    contains: params.file,
                    mode: 'insensitive',
                },
            });
        }
        if (params.topic) {
            OR.push({
                topic: {
                    contains: params.topic,
                    mode: 'insensitive',
                },
            });
        }
        if (OR.length > 0) {
            where.OR = OR;
        }
        return where;
    }
    async find(user, params) {
        const { take, skip, sortField = 'id', sortOrder = 'asc', ...where } = params;
        const currentData = await this.imageWorkflowService.find({
            where: this.whereToPrisma(where),
            orderBy: { [sortField]: sortOrder },
            take,
            skip,
        });
        if (currentData && currentData.length > 0) {
        }
        return currentData;
    }
    async read(id) {
        const currentData = await this.imageWorkflowService.get({ id });
        if (!currentData) {
            throw new common_1.NotFoundException();
        }
        return currentData;
    }
};
exports.ImageWorkflowController = ImageWorkflowController;
__decorate([
    (0, common_1.Post)('/edit'),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.imageToEdit.access),
    (0, swagger_1.ApiOperation)(story.imageToEdit.operation),
    (0, swagger_1.ApiResponse)(story.imageToEdit.codes['201'].response),
    (0, swagger_1.ApiResponse)(story.imageToEdit.codes['401'].response),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file', { dest: '/tmp/' })),
    (0, acl_decorator_1.AccessCreditNeeded)({ image: 1 }),
    __param(0, (0, auth_decorator_1.ReqUser)()),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.UploadedFile)('file')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, image_workflow_swagger_1.CreateImageParams, Object]),
    __metadata("design:returntype", Promise)
], ImageWorkflowController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('find'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.imageToFind.access),
    (0, swagger_1.ApiOperation)(story.imageToFind.operation),
    (0, swagger_1.ApiResponse)(story.imageToFind.codes['201'].response),
    __param(0, (0, auth_decorator_1.ReqUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, image_workflow_swagger_1.FindImageParams]),
    __metadata("design:returntype", Promise)
], ImageWorkflowController.prototype, "find", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.imageToRead.access),
    (0, swagger_1.ApiOperation)(story.imageToRead.operation),
    (0, swagger_1.ApiResponse)(story.imageToRead.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.imageToRead.codes['401'].response),
    (0, swagger_1.ApiResponse)(story.imageToRead.codes['404'].response),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ImageWorkflowController.prototype, "read", null);
exports.ImageWorkflowController = ImageWorkflowController = __decorate([
    (0, common_1.Controller)('workflow/image'),
    (0, swagger_1.ApiTags)('workflow'),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [ai_service_1.AiService,
        config_service_1.ConfigService,
        file_service_1.FileService,
        notification_service_1.NotificationService,
        image_workflow_service_1.ImageWorkflowService,
        workflow_service_1.WorkflowService])
], ImageWorkflowController);
//# sourceMappingURL=image.workflow.controller.js.map