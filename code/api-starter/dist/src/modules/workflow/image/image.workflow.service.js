"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ImageWorkflowService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../../shared/prisma/prisma.service");
const file_service_1 = require("../../../shared/file/file.service");
const ai_service_1 = require("../../../shared/ai/ai.service");
const path_1 = __importDefault(require("path"));
const promises_1 = __importDefault(require("fs/promises"));
const media_1 = require("../../../shared/format/media");
let ImageWorkflowService = class ImageWorkflowService {
    prisma;
    aiService;
    fileService;
    constructor(prisma, aiService, fileService) {
        this.prisma = prisma;
        this.aiService = aiService;
        this.fileService = fileService;
    }
    async create(data) {
        return await this.prisma.image.create({
            data,
        });
    }
    async get(where) {
        return await this.prisma.image.findUnique({ where });
    }
    async delete(where) {
        try {
            return await this.prisma.image.delete({ where });
        }
        catch (e) {
            return false;
        }
    }
    async update(where, params) {
        return await this.prisma.image.update({ data: params, where });
    }
    async findAll() {
        return await this.prisma.image.findMany();
    }
    async find({ where, orderBy, skip = 0, take = 10, }) {
        return await this.prisma.image.findMany({ where, orderBy, skip, take });
    }
    async count(where) {
        return await this.prisma.image.count({ where });
    }
    async editImageFromInput(inputs, taskId, workflowId) {
        const response = await this.aiService.send({
            user: inputs.user,
            type: 'image',
            model: 'gemini-2.0-flash-exp',
            provider: 'google',
            messages: [
                { role: 'user', content: inputs.prompt, image: inputs['input'] },
            ],
        });
        return { input: response['image'] };
    }
    async storeImageFromEditImageInput(inputs, taskId, workflowId) {
        const fileFolder = path_1.default.join('/tmp', 'images', taskId);
        await promises_1.default.mkdir(fileFolder, { recursive: true });
        const filePath = await (0, media_1.downloadMedia)(inputs['imageWorkflowService.editImageFromInput'].input, fileFolder);
        const file = (0, media_1.getFileMetadata)(filePath, 'image');
        const fileUploaded = await this.fileService.uploadMedia({
            folder: 'images',
            file,
            width: 800,
            height: 400,
        });
        const output = await this.create({
            topic: inputs?.prompt ?? '',
            userId: inputs.user.id,
            publicId: fileUploaded.json.public_id,
            url: fileUploaded.url,
            thumbnailUrl: fileUploaded.json.eager[0].url,
            json: fileUploaded.json,
            type: 'image',
        });
        return { input: output };
    }
};
exports.ImageWorkflowService = ImageWorkflowService;
exports.ImageWorkflowService = ImageWorkflowService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        ai_service_1.AiService,
        file_service_1.FileService])
], ImageWorkflowService);
//# sourceMappingURL=image.workflow.service.js.map