"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkerService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../shared/prisma/prisma.service");
const client_1 = require("@prisma/client");
const config_service_1 = require("../../shared/config/config.service");
const ai_service_1 = require("../../shared/ai/ai.service");
const bugsnag_service_1 = require("../../shared/bugsnag/bugsnag.service");
const image_workflow_service_1 = require("./image/image.workflow.service");
const audio_workflow_service_1 = require("./audio/audio.workflow.service");
const postWorkflowService_service_1 = require("./postWorkflow/postWorkflowService.service");
let WorkerService = class WorkerService {
    prisma;
    config;
    bugsnag;
    aiService;
    imageWorkflowService;
    audioWorkflowService;
    postWorkflowService;
    constructor(prisma, config, bugsnag, aiService, imageWorkflowService, audioWorkflowService, postWorkflowService) {
        this.prisma = prisma;
        this.config = config;
        this.bugsnag = bugsnag;
        this.aiService = aiService;
        this.imageWorkflowService = imageWorkflowService;
        this.audioWorkflowService = audioWorkflowService;
        this.postWorkflowService = postWorkflowService;
    }
    async exec(taskName, workflow) {
        console.log('exec', taskName);
        let task = await workflow.requestTask({
            taskName,
            podId: process.env.POD_NAME || taskName,
        });
        if (!task) {
            console.log('No task found');
            return false;
        }
        try {
            await workflow.updateTaskStatus(task.id, client_1.WorkflowStatus.IN_PROGRESS);
            let result;
            if (await this[taskName]) {
                result = await this[taskName](task.inputs, task.id, task.workflowId);
            }
            else if (taskName.indexOf('.') > 0) {
                let [serviceName, methodName] = taskName.split('.');
                result = await this[serviceName][methodName](task.inputs, task.id, task.workflowId);
            }
            await workflow.updateTaskStatus(task.id, client_1.WorkflowStatus.COMPLETED, result);
        }
        catch (e) {
            e.context = { task, taskName };
            console.error(e);
            this.bugsnag.client.notify(e);
            if (task.nbOfExecution > 2) {
                await this.prisma.task.update({
                    where: { id: task.id },
                    data: {
                        status: client_1.WorkflowStatus.ERROR,
                        nbOfExecution: {
                            increment: 1,
                        },
                    },
                });
                await workflow.updateTaskStatus(task.id, client_1.WorkflowStatus.ERROR, null, {
                    message: e.message,
                    stack: e.stack,
                });
            }
            else {
                await this.prisma.task.update({
                    where: { id: task.id },
                    data: {
                        status: client_1.WorkflowStatus.PENDING,
                        nbOfExecution: {
                            increment: 1,
                        },
                    },
                });
            }
            await workflow.updateTaskStatus(task.id, client_1.WorkflowStatus.ERROR, null, e.message);
        }
    }
    async start(inputs) {
        return { result: 'start result' };
    }
    async edit(inputs) {
        return { result: 'edit result' };
    }
    async end(inputs) {
        return { result: 'end result' };
    }
};
exports.WorkerService = WorkerService;
exports.WorkerService = WorkerService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        config_service_1.ConfigService,
        bugsnag_service_1.BugsnagService,
        ai_service_1.AiService,
        image_workflow_service_1.ImageWorkflowService,
        audio_workflow_service_1.AudioWorkflowService, typeof (_a = typeof postWorkflowService_service_1.PostWorkflowService !== "undefined" && postWorkflowService_service_1.PostWorkflowService) === "function" ? _a : Object])
], WorkerService);
//# sourceMappingURL=worker.service.js.map