import { PrismaService } from 'src/shared/prisma/prisma.service';
import { WorkflowService } from './workflow.service';
import { ConfigService } from 'src/shared/config/config.service';
import { AiService } from 'src/shared/ai/ai.service';
import { BugsnagService } from 'src/shared/bugsnag/bugsnag.service';
import { ImageWorkflowService } from './image/image.workflow.service';
import { AudioWorkflowService } from './audio/audio.workflow.service';
import { PostWorkflowService } from './postWorkflow/postWorkflowService.service';
export declare class WorkerService {
    prisma: PrismaService;
    config: ConfigService;
    bugsnag: BugsnagService;
    private aiService;
    private imageWorkflowService;
    private audioWorkflowService;
    private postWorkflowService;
    constructor(prisma: PrismaService, config: ConfigService, bugsnag: BugsnagService, aiService: AiService, imageWorkflowService: ImageWorkflowService, audioWorkflowService: AudioWorkflowService, postWorkflowService: PostWorkflowService);
    exec(taskName: any, workflow: WorkflowService): Promise<boolean>;
    start(inputs: any): Promise<{
        result: string;
    }>;
    edit(inputs: any): Promise<{
        result: string;
    }>;
    end(inputs: any): Promise<{
        result: string;
    }>;
}
