"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const workflow_service_1 = require("./workflow.service");
const workflow_swagger_1 = require("./workflow.swagger");
const story = __importStar(require("../../../specs/story/workflow"));
const acl_decorator_1 = require("../../acl/acl.decorator");
const auth_guard_1 = require("../auth/auth.guard");
const acl_guard_1 = require("../../acl/acl.guard");
const auth_decorator_1 = require("../auth/auth.decorator");
let WorkflowController = class WorkflowController {
    workflowService;
    constructor(workflowService) {
        this.workflowService = workflowService;
    }
    async get(id) {
        return await this.workflowService.get({ id });
    }
    async find(user, params) {
        return await this.workflowService.find({ ownerId: user.id, ...params });
    }
    async requestTask(body) {
        return await this.workflowService.requestTask(body);
    }
    async getTask({ taskId }) {
        return await this.workflowService.getTask(taskId);
    }
    async getTasks({ workflowId }) {
        return await this.workflowService.getTasks(workflowId);
    }
    async resetTask(user, { taskId, resetNbOfExecution }) {
        return await this.workflowService.resetTask(taskId, resetNbOfExecution);
    }
    async resetTaskImage(user, { taskId, resetNbOfExecution }) {
        return await this.workflowService.resetTask(taskId, resetNbOfExecution);
    }
    async updateTaskStatus({ taskId, status, result, error }) {
        return await this.workflowService.updateTaskStatus(taskId, status, result, error);
    }
};
exports.WorkflowController = WorkflowController;
__decorate([
    (0, common_1.Get)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.workflowToRead.access),
    (0, swagger_1.ApiOperation)(story.workflowToRead.operation),
    (0, swagger_1.ApiResponse)(story.workflowToRead.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.workflowToRead.codes['404'].response),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], WorkflowController.prototype, "get", null);
__decorate([
    (0, common_1.Post)('find'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.workflowToFind.access),
    (0, swagger_1.ApiOperation)(story.workflowToFind.operation),
    (0, swagger_1.ApiResponse)(story.workflowToFind.codes['201'].response),
    __param(0, (0, auth_decorator_1.ReqUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, workflow_swagger_1.FindWorkflowParams]),
    __metadata("design:returntype", Promise)
], WorkflowController.prototype, "find", null);
__decorate([
    (0, common_1.Post)('task/request'),
    (0, swagger_1.ApiOperation)({ summary: 'Request a new task' }),
    (0, swagger_1.ApiResponse)(story.workflowToCreate.codes['201'].response),
    (0, swagger_1.ApiResponse)(story.workflowToCreate.codes['401'].response),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [workflow_swagger_1.RequestTask]),
    __metadata("design:returntype", Promise)
], WorkflowController.prototype, "requestTask", null);
__decorate([
    (0, common_1.Get)('task'),
    (0, swagger_1.ApiOperation)({ summary: 'get a task' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [workflow_swagger_1.getTaskParams]),
    __metadata("design:returntype", Promise)
], WorkflowController.prototype, "getTask", null);
__decorate([
    (0, common_1.Get)('tasks'),
    (0, swagger_1.ApiOperation)({ summary: 'get a workflow tasks' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [workflow_swagger_1.getTasksParams]),
    __metadata("design:returntype", Promise)
], WorkflowController.prototype, "getTasks", null);
__decorate([
    (0, common_1.Post)('task/reset'),
    (0, swagger_1.ApiOperation)({ summary: 'Reset a task' }),
    (0, acl_decorator_1.AccessCreditNeeded)({ text: 1 }),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)({
        resource: 'item',
        action: 'create',
    }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Task assigned' }),
    __param(0, (0, auth_decorator_1.ReqUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, workflow_swagger_1.getTaskParams]),
    __metadata("design:returntype", Promise)
], WorkflowController.prototype, "resetTask", null);
__decorate([
    (0, common_1.Post)('task/reset/image'),
    (0, swagger_1.ApiOperation)({ summary: 'Reset a task' }),
    (0, acl_decorator_1.AccessCreditNeeded)({ image: 1, text: 1 }),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Task assigned' }),
    (0, acl_decorator_1.AccessTo)({
        resource: 'item',
        action: 'create',
    }),
    __param(0, (0, auth_decorator_1.ReqUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, workflow_swagger_1.getTaskParams]),
    __metadata("design:returntype", Promise)
], WorkflowController.prototype, "resetTaskImage", null);
__decorate([
    (0, common_1.Patch)('task/status'),
    (0, swagger_1.ApiOperation)({ summary: 'Update task status' }),
    (0, swagger_1.ApiResponse)(story.workflowToCreate.codes['201'].response),
    (0, swagger_1.ApiResponse)(story.workflowToCreate.codes['401'].response),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Status updated' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [workflow_swagger_1.UpdateTaskStatus]),
    __metadata("design:returntype", Promise)
], WorkflowController.prototype, "updateTaskStatus", null);
exports.WorkflowController = WorkflowController = __decorate([
    (0, common_1.Controller)('workflow'),
    (0, swagger_1.ApiTags)('workflow'),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [workflow_service_1.WorkflowService])
], WorkflowController);
//# sourceMappingURL=workflow.controller.js.map