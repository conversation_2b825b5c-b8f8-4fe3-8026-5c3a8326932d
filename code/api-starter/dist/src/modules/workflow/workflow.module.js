"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowModule = void 0;
const common_1 = require("@nestjs/common");
const workflow_controller_1 = require("./workflow.controller");
const workflow_service_1 = require("./workflow.service");
const ai_service_1 = require("../../shared/ai/ai.service");
const worker_service_1 = require("./worker.service");
const file_service_1 = require("../../shared/file/file.service");
const notification_service_1 = require("../../shared/notification/notification.service");
const image_workflow_service_1 = require("./image/image.workflow.service");
const image_workflow_controller_1 = require("./image/image.workflow.controller");
const audio_workflow_service_1 = require("./audio/audio.workflow.service");
const audio_workflow_controller_1 = require("./audio/audio.workflow.controller");
const post_workflow_controller_1 = require("./post/post.workflow.controller");
const post_workflow_service_1 = require("./post/post.workflow.service");
let WorkflowModule = class WorkflowModule {
};
exports.WorkflowModule = WorkflowModule;
exports.WorkflowModule = WorkflowModule = __decorate([
    (0, common_1.Module)({
        imports: [],
        controllers: [
            workflow_controller_1.WorkflowController,
            image_workflow_controller_1.ImageWorkflowController,
            audio_workflow_controller_1.AudioWorkflowController,
            post_workflow_controller_1.PostWorkflowController
        ],
        providers: [
            workflow_service_1.WorkflowService,
            worker_service_1.WorkerService,
            ai_service_1.AiService,
            file_service_1.FileService,
            workflow_controller_1.WorkflowController,
            notification_service_1.NotificationService,
            image_workflow_service_1.ImageWorkflowService,
            audio_workflow_service_1.AudioWorkflowService,
            post_workflow_service_1.PostWorkflowService, post_workflow_controller_1.PostWorkflowController
        ],
        exports: [workflow_controller_1.WorkflowController, post_workflow_controller_1.PostWorkflowController],
    })
], WorkflowModule);
//# sourceMappingURL=workflow.module.js.map