"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.generated@76369 = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const client_1 = require("@prisma/client");
const _id_workflow_service_1 = require("./:id.workflow.service");
const workflow_service_1 = require("../workflow.service");
let default_1 = class {
};
default_1 = __decorate([
    (0, common_1.Controller)("workflow/:id"),
    (0, swagger_1.ApiTags)("workflow"),
    (0, swagger_1.ApiBearerAuth)()
], default_1);
idWorkflowController;
{
    constructor(private, _id_workflow_service_1.idWorkflowService, _id_workflow_service_1.idWorkflowService, private, workflowService, workflow_service_1.WorkflowService);
    { }
    read(, user, client_1.User, , client_1.id, string);
    Promise < ;
    client_1.id > {
        const: currentData = await this., idWorkflowService: _id_workflow_service_1.idWorkflowService, : .get({ id: client_1.id }),
        if(, currentData) {
            throw new common_1.NotFoundException();
        },
        return: currentData
    };
}
//# sourceMappingURL=:id.workflow.controller.js.map