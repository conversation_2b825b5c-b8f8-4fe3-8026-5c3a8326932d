"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FindWorkflowParams = exports.UpdateTaskStatus = exports.RequestTask = exports.getTasksParams = exports.getTaskParams = exports.CreateWorkflowParams = void 0;
const swagger_1 = require("@nestjs/swagger");
const client_1 = require("@prisma/client");
const class_validator_1 = require("class-validator");
class CreateWorkflowParams {
    videoId;
    language;
}
exports.CreateWorkflowParams = CreateWorkflowParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Video to translate',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateWorkflowParams.prototype, "videoId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Language',
        example: {},
    }),
    (0, class_validator_1.IsObject)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateWorkflowParams.prototype, "language", void 0);
class getTaskParams {
    taskId;
    resetNbOfExecution;
}
exports.getTaskParams = getTaskParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'taskId',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], getTaskParams.prototype, "taskId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'resetNbOfExecution',
    }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], getTaskParams.prototype, "resetNbOfExecution", void 0);
class getTasksParams {
    workflowId;
}
exports.getTasksParams = getTasksParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'workflowId',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], getTasksParams.prototype, "workflowId", void 0);
class RequestTask {
    taskName;
    podId;
}
exports.RequestTask = RequestTask;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Worker Name',
        example: 'WorkerName',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], RequestTask.prototype, "taskName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Pod Id',
        example: 'pod-id',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], RequestTask.prototype, "podId", void 0);
class UpdateTaskStatus {
    taskId;
    status;
    result;
    error;
}
exports.UpdateTaskStatus = UpdateTaskStatus;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Task ID',
        example: 'task-id',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], UpdateTaskStatus.prototype, "taskId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Task Status',
        example: 'COMPLETED',
        enum: Object.keys(client_1.WorkflowStatus),
    }),
    (0, class_validator_1.IsEnum)(Object.keys(client_1.WorkflowStatus)),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], UpdateTaskStatus.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Task Result',
        example: 'result',
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], UpdateTaskStatus.prototype, "result", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Task Error',
        example: 'error',
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], UpdateTaskStatus.prototype, "error", void 0);
class FindWorkflowParams {
    ownerType;
}
exports.FindWorkflowParams = FindWorkflowParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Project id',
        example: 'seed.workflowToRead.result.id',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FindWorkflowParams.prototype, "ownerType", void 0);
//# sourceMappingURL=workflow.swagger.js.map