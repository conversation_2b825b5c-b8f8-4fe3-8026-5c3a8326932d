import { WorkflowService } from './workflow.service';
import { FindWorkflowParams, getTaskParams, getTasksParams, RequestTask, UpdateTaskStatus } from './workflow.swagger';
import { Workflow, Task, User } from '@prisma/client';
export declare class WorkflowController {
    private readonly workflowService;
    constructor(workflowService: WorkflowService);
    get(id: string): Promise<Workflow>;
    find(user: User, params: FindWorkflowParams): Promise<any>;
    requestTask(body: RequestTask): Promise<Task | null>;
    getTask({ taskId }: getTaskParams): Promise<Task>;
    getTasks({ workflowId }: getTasksParams): Promise<Task[]>;
    resetTask(user: User, { taskId, resetNbOfExecution }: getTaskParams): Promise<Task>;
    resetTaskImage(user: User, { taskId, resetNbOfExecution }: getTaskParams): Promise<Task>;
    updateTaskStatus({ taskId, status, result, error }: UpdateTaskStatus): Promise<Task>;
}
