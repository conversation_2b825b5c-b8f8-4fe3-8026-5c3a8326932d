"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FieldController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const field_swagger_1 = require("./field.swagger");
const prisma = __importStar(require("@prisma/client"));
const prisma_service_1 = require("../../shared/prisma/prisma.service");
const field_service_1 = require("./field.service");
const story = __importStar(require("../../../specs/story/field"));
const auth_guard_1 = require("../auth/auth.guard");
const acl_guard_1 = require("../../acl/acl.guard");
const acl_decorator_1 = require("../../acl/acl.decorator");
let FieldController = class FieldController {
    fieldService;
    primaService;
    constructor(fieldService, primaService) {
        this.fieldService = fieldService;
        this.primaService = primaService;
    }
    async static({ key }) {
        if (prisma[key]) {
            return Object.keys(prisma[key]);
        }
        return [];
    }
    async dynamic({ key }) {
        return await this.fieldService.list({ key });
    }
    async createDynamic(params) {
        return await this.fieldService.create(params);
    }
    async delete(id) {
        const exemple = await this.fieldService.delete({ id });
        if (!exemple) {
            throw new common_1.NotFoundException();
        }
        return { success: true };
    }
};
exports.FieldController = FieldController;
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Field Field values' }),
    (0, common_1.Get)('static'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [field_swagger_1.FieldEnumParams]),
    __metadata("design:returntype", Promise)
], FieldController.prototype, "static", null);
__decorate([
    (0, common_1.Get)('dynamic'),
    (0, swagger_1.ApiOperation)(story.dynamicFieldToRead.operation),
    (0, swagger_1.ApiResponse)(story.dynamicFieldToRead.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.dynamicFieldToRead.codes['401'].response),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [field_swagger_1.FieldOptionsParams]),
    __metadata("design:returntype", Promise)
], FieldController.prototype, "dynamic", null);
__decorate([
    (0, common_1.Post)('dynamic'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.dynamicFieldToCreate.access),
    (0, swagger_1.ApiOperation)(story.dynamicFieldToCreate.operation),
    (0, swagger_1.ApiResponse)(story.dynamicFieldToCreate.codes['201'].response),
    (0, swagger_1.ApiResponse)(story.dynamicFieldToCreate.codes['401'].response),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [field_swagger_1.FieldCreateOptionsParams]),
    __metadata("design:returntype", Promise)
], FieldController.prototype, "createDynamic", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.dynamicFieldToDelete.access),
    (0, swagger_1.ApiOperation)(story.dynamicFieldToDelete.operation),
    (0, swagger_1.ApiResponse)(story.dynamicFieldToDelete.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.dynamicFieldToDelete.codes['404'].response),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FieldController.prototype, "delete", null);
exports.FieldController = FieldController = __decorate([
    (0, swagger_1.ApiTags)('field'),
    (0, common_1.Controller)('field'),
    __metadata("design:paramtypes", [field_service_1.FieldService,
        prisma_service_1.PrismaService])
], FieldController);
//# sourceMappingURL=field.controller.js.map