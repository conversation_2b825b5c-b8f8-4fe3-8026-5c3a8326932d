import { Field, Prisma } from '@prisma/client';
import { PrismaService } from 'src/shared/prisma/prisma.service';
export declare class FieldService {
    prisma: PrismaService;
    constructor(prisma: PrismaService);
    create(data: Prisma.FieldCreateInput): Promise<Field>;
    get(where: Prisma.FieldWhereUniqueInput): Promise<Field>;
    delete(where: Prisma.FieldWhereUniqueInput): Promise<any>;
    update(where: Prisma.FieldWhereUniqueInput, params: Prisma.FieldUpdateInput): Promise<Field>;
    list(where: Prisma.FieldWhereInput): Promise<Field[]>;
    find({ where, orderBy, skip, take, }: Prisma.FieldFindManyArgs): Promise<Field[]>;
    exists(where: Prisma.FieldWhereInput): Promise<boolean>;
    count(where: Prisma.FieldWhereInput): Promise<number>;
    getBy(where: Prisma.FieldWhereInput): Promise<Field>;
}
