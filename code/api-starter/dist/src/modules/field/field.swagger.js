"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FieldOptionsDeleteParams = exports.FieldOptionsParams = exports.FieldCreateOptionsParams = exports.FieldEnumParams = void 0;
const prisma = __importStar(require("@prisma/client"));
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const enums = Object.keys(prisma).filter(key => {
    if (typeof prisma[key] === 'object' &&
        key.indexOf('ScalarFieldEnum') === -1) {
        let keys = Object.keys(prisma[key]);
        if (keys[0] === prisma[key][keys[0]]) {
            return true;
        }
    }
    return false;
});
class FieldEnumParams {
    key;
}
exports.FieldEnumParams = FieldEnumParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'enum',
        enum: enums,
    }),
    (0, class_validator_1.IsEnum)(enums),
    __metadata("design:type", String)
], FieldEnumParams.prototype, "key", void 0);
class FieldCreateOptionsParams {
    key;
    value;
    name;
}
exports.FieldCreateOptionsParams = FieldCreateOptionsParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: "Option's key",
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FieldCreateOptionsParams.prototype, "key", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: "Option's value",
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", Object)
], FieldCreateOptionsParams.prototype, "value", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: "Option's displayed name",
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FieldCreateOptionsParams.prototype, "name", void 0);
class FieldOptionsParams {
    key;
}
exports.FieldOptionsParams = FieldOptionsParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: "Option's key",
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FieldOptionsParams.prototype, "key", void 0);
class FieldOptionsDeleteParams {
    id;
}
exports.FieldOptionsDeleteParams = FieldOptionsDeleteParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: "Option's id",
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FieldOptionsDeleteParams.prototype, "id", void 0);
//# sourceMappingURL=field.swagger.js.map