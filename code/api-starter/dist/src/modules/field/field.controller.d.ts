import { FieldCreateOptionsParams, FieldEnumParams, FieldOptionsParams } from 'src/modules/field/field.swagger';
import { Field } from '@prisma/client';
import { PrismaService } from 'src/shared/prisma/prisma.service';
import { FieldService } from './field.service';
export declare class FieldController {
    private readonly fieldService;
    private primaService;
    constructor(fieldService: FieldService, primaService: PrismaService);
    static({ key }: FieldEnumParams): Promise<string[]>;
    dynamic({ key }: FieldOptionsParams): Promise<Field[]>;
    createDynamic(params: FieldCreateOptionsParams): Promise<Field>;
    delete(id: string): Promise<any>;
}
