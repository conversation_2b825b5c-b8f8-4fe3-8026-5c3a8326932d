import { Highlight } from '@prisma/client';
import { HighlightService } from './highlight.service';
import { CreateHighlightParams, UpdateHighlightParams } from './highlight.swagger';
import { NewService } from '../news/news.service';
export declare class HighlightController {
    private readonly highlightService;
    private readonly newService;
    constructor(highlightService: HighlightService, newService: NewService);
    create(highlight: CreateHighlightParams): Promise<Highlight>;
    find(): Promise<any>;
    read(location: string): Promise<any>;
    delete(id: string): Promise<any>;
    update(id: string, params: UpdateHighlightParams): Promise<Highlight>;
}
