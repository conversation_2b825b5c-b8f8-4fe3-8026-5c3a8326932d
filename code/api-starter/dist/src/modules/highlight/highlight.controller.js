"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HighlightController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const acl_decorator_1 = require("../../acl/acl.decorator");
const acl_guard_1 = require("../../acl/acl.guard");
const auth_guard_1 = require("../auth/auth.guard");
const highlight_service_1 = require("./highlight.service");
const highlight_swagger_1 = require("./highlight.swagger");
const story = __importStar(require("../../../specs/story/highlight"));
const news_service_1 = require("../news/news.service");
let HighlightController = class HighlightController {
    highlightService;
    newService;
    constructor(highlightService, newService) {
        this.highlightService = highlightService;
        this.newService = newService;
    }
    async create(highlight) {
        return await this.highlightService.create(highlight);
    }
    async find() { }
    async read(location) {
        const sections = await this.highlightService.list({ location });
        let data = {};
        await Promise.all(sections.map(async (section) => {
            const where = { id: section.value.id };
            switch (section.id) {
                case 'news':
                    data[section.id] = await this.newService.get(where);
                    break;
                default:
                    data[section.id] = section.value;
                    break;
            }
        }));
        return data;
    }
    async delete(id) {
        let highlight = false;
        try {
            highlight = await this.highlightService.delete({ id });
        }
        catch (e) {
            console.warn(e);
        }
        if (!highlight) {
            throw new common_1.NotFoundException();
        }
        return { success: true };
    }
    async update(id, params) {
        let highlight = false;
        try {
            highlight = await this.highlightService.update({ id, ...params });
        }
        catch (e) {
            console.warn(e);
        }
        if (!highlight) {
            throw new common_1.NotFoundException();
        }
        return highlight;
    }
};
exports.HighlightController = HighlightController;
__decorate([
    (0, common_1.Post)(''),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.highlightToCreate.access),
    (0, swagger_1.ApiOperation)(story.highlightToCreate.operation),
    (0, swagger_1.ApiResponse)(story.highlightToCreate.codes['201'].response),
    (0, swagger_1.ApiResponse)(story.highlightToCreate.codes['401'].response),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [highlight_swagger_1.CreateHighlightParams]),
    __metadata("design:returntype", Promise)
], HighlightController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(''),
    (0, swagger_1.ApiOperation)(story.highlightToFind.operation),
    (0, swagger_1.ApiResponse)(story.highlightToFind.codes['200'].response),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], HighlightController.prototype, "find", null);
__decorate([
    (0, common_1.Get)(':location'),
    (0, swagger_1.ApiOperation)(story.highlightToRead.operation),
    (0, swagger_1.ApiResponse)(story.highlightToRead.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.highlightToRead.codes['401'].response),
    (0, swagger_1.ApiResponse)(story.highlightToRead.codes['404'].response),
    __param(0, (0, common_1.Param)('location')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], HighlightController.prototype, "read", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.highlightToDelete.access),
    (0, swagger_1.ApiOperation)(story.highlightToDelete.operation),
    (0, swagger_1.ApiResponse)(story.highlightToDelete.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.highlightToDelete.codes['403'].response),
    (0, swagger_1.ApiResponse)(story.highlightToDelete.codes['404'].response),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], HighlightController.prototype, "delete", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.highlightToUpdate.access),
    (0, swagger_1.ApiOperation)(story.highlightToUpdate.operation),
    (0, swagger_1.ApiResponse)(story.highlightToUpdate.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.highlightToUpdate.codes['401'].response),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, highlight_swagger_1.UpdateHighlightParams]),
    __metadata("design:returntype", Promise)
], HighlightController.prototype, "update", null);
exports.HighlightController = HighlightController = __decorate([
    (0, common_1.Controller)('highlight'),
    (0, swagger_1.ApiTags)('highlight'),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [highlight_service_1.HighlightService,
        news_service_1.NewService])
], HighlightController);
//# sourceMappingURL=highlight.controller.js.map