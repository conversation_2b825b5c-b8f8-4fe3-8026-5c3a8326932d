"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HighlightService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../shared/prisma/prisma.service");
let HighlightService = class HighlightService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(data) {
        return await this.prisma.highlight.create({ data });
    }
    async get(where) {
        return await this.prisma.highlight.findUnique({
            where,
        });
    }
    async delete(where) {
        try {
            return await this.prisma.highlight.delete({ where });
        }
        catch (e) {
            return false;
        }
    }
    async update({ id, location, value, }) {
        return await this.prisma.highlight.upsert({
            create: { id, location, value },
            update: { location, value },
            where: { id },
        });
    }
    async list(where) {
        return await this.prisma.highlight.findMany({
            where,
        });
    }
    async find({ where, orderBy, skip = 0, take = 10, }) {
        return await this.prisma.highlight.findMany({
            where,
            orderBy,
            skip,
            take,
        });
    }
    async exists(where) {
        return (await this.prisma.highlight.count({ where })) > 0 ? true : false;
    }
    async count(where) {
        return await this.prisma.highlight.count({
            where,
        });
    }
    async getBy(where) {
        const data = await this.prisma.highlight.findFirst({
            where,
        });
        if (!data)
            throw new common_1.NotFoundException();
        return data;
    }
};
exports.HighlightService = HighlightService;
exports.HighlightService = HighlightService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], HighlightService);
//# sourceMappingURL=highlight.service.js.map