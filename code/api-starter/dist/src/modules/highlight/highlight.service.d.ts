import { Highlight, Prisma } from '@prisma/client';
import { PrismaService } from 'src/shared/prisma/prisma.service';
export declare class HighlightService {
    prisma: PrismaService;
    constructor(prisma: PrismaService);
    create(data: Prisma.HighlightCreateInput): Promise<Highlight>;
    get(where: Prisma.HighlightWhereUniqueInput): Promise<Highlight>;
    delete(where: Prisma.HighlightWhereUniqueInput): Promise<any>;
    update({ id, location, value, }: Prisma.HighlightCreateInput): Promise<Highlight>;
    list(where: Prisma.HighlightWhereInput): Promise<Highlight[]>;
    find({ where, orderBy, skip, take, }: Prisma.HighlightFindManyArgs): Promise<Highlight[]>;
    exists(where: Prisma.HighlightWhereInput): Promise<boolean>;
    count(where: Prisma.HighlightWhereInput): Promise<number>;
    getBy(where: Prisma.HighlightWhereInput): Promise<Highlight>;
}
