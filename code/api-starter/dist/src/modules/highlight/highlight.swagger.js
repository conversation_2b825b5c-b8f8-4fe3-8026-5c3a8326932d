"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateHighlightParams = exports.CreateHighlightParams = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateHighlightParams {
    id;
    location;
    value;
}
exports.CreateHighlightParams = CreateHighlightParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Highlight section Id',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateHighlightParams.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Highlight location',
    }),
    __metadata("design:type", String)
], CreateHighlightParams.prototype, "location", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Highlight value',
    }),
    __metadata("design:type", Object)
], CreateHighlightParams.prototype, "value", void 0);
class UpdateHighlightParams {
    value;
    location;
}
exports.UpdateHighlightParams = UpdateHighlightParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Highlight section value',
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], UpdateHighlightParams.prototype, "value", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Highlight location',
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateHighlightParams.prototype, "location", void 0);
//# sourceMappingURL=highlight.swagger.js.map