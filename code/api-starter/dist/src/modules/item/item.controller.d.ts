import { Item } from '@prisma/client';
import { CreateItemParams } from 'src/modules/item/item.swagger';
import { ItemService } from './item.service';
export declare class ItemController {
    private readonly itemService;
    constructor(itemService: ItemService);
    create(ownerType: string, ownerId: string, createParams: CreateItemParams): Promise<Item>;
    get(ownerType: string, ownerId: string, itemType: string): Promise<Item>;
    remove(id: string): Promise<boolean>;
}
