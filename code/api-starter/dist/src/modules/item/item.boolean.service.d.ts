import { PrismaService } from 'src/shared/prisma/prisma.service';
import { Prisma, ItemBoolean } from '@prisma/client';
export declare class ItemBooleanService {
    private prisma;
    constructor(prisma: PrismaService);
    create(data: Prisma.ItemBooleanCreateInput): Promise<ItemBoolean>;
    get(id: string): Promise<ItemBoolean>;
    update(id: string, data: Prisma.ItemBooleanUpdateInput): Promise<ItemBoolean>;
    remove(id: string): Promise<boolean>;
}
