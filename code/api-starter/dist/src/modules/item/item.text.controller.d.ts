import { Prisma, ItemText } from '@prisma/client';
import { UpdateItemTextBody } from './item.swagger';
import { ItemTextService } from './item.text.service';
export declare class ItemTextController {
    private readonly itemTextService;
    constructor(itemTextService: ItemTextService);
    create(data: Prisma.ItemTextCreateInput): Promise<ItemText>;
    get(id: string): Promise<ItemText>;
    update(id: string, updateItemTextBody: UpdateItemTextBody): Promise<ItemText>;
    remove(id: string): Promise<boolean>;
}
