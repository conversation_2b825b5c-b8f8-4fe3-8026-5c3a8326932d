import { PrismaService } from 'src/shared/prisma/prisma.service';
import { Prisma, ItemText } from '@prisma/client';
export declare class ItemTextService {
    private prisma;
    constructor(prisma: PrismaService);
    create(data: Prisma.ItemTextCreateInput): Promise<ItemText>;
    get(id: string): Promise<ItemText>;
    update(id: string, data: Prisma.ItemTextUpdateInput): Promise<ItemText>;
    remove(id: string): Promise<boolean>;
}
