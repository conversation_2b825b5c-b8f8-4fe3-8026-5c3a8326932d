"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ItemBooleanService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../shared/prisma/prisma.service");
let ItemBooleanService = class ItemBooleanService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(data) {
        return await this.prisma.itemBoolean.create({
            data,
        });
    }
    async get(id) {
        const itemBoolean = await this.prisma.itemBoolean.findUnique({
            where: {
                id,
            },
        });
        if (!itemBoolean) {
            throw new common_1.NotFoundException('ItemBoolean not found');
        }
        return itemBoolean;
    }
    async update(id, data) {
        return await this.prisma.itemBoolean.update({
            where: {
                id,
            },
            data,
        });
    }
    async remove(id) {
        try {
            await this.prisma.itemBoolean.delete({
                where: {
                    id,
                },
            });
            return true;
        }
        catch (error) {
            throw new common_1.NotFoundException('ItemBoolean not found');
        }
    }
};
exports.ItemBooleanService = ItemBooleanService;
exports.ItemBooleanService = ItemBooleanService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], ItemBooleanService);
//# sourceMappingURL=item.boolean.service.js.map