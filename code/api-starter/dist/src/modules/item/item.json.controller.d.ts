import { <PERSON>risma, Item<PERSON><PERSON> } from '@prisma/client';
import { UpdateItemJsonBody } from './item.swagger';
import { ItemJsonService } from './item.json.service';
export declare class ItemJsonController {
    private readonly itemJsonService;
    constructor(itemJsonService: ItemJsonService);
    create(data: Prisma.ItemJsonCreateInput): Promise<ItemJson>;
    get(id: string): Promise<ItemJson>;
    update(id: string, updateItemJsonBody: UpdateItemJsonBody): Promise<ItemJson>;
    remove(id: string): Promise<boolean>;
}
