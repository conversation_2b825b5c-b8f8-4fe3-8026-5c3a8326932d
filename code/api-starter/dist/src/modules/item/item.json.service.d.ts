import { PrismaService } from 'src/shared/prisma/prisma.service';
import { Prisma, ItemJson } from '@prisma/client';
export declare class ItemJsonService {
    private prisma;
    constructor(prisma: PrismaService);
    create(data: Prisma.ItemJsonCreateInput): Promise<ItemJson>;
    get(id: string): Promise<ItemJson>;
    update(id: string, data: Prisma.ItemJsonUpdateInput): Promise<ItemJson>;
    remove(id: string): Promise<boolean>;
}
