"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ItemController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const acl_decorator_1 = require("../../acl/acl.decorator");
const acl_guard_1 = require("../../acl/acl.guard");
const auth_guard_1 = require("../auth/auth.guard");
const item_swagger_1 = require("./item.swagger");
const story = __importStar(require("../../../specs/story/item"));
const item_service_1 = require("./item.service");
let ItemController = class ItemController {
    itemService;
    constructor(itemService) {
        this.itemService = itemService;
    }
    async create(ownerType, ownerId, createParams) {
        return await this.itemService.create({
            itemType: 'item',
            ownerId,
            ownerType,
            ...createParams,
        });
    }
    async get(ownerType, ownerId, itemType) {
        return await this.itemService.get({
            ownerId,
            ownerType,
            itemType,
        });
    }
    async remove(id) {
        return !!(await this.itemService.delete({
            id,
        }));
    }
};
exports.ItemController = ItemController;
__decorate([
    (0, common_1.Post)(':ownerId/:ownerType'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.itemToCreate.access),
    (0, swagger_1.ApiOperation)(story.itemToCreate.operation),
    (0, swagger_1.ApiResponse)(story.itemToCreate.codes['201'].response),
    (0, swagger_1.ApiResponse)(story.itemToCreate.codes['401'].response),
    __param(0, (0, common_1.Param)('ownerType')),
    __param(1, (0, common_1.Param)('ownerId')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, item_swagger_1.CreateItemParams]),
    __metadata("design:returntype", Promise)
], ItemController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(':ownerType/:ownerId/:itemType'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.itemToRead.access),
    (0, swagger_1.ApiOperation)(story.itemToRead.operation),
    (0, swagger_1.ApiResponse)(story.itemToRead.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.itemToRead.codes['404'].response),
    __param(0, (0, common_1.Param)('ownerType')),
    __param(1, (0, common_1.Param)('ownerId')),
    __param(2, (0, common_1.Param)('itemType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], ItemController.prototype, "get", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.itemToDelete.access),
    (0, swagger_1.ApiOperation)(story.itemToDelete.operation),
    (0, swagger_1.ApiResponse)(story.itemToDelete.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.itemToDelete.codes['403'].response),
    (0, swagger_1.ApiResponse)(story.itemToDelete.codes['404'].response),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ItemController.prototype, "remove", null);
exports.ItemController = ItemController = __decorate([
    (0, common_1.Controller)('item'),
    (0, swagger_1.ApiTags)('item'),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [item_service_1.ItemService])
], ItemController);
//# sourceMappingURL=item.controller.js.map