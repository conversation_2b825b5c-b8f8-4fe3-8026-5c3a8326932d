import { Prisma, ItemBoolean } from '@prisma/client';
import { ItemBooleanService } from './item.boolean.service';
export declare class ItemBooleanController {
    private readonly itemBooleanService;
    constructor(itemBooleanService: ItemBooleanService);
    create(data: Prisma.ItemBooleanCreateInput): Promise<ItemBoolean>;
    get(id: string): Promise<ItemBoolean>;
    update(id: string, updateItemBooleanBody: Prisma.ItemBooleanUpdateInput): Promise<ItemBoolean>;
    remove(id: string): Promise<boolean>;
}
