"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ItemService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../shared/prisma/prisma.service");
let ItemService = class ItemService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(data) {
        const itemInfos = data;
        return await this.prisma.item.create({
            data: {
                ...data,
                jsons: {
                    create: itemInfos.jsons?.map(itemJson => ({
                        ...itemJson,
                    })) || [],
                },
                texts: {
                    create: itemInfos.texts?.map(itemText => ({
                        ...itemText,
                    })) || [],
                },
                booleans: {
                    create: itemInfos.booleans?.map(itemBoolean => ({
                        ...itemBoolean,
                    })) || [],
                },
                fields: {
                    create: itemInfos.fields?.map(itemfield => ({
                        ...itemfield,
                    })) || [],
                },
            },
        });
    }
    async get(where) {
        const result = await this.prisma.item.findFirst({
            where,
            include: {
                texts: true,
                jsons: true,
            },
        });
        console.log({ result });
        return result;
    }
    async find(where) {
        return await this.prisma.item.findMany({
            where,
        });
    }
    async delete(where) {
        return await this.prisma.item.delete({
            where,
        });
    }
    async update(where, data) {
        return await this.prisma.item.update({
            where,
            data,
        });
    }
};
exports.ItemService = ItemService;
exports.ItemService = ItemService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], ItemService);
//# sourceMappingURL=item.service.js.map