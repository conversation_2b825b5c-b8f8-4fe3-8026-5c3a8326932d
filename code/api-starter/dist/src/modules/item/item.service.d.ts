import { PrismaService } from 'src/shared/prisma/prisma.service';
import { Prisma, Item } from '@prisma/client';
export declare class ItemService {
    private prisma;
    constructor(prisma: PrismaService);
    create(data: Prisma.ItemCreateInput): Promise<Item>;
    get(where: Prisma.ItemWhereInput): Promise<Item>;
    find(where: Prisma.ItemWhereInput): Promise<Item[]>;
    delete(where: Prisma.ItemWhereUniqueInput): Promise<Item>;
    update(where: Prisma.ItemWhereUniqueInput, data: Prisma.ItemUpdateInput): Promise<Item>;
}
