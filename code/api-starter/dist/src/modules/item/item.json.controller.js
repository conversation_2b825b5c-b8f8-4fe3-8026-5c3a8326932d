"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ItemJsonController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const acl_decorator_1 = require("../../acl/acl.decorator");
const acl_guard_1 = require("../../acl/acl.guard");
const client_1 = require("@prisma/client");
const auth_guard_1 = require("../auth/auth.guard");
const item_swagger_1 = require("./item.swagger");
const itemJson_1 = require("../../../specs/story/itemJson");
const item_json_service_1 = require("./item.json.service");
let ItemJsonController = class ItemJsonController {
    itemJsonService;
    constructor(itemJsonService) {
        this.itemJsonService = itemJsonService;
    }
    async create(data) {
        return await this.itemJsonService.create(data);
    }
    async get(id) {
        return await this.itemJsonService.get(id);
    }
    async update(id, updateItemJsonBody) {
        return await this.itemJsonService.update(id, updateItemJsonBody);
    }
    async remove(id) {
        return await this.itemJsonService.remove(id);
    }
};
exports.ItemJsonController = ItemJsonController;
__decorate([
    (0, common_1.Post)(''),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(itemJson_1.itemJsonToCreate.access),
    (0, swagger_1.ApiOperation)(itemJson_1.itemJsonToCreate.operation),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: itemJson_1.itemJsonToCreate.codes['201'].response.description,
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: itemJson_1.itemJsonToCreate.codes['401'].response.description,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ItemJsonController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(itemJson_1.itemJsonToRead.access),
    (0, swagger_1.ApiOperation)(itemJson_1.itemJsonToRead.operation),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: itemJson_1.itemJsonToRead.codes['200'].response.description,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: itemJson_1.itemJsonToRead.codes['404'].response.description,
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ItemJsonController.prototype, "get", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(itemJson_1.itemJsonToUpdate.access),
    (0, swagger_1.ApiOperation)(itemJson_1.itemJsonToUpdate.operation),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: itemJson_1.itemJsonToUpdate.codes['200'].response.description,
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: itemJson_1.itemJsonToUpdate.codes['401'].response.description,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: itemJson_1.itemJsonToUpdate.codes['404'].response.description,
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, item_swagger_1.UpdateItemJsonBody]),
    __metadata("design:returntype", Promise)
], ItemJsonController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(itemJson_1.itemJsonToDelete.access),
    (0, swagger_1.ApiOperation)(itemJson_1.itemJsonToDelete.operation),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: itemJson_1.itemJsonToDelete.codes['200'].response.description,
        schema: { type: 'boolean' },
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: itemJson_1.itemJsonToDelete.codes['403'].response.description,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: itemJson_1.itemJsonToDelete.codes['404'].response.description,
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ItemJsonController.prototype, "remove", null);
exports.ItemJsonController = ItemJsonController = __decorate([
    (0, common_1.Controller)('sectio/json'),
    (0, swagger_1.ApiTags)('item-json'),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [item_json_service_1.ItemJsonService])
], ItemJsonController);
//# sourceMappingURL=item.json.controller.js.map