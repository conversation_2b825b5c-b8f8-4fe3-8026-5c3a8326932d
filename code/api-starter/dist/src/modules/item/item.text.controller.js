"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ItemTextController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const acl_decorator_1 = require("../../acl/acl.decorator");
const acl_guard_1 = require("../../acl/acl.guard");
const client_1 = require("@prisma/client");
const auth_guard_1 = require("../auth/auth.guard");
const item_swagger_1 = require("./item.swagger");
const story = __importStar(require("../../../specs/story/itemText"));
const item_text_service_1 = require("./item.text.service");
let ItemTextController = class ItemTextController {
    itemTextService;
    constructor(itemTextService) {
        this.itemTextService = itemTextService;
    }
    async create(data) {
        return await this.itemTextService.create(data);
    }
    async get(id) {
        return await this.itemTextService.get(id);
    }
    async update(id, updateItemTextBody) {
        const existingItemText = await this.itemTextService.get(id);
        if (!existingItemText) {
            throw new common_1.NotFoundException("ItemText doesn't exist");
        }
        return await this.itemTextService.update(id, updateItemTextBody);
    }
    async remove(id) {
        return await this.itemTextService.remove(id);
    }
};
exports.ItemTextController = ItemTextController;
__decorate([
    (0, common_1.Post)(''),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.itemTextToCreate.access),
    (0, swagger_1.ApiOperation)(story.itemTextToCreate.operation),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: story.itemTextToCreate.codes['201'].response.description,
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: story.itemTextToCreate.codes['401'].response.description,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ItemTextController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.itemTextToRead.access),
    (0, swagger_1.ApiOperation)(story.itemTextToRead.operation),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: story.itemTextToRead.codes['200'].response.description,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: story.itemTextToRead.codes['404'].response.description,
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ItemTextController.prototype, "get", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.itemTextToUpdate.access),
    (0, swagger_1.ApiOperation)(story.itemTextToUpdate.operation),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: story.itemTextToUpdate.codes['200'].response.description,
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: story.itemTextToUpdate.codes['403'].response.description,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: story.itemTextToUpdate.codes['404'].response.description,
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, item_swagger_1.UpdateItemTextBody]),
    __metadata("design:returntype", Promise)
], ItemTextController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.itemTextToDelete.access),
    (0, swagger_1.ApiOperation)(story.itemTextToDelete.operation),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: story.itemTextToDelete.codes['200'].response.description,
        schema: { type: 'boolean' },
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: story.itemTextToDelete.codes['403'].response.description,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: story.itemTextToDelete.codes['404'].response.description,
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ItemTextController.prototype, "remove", null);
exports.ItemTextController = ItemTextController = __decorate([
    (0, common_1.Controller)('item/text'),
    (0, swagger_1.ApiTags)('item-texts'),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [item_text_service_1.ItemTextService])
], ItemTextController);
//# sourceMappingURL=item.text.controller.js.map