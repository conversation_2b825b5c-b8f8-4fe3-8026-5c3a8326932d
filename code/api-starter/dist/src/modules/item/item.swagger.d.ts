export declare class CreateItemParams {
    itemType: string;
    jsons?: any;
}
export declare class UpdateItemParams {
    name?: string;
    summary?: string;
    order?: string[];
}
export declare class GenerateItemParams {
    name: string;
    description: string;
}
export declare class CreateItemBooleanBody {
    id: string;
    value: boolean;
}
export declare class UpdateItemBooleanBody {
    value?: boolean;
}
export declare class CreateItemTextBody {
    name: string;
    description?: string;
    title?: string;
    value: string;
    itemId: string;
    itemNumber?: number;
    fileId?: string;
    parentItemId?: string;
    parentType?: string;
    parentId?: string;
    parentNumber?: number;
}
export declare class UpdateItemTextBody {
    text: string;
}
export declare class CreateItemJsonBody {
    name: string;
    json: Record<string, any>;
}
export declare class UpdateItemJsonBody {
    json?: Record<string, any>;
}
