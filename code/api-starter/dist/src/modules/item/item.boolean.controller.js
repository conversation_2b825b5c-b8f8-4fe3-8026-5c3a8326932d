"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ItemBooleanController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const acl_decorator_1 = require("../../acl/acl.decorator");
const acl_guard_1 = require("../../acl/acl.guard");
const client_1 = require("@prisma/client");
const auth_guard_1 = require("../auth/auth.guard");
const itemBoolean_1 = require("../../../specs/story/itemBoolean");
const item_boolean_service_1 = require("./item.boolean.service");
let ItemBooleanController = class ItemBooleanController {
    itemBooleanService;
    constructor(itemBooleanService) {
        this.itemBooleanService = itemBooleanService;
    }
    async create(data) {
        return await this.itemBooleanService.create(data);
    }
    async get(id) {
        return await this.itemBooleanService.get(id);
    }
    async update(id, updateItemBooleanBody) {
        return await this.itemBooleanService.update(id, updateItemBooleanBody);
    }
    async remove(id) {
        return await this.itemBooleanService.remove(id);
    }
};
exports.ItemBooleanController = ItemBooleanController;
__decorate([
    (0, common_1.Post)(''),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(itemBoolean_1.itemBooleanToCreate.access),
    (0, swagger_1.ApiOperation)(itemBoolean_1.itemBooleanToCreate.operation),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: itemBoolean_1.itemBooleanToCreate.codes['201'].response.description,
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: itemBoolean_1.itemBooleanToCreate.codes['401'].response.description,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ItemBooleanController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(itemBoolean_1.itemBooleanToRead.access),
    (0, swagger_1.ApiOperation)(itemBoolean_1.itemBooleanToRead.operation),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: itemBoolean_1.itemBooleanToRead.codes['200'].response.description,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: itemBoolean_1.itemBooleanToRead.codes['404'].response.description,
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ItemBooleanController.prototype, "get", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(itemBoolean_1.itemBooleanToUpdate.access),
    (0, swagger_1.ApiOperation)(itemBoolean_1.itemBooleanToUpdate.operation),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: itemBoolean_1.itemBooleanToUpdate.codes['200'].response.description,
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: itemBoolean_1.itemBooleanToUpdate.codes['401'].response.description,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: itemBoolean_1.itemBooleanToUpdate.codes['404'].response.description,
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ItemBooleanController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(itemBoolean_1.itemBooleanToDelete.access),
    (0, swagger_1.ApiOperation)(itemBoolean_1.itemBooleanToDelete.operation),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: itemBoolean_1.itemBooleanToDelete.codes['200'].response.description,
        schema: { type: 'boolean' },
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: itemBoolean_1.itemBooleanToDelete.codes['403'].response.description,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: itemBoolean_1.itemBooleanToDelete.codes['404'].response.description,
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ItemBooleanController.prototype, "remove", null);
exports.ItemBooleanController = ItemBooleanController = __decorate([
    (0, common_1.Controller)('item/boolean'),
    (0, swagger_1.ApiTags)('item-texts'),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [item_boolean_service_1.ItemBooleanService])
], ItemBooleanController);
//# sourceMappingURL=item.boolean.controller.js.map