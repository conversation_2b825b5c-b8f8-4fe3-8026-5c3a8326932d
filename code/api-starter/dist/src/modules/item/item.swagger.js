"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateItemJsonBody = exports.CreateItemJsonBody = exports.UpdateItemTextBody = exports.CreateItemTextBody = exports.UpdateItemBooleanBody = exports.CreateItemBooleanBody = exports.GenerateItemParams = exports.UpdateItemParams = exports.CreateItemParams = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateItemParams {
    itemType;
    jsons;
}
exports.CreateItemParams = CreateItemParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Item Type',
        example: 'Sample Item',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateItemParams.prototype, "itemType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'List of itemJson to create',
        example: '',
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], CreateItemParams.prototype, "jsons", void 0);
class UpdateItemParams {
    name;
    summary;
    order;
}
exports.UpdateItemParams = UpdateItemParams;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Nom des données.',
        example: 'Updated Item Name',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateItemParams.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Résumé des données.',
        example: 'Updated summary of the item',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateItemParams.prototype, "summary", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Nouvel ordre des items.',
        example: ['item1', 'item3'],
        type: [String],
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], UpdateItemParams.prototype, "order", void 0);
class GenerateItemParams {
    name;
    description;
}
exports.GenerateItemParams = GenerateItemParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Le nom des données à générer.',
        example: 'mock.name',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], GenerateItemParams.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'La description des données à générer.',
        example: 'mock.description',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], GenerateItemParams.prototype, "description", void 0);
class CreateItemBooleanBody {
    id;
    value;
}
exports.CreateItemBooleanBody = CreateItemBooleanBody;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Le nom du ItemBoolean.',
        example: 'isActve',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateItemBooleanBody.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'La valeur booléenne.',
        example: true,
    }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Boolean)
], CreateItemBooleanBody.prototype, "value", void 0);
class UpdateItemBooleanBody {
    value;
}
exports.UpdateItemBooleanBody = UpdateItemBooleanBody;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'La valeur booléenne.',
        example: false,
    }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], UpdateItemBooleanBody.prototype, "value", void 0);
class CreateItemTextBody {
    name;
    description;
    title;
    value;
    itemId;
    itemNumber;
    fileId;
    parentItemId;
    parentType;
    parentId;
    parentNumber;
}
exports.CreateItemTextBody = CreateItemTextBody;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Le nom du ItemText.',
        example: 'description',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateItemTextBody.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'La description du ItemText.',
        example: 'This is a detailed description.',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateItemTextBody.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Le titre du ItemText.',
        example: 'Introduction',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateItemTextBody.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'La valeur du ItemText.',
        example: 'Detailed content goes here.',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateItemTextBody.prototype, "value", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Identifiant du Item parent.',
        example: 'item-1234',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateItemTextBody.prototype, "itemId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Numéro de item.',
        example: 1,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateItemTextBody.prototype, "itemNumber", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Identifiant du fichier associé.',
        example: 'file-5678',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateItemTextBody.prototype, "fileId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Identifiant de la item parent.',
        example: 'item-91011',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateItemTextBody.prototype, "parentItemId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Type de parent.',
        example: 'item',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateItemTextBody.prototype, "parentType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Identifiant du parent.',
        example: 'item-1234',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateItemTextBody.prototype, "parentId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Numéro du parent.',
        example: 2,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateItemTextBody.prototype, "parentNumber", void 0);
class UpdateItemTextBody {
    text;
}
exports.UpdateItemTextBody = UpdateItemTextBody;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Modifier le contenu du ItemText',
        example: 'mock.description',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateItemTextBody.prototype, "text", void 0);
class CreateItemJsonBody {
    name;
    json;
}
exports.CreateItemJsonBody = CreateItemJsonBody;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Le nom du ItemJson.',
        example: 'metaitem',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateItemJsonBody.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'La valeur JSON du ItemJson.',
        example: { key: 'value' },
    }),
    (0, class_validator_1.IsJSON)(),
    __metadata("design:type", Object)
], CreateItemJsonBody.prototype, "json", void 0);
class UpdateItemJsonBody {
    json;
}
exports.UpdateItemJsonBody = UpdateItemJsonBody;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'La valeur JSON du ItemJson.',
        example: { key: 'newValue' },
    }),
    (0, class_validator_1.IsObject)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], UpdateItemJsonBody.prototype, "json", void 0);
//# sourceMappingURL=item.swagger.js.map