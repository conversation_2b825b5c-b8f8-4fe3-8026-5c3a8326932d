import { ChatService } from './chat.service';
import { Prisma, User } from '@prisma/client';
import { CreateConversationParams, FindConversationsParams, UpdateConversationParams } from './chat.swagger';
export declare class ChatController {
    private readonly chatService;
    constructor(chatService: ChatService);
    whereToPrisma(params: any): Prisma.ChatConversationWhereInput;
    create(user: User, params: CreateConversationParams): Promise<{
        prompt: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        title: string | null;
        status: import(".prisma/client").$Enums.ChatConversationStatus;
        agentId: string;
    }>;
    find(params: FindConversationsParams): Promise<{
        prompt: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        title: string | null;
        status: import(".prisma/client").$Enums.ChatConversationStatus;
        agentId: string;
    }[]>;
    get(conversationId: string): Promise<{
        prompt: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        title: string | null;
        status: import(".prisma/client").$Enums.ChatConversationStatus;
        agentId: string;
    }>;
    update(conversationId: string, params: UpdateConversationParams): Promise<{
        prompt: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        title: string | null;
        status: import(".prisma/client").$Enums.ChatConversationStatus;
        agentId: string;
    }>;
    delete(id: string): Promise<any>;
    clear(id: string): Promise<any>;
}
