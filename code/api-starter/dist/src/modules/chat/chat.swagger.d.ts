import { ChatConversationStatus, ChatM<PERSON>ageRole, ChatMessageType, Prisma } from '@prisma/client';
export declare class CreateConversationParams {
    prompt: string;
    title?: string;
    agentId: string;
}
export declare class CreateChatMessageParams {
    content: string;
    conversationId: string;
    role: ChatMessageRole;
    messageType?: ChatMessageType;
    metadata?: Record<string, any>;
}
export declare class SendMessageParams {
    content: string;
    conversationId: string;
    messageType?: ChatMessageType;
}
export declare class FindConversationsParams {
    status?: ChatConversationStatus;
    sortField: string;
    sortOrder: Prisma.SortOrder;
    take: number;
    skip: number;
}
export declare class UpdateConversationParams {
    status: ChatConversationStatus;
}
export declare class CreateAgentParams {
    systemPrompt: string;
}
export declare class FindMessagesParams {
    userId?: string;
    conversationId?: string;
    sortField: string;
    sortOrder: Prisma.SortOrder;
    take: number;
    skip: number;
}
export declare class FindAgentParams {
    name?: string;
    description?: string;
    systemPrompt?: string;
    sortField: string;
    sortOrder: Prisma.SortOrder;
    take: number;
    skip: number;
}
export declare class CreateMessageParams {
    prompt: string;
    title?: string;
    agentId: string;
}
