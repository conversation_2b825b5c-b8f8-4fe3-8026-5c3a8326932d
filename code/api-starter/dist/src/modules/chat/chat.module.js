"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatModule = void 0;
const common_1 = require("@nestjs/common");
const chat_controller_1 = require("./chat.controller");
const chat_service_1 = require("./chat.service");
const ai_service_1 = require("../../shared/ai/ai.service");
const chatMessage_controller_1 = require("./chatMessage.controller");
const agent_controller_1 = require("./agent.controller");
const agent_service_1 = require("./agent.service");
const openai_realtime_controller_1 = require("./openai-realtime.controller");
let ChatModule = class ChatModule {
};
exports.ChatModule = ChatModule;
exports.ChatModule = ChatModule = __decorate([
    (0, common_1.Module)({
        imports: [],
        controllers: [
            chat_controller_1.ChatController,
            chatMessage_controller_1.ChatMessageController,
            agent_controller_1.AgentController,
            openai_realtime_controller_1.OpenAIRealtimeController,
        ],
        providers: [
            chat_service_1.ChatService,
            chat_controller_1.ChatController,
            chatMessage_controller_1.ChatMessageController,
            agent_service_1.AgentService,
            ai_service_1.AiService,
            agent_controller_1.AgentController,
            openai_realtime_controller_1.OpenAIRealtimeController,
        ],
        exports: [
            chat_service_1.ChatService,
            chat_controller_1.ChatController,
            chatMessage_controller_1.ChatMessageController,
            agent_controller_1.AgentController,
            agent_service_1.AgentService,
            openai_realtime_controller_1.OpenAIRealtimeController,
        ],
    })
], ChatModule);
//# sourceMappingURL=chat.module.js.map