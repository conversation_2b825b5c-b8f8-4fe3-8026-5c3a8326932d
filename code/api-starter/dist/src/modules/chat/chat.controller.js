"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const chat_service_1 = require("./chat.service");
const auth_guard_1 = require("../auth/auth.guard");
const acl_guard_1 = require("../../acl/acl.guard");
const acl_decorator_1 = require("../../acl/acl.decorator");
const auth_decorator_1 = require("../auth/auth.decorator");
const chat_swagger_1 = require("./chat.swagger");
const story = __importStar(require("../../../specs/story/chat"));
let ChatController = class ChatController {
    chatService;
    constructor(chatService) {
        this.chatService = chatService;
    }
    whereToPrisma(params) {
        let where = {};
        if (params.title) {
            where.title = {
                contains: params.title,
                mode: 'insensitive',
            };
        }
        if (params.systemPrompt) {
            where.prompt = {
                contains: params.prompt,
                mode: 'insensitive',
            };
        }
        if (params.agentId) {
            where.agentId = params.agentId;
        }
        return where;
    }
    async create(user, params) {
        const data = {
            user: { connect: { id: user.id } },
            prompt: params.prompt,
            title: params.title,
            agent: { connect: { id: params.agentId } },
        };
        return await this.chatService.create(data);
    }
    async find(params) {
        const { take, skip, sortField = 'id', sortOrder = 'asc', ...where } = params;
        return await this.chatService.find({
            where: this.whereToPrisma(where),
            take,
            skip,
        });
    }
    async get(conversationId) {
        return await this.chatService.get({ id: conversationId });
    }
    async update(conversationId, params) {
        return await this.chatService.update({ id: conversationId }, params);
    }
    async delete(id) {
        const chat = await this.chatService.delete({ id });
        if (!chat) {
            throw new common_1.NotFoundException();
        }
        return { success: true };
    }
    async clear(id) {
        const clearedChat = await this.chatService.clearMessages(id);
        if (!clearedChat) {
            throw new common_1.NotFoundException();
        }
        return { success: true };
    }
};
exports.ChatController = ChatController;
__decorate([
    (0, common_1.Post)('create'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.chatToCreate.access),
    (0, swagger_1.ApiOperation)(story.chatToCreate.operation),
    (0, swagger_1.ApiResponse)(story.chatToCreate.codes['201'].response),
    (0, swagger_1.ApiResponse)(story.chatToCreate.codes['401'].response),
    (0, acl_decorator_1.AccessCreditNeeded)({
        text: 1,
    }),
    __param(0, (0, auth_decorator_1.ReqUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, chat_swagger_1.CreateConversationParams]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('find'),
    (0, acl_decorator_1.AccessTo)(story.chatToFind.access),
    (0, swagger_1.ApiOperation)(story.chatToFind.operation),
    (0, swagger_1.ApiResponse)(story.chatToFind.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.chatToRead.codes['401'].response),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [chat_swagger_1.FindConversationsParams]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "find", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, acl_decorator_1.AccessTo)(story.chatToRead.access),
    (0, swagger_1.ApiOperation)(story.chatToRead.operation),
    (0, swagger_1.ApiResponse)(story.chatToRead.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.chatToRead.codes['401'].response),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "get", null);
__decorate([
    (0, common_1.Patch)('update/:id'),
    (0, acl_decorator_1.AccessTo)(story.chatToUpdate.access),
    (0, swagger_1.ApiOperation)(story.chatToUpdate.operation),
    (0, swagger_1.ApiResponse)(story.chatToUpdate.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.chatToUpdate.codes['401'].response),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, chat_swagger_1.UpdateConversationParams]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)('delete/:id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.chatToDelete.access),
    (0, swagger_1.ApiOperation)(story.chatToDelete.operation),
    (0, swagger_1.ApiResponse)(story.chatToDelete.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.chatToDelete.codes['404'].response),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "delete", null);
__decorate([
    (0, common_1.Delete)('/clear/:id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.chatToClear.access),
    (0, swagger_1.ApiOperation)(story.chatToClear.operation),
    (0, swagger_1.ApiResponse)(story.chatToClear.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.chatToClear.codes['404'].response),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "clear", null);
exports.ChatController = ChatController = __decorate([
    (0, common_1.Controller)('chat/chatConversation'),
    (0, swagger_1.ApiTags)('chat/chatConversation'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    __metadata("design:paramtypes", [chat_service_1.ChatService])
], ChatController);
//# sourceMappingURL=chat.controller.js.map