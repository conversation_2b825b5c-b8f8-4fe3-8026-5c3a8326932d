import { PrismaService } from '../../shared/prisma/prisma.service';
import { AiService } from '../../shared/ai/ai.service';
import { Agent, Prisma } from '@prisma/client';
export declare class AgentService {
    private prisma;
    private aiService;
    constructor(prisma: PrismaService, aiService: AiService);
    find({ where, orderBy, skip, take, }: Prisma.AgentFindManyArgs): Promise<Agent[]>;
    findAll(): Promise<Agent[]>;
    get(where: Prisma.AgentWhereUniqueInput): Promise<Agent | null>;
    create(data: Prisma.AgentCreateInput): Promise<Agent>;
    update(where: Prisma.AgentWhereUniqueInput, data: Prisma.AgentUpdateInput): Promise<Agent>;
    delete(where: Prisma.AgentWhereUniqueInput): Promise<boolean>;
}
