import { ChatService } from './chat.service';
import { ChatMessage, Prisma, User } from '@prisma/client';
import { SendMessageParams, FindMessagesParams, CreateChatMessageParams } from './chat.swagger';
export declare class ChatMessageController {
    private readonly chatService;
    constructor(chatService: ChatService);
    whereToPrisma(params: Record<string, any>): Prisma.ChatMessageWhereInput;
    getConversations(params: FindMessagesParams): Promise<{
        id: string;
        role: import(".prisma/client").$Enums.ChatMessageRole;
        createdAt: Date;
        updatedAt: Date;
        content: string;
        userId: string;
        metadata: Prisma.JsonValue | null;
        messageType: import(".prisma/client").$Enums.ChatMessageType;
        conversationId: string;
    }[]>;
    create(user: User, params: CreateChatMessageParams): Promise<ChatMessage>;
    createLiveMessage(user: User, params: Create<PERSON>hatMessageParams): Promise<ChatMessage>;
    sendMessage(user: User, params: SendMessageParams): Promise<{
        id: string;
        content: string;
    }>;
}
