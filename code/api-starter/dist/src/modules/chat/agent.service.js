"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../shared/prisma/prisma.service");
const ai_service_1 = require("../../shared/ai/ai.service");
const prompt_1 = require("../../../specs/prompt");
const helpers_1 = require("../../shared/format/helpers");
let AgentService = class AgentService {
    prisma;
    aiService;
    constructor(prisma, aiService) {
        this.prisma = prisma;
        this.aiService = aiService;
    }
    async find({ where, orderBy, skip = 0, take = 10, }) {
        return await this.prisma.agent.findMany({
            where,
            orderBy: orderBy || { createdAt: 'desc' },
            take,
            skip,
        });
    }
    async findAll() {
        return await this.prisma.agent.findMany();
    }
    async get(where) {
        const agent = await this.prisma.agent.findUnique({ where });
        if (!agent)
            throw new common_1.NotFoundException('Agent not found');
        return agent;
    }
    async create(data) {
        try {
            const user = await this.prisma.user.findFirstOrThrow({
                where: { id: data.user.connect.id },
            });
            const aiResponse = await this.aiService.send({
                user,
                type: 'text',
                messages: [{ role: 'user', content: (0, prompt_1.agentPrompt)(data.systemPrompt) }],
            });
            const aiResponseObject = (0, helpers_1.extractJson)(aiResponse);
            return await this.prisma.agent.create({
                data: {
                    name: aiResponseObject.agent.name,
                    description: aiResponseObject.agent.description,
                    systemPrompt: data.systemPrompt,
                    starters: aiResponseObject.agent.starters,
                    user: { connect: { id: user.id } },
                },
            });
        }
        catch (error) {
            throw error;
        }
    }
    async update(where, data) {
        return await this.prisma.agent.update({
            where,
            data: {
                ...data,
                updatedAt: new Date(),
            },
        });
    }
    async delete(where) {
        const agent = await this.prisma.agent.findUnique({ where });
        if (!agent)
            throw new common_1.NotFoundException('Agent not found');
        await this.prisma.agent.delete({ where });
        return true;
    }
};
exports.AgentService = AgentService;
exports.AgentService = AgentService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        ai_service_1.AiService])
], AgentService);
//# sourceMappingURL=agent.service.js.map