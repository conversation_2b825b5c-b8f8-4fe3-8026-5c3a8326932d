import { PrismaService } from '../../shared/prisma/prisma.service';
import { AiService } from '../../shared/ai/ai.service';
import { User, ChatConversation, Prisma, ChatMessage } from '@prisma/client';
export declare class ChatService {
    private prisma;
    private aiService;
    private readonly logger;
    constructor(prisma: PrismaService, aiService: AiService);
    create(data: Prisma.ChatConversationCreateInput): Promise<ChatConversation>;
    get(where: Prisma.ChatConversationWhereUniqueInput): Promise<ChatConversation>;
    delete(where: Prisma.ChatConversationWhereUniqueInput): Promise<boolean>;
    update(where: Prisma.ChatConversationWhereUniqueInput, data: Prisma.ChatConversationUpdateInput): Promise<ChatConversation>;
    findAll(): Promise<ChatConversation[]>;
    find({ where, orderBy, skip, take, }: Prisma.ChatConversationFindManyArgs): Promise<ChatConversation[]>;
    findMessage({ where, orderBy, skip, take, }: Prisma.ChatMessageFindManyArgs): Promise<ChatMessage[]>;
    sendMessage(data: {
        user: User;
        content: string;
        conversationId: string;
        messageType?: 'text' | 'audio' | 'image' | 'video';
    }): Promise<{
        id: string;
        content: string;
    }>;
    updateConversationStatus(where: Prisma.ChatConversationWhereUniqueInput, status: 'active' | 'archived' | 'deleted'): Promise<ChatConversation>;
    clearMessages(id: string): Promise<{
        prompt: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        title: string | null;
        status: import(".prisma/client").$Enums.ChatConversationStatus;
        agentId: string;
    }>;
    createChatMessage(data: Prisma.ChatMessageCreateInput, options?: {
        deductCredits?: boolean;
        userId?: string;
        creditAmount?: number;
    }): Promise<ChatMessage>;
}
