import { AgentService } from './agent.service';
import { Prisma, User } from '@prisma/client';
import { CreateAgentParams, FindAgentParams } from './chat.swagger';
export declare class AgentController {
    private readonly agentService;
    constructor(agentService: AgentService);
    private whereToPrisma;
    create(user: User, params: CreateAgentParams): Promise<{
        name: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        description: string | null;
        systemPrompt: string;
        starters: Prisma.JsonValue | null;
    }>;
    get(id: string): Promise<{
        name: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        description: string | null;
        systemPrompt: string;
        starters: Prisma.JsonValue | null;
    }>;
    find(params: FindAgentParams): Promise<{
        name: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        description: string | null;
        systemPrompt: string;
        starters: Prisma.JsonValue | null;
    }[]>;
    update(id: string, params: Prisma.AgentUpdateInput): Promise<{
        name: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        description: string | null;
        systemPrompt: string;
        starters: Prisma.JsonValue | null;
    }>;
    delete(id: string): Promise<{
        success: boolean;
    }>;
}
