"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatMessageController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const chat_service_1 = require("./chat.service");
const auth_guard_1 = require("../auth/auth.guard");
const acl_guard_1 = require("../../acl/acl.guard");
const acl_decorator_1 = require("../../acl/acl.decorator");
const auth_decorator_1 = require("../auth/auth.decorator");
const chat_swagger_1 = require("./chat.swagger");
const story = __importStar(require("../../../specs/story/chat"));
const ai_model_1 = require("../../shared/ai/ai.model");
let ChatMessageController = class ChatMessageController {
    chatService;
    constructor(chatService) {
        this.chatService = chatService;
    }
    whereToPrisma(params) {
        let where = {};
        if (params.userId) {
            where.userId = params.userId;
        }
        if (params.conversationId) {
            where.conversationId = params.conversationId;
        }
        return where;
    }
    async getConversations(params) {
        const { take, skip, sortField = 'id', sortOrder = 'asc', ...where } = params;
        return await this.chatService.findMessage({
            where: this.whereToPrisma(where),
            take,
            skip,
        });
    }
    async create(user, params) {
        const { conversationId, ...rest } = params;
        return await this.chatService.createChatMessage({
            conversation: { connect: { id: conversationId } },
            user: { connect: { id: user.id } },
            ...rest,
        });
    }
    async createLiveMessage(user, params) {
        const { conversationId, ...rest } = params;
        return await this.chatService.createChatMessage({
            conversation: { connect: { id: conversationId } },
            user: { connect: { id: user.id } },
            ...rest,
        }, {
            deductCredits: true,
            userId: user.id,
            creditAmount: ai_model_1.MODEL_INFO.audio[user.audioModel]?.creditCost || 1,
        });
    }
    async sendMessage(user, params) {
        return await this.chatService.sendMessage({
            user,
            content: params.content,
            conversationId: params.conversationId,
            messageType: params.messageType,
        });
    }
};
exports.ChatMessageController = ChatMessageController;
__decorate([
    (0, common_1.Post)('find'),
    (0, acl_decorator_1.AccessTo)(story.chatMessageToFind.access),
    (0, swagger_1.ApiOperation)(story.chatMessageToFind.operation),
    (0, swagger_1.ApiResponse)(story.chatMessageToFind.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.chatMessageToFind.codes['401'].response),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [chat_swagger_1.FindMessagesParams]),
    __metadata("design:returntype", Promise)
], ChatMessageController.prototype, "getConversations", null);
__decorate([
    (0, common_1.Post)('create'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.chatMessageToCreate.access),
    (0, swagger_1.ApiOperation)(story.chatMessageToCreate.operation),
    (0, swagger_1.ApiResponse)(story.chatMessageToCreate.codes['201'].response),
    (0, swagger_1.ApiResponse)(story.chatMessageToCreate.codes['401'].response),
    __param(0, (0, auth_decorator_1.ReqUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, chat_swagger_1.CreateChatMessageParams]),
    __metadata("design:returntype", Promise)
], ChatMessageController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('create/live'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.chatMessageToCreateLiveMessage.access),
    (0, swagger_1.ApiOperation)(story.chatMessageToCreateLiveMessage.operation),
    (0, swagger_1.ApiResponse)(story.chatMessageToCreateLiveMessage.codes['201'].response),
    (0, swagger_1.ApiResponse)(story.chatMessageToCreateLiveMessage.codes['401'].response),
    (0, acl_decorator_1.AccessCreditNeeded)({
        audio: 1,
    }),
    __param(0, (0, auth_decorator_1.ReqUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, chat_swagger_1.CreateChatMessageParams]),
    __metadata("design:returntype", Promise)
], ChatMessageController.prototype, "createLiveMessage", null);
__decorate([
    (0, common_1.Post)('send'),
    (0, acl_decorator_1.AccessTo)(story.chatMessageToSend.access),
    (0, acl_decorator_1.AccessCreditNeeded)({
        text: 1,
    }),
    (0, swagger_1.ApiOperation)(story.chatMessageToSend.operation),
    (0, swagger_1.ApiResponse)(story.chatMessageToSend.codes['201'].response),
    (0, swagger_1.ApiResponse)(story.chatMessageToSend.codes['401'].response),
    __param(0, (0, auth_decorator_1.ReqUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, chat_swagger_1.SendMessageParams]),
    __metadata("design:returntype", Promise)
], ChatMessageController.prototype, "sendMessage", null);
exports.ChatMessageController = ChatMessageController = __decorate([
    (0, common_1.Controller)('chat/chatMessage'),
    (0, swagger_1.ApiTags)('chat/chatMessage'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    __metadata("design:paramtypes", [chat_service_1.ChatService])
], ChatMessageController);
//# sourceMappingURL=chatMessage.controller.js.map