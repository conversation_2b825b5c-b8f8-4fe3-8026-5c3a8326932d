"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ChatService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../shared/prisma/prisma.service");
const ai_service_1 = require("../../shared/ai/ai.service");
let ChatService = ChatService_1 = class ChatService {
    prisma;
    aiService;
    logger = new common_1.Logger(ChatService_1.name);
    constructor(prisma, aiService) {
        this.prisma = prisma;
        this.aiService = aiService;
    }
    async create(data) {
        const agent = await this.prisma.agent.findFirstOrThrow({
            where: { id: data.agent.connect.id },
        });
        const conversation = await this.prisma.chatConversation.create({
            data: {
                user: { connect: { id: data.user.connect.id } },
                title: data.prompt.slice(0, 20),
                prompt: data.prompt,
                status: 'active',
                agent: { connect: { id: data.agent.connect.id } },
            },
        });
        await this.prisma.chatMessage.create({
            data: {
                conversation: {
                    connect: { id: conversation.id },
                },
                user: {
                    connect: { id: data.user.connect.id },
                },
                role: 'system',
                content: agent.systemPrompt,
                metadata: {
                    timestamp: new Date(),
                },
            },
        });
        await this.prisma.chatMessage.create({
            data: {
                conversation: {
                    connect: { id: conversation.id },
                },
                user: {
                    connect: { id: data.user.connect.id },
                },
                role: 'user',
                content: data.prompt,
                metadata: {
                    timestamp: new Date(),
                },
            },
        });
        const messages = await this.prisma.chatMessage.findMany({
            where: { conversationId: conversation.id },
            orderBy: { createdAt: 'asc' },
        });
        const formattedMessages = messages.map(msg => ({
            role: msg.role,
            content: msg.content,
        }));
        const user = await this.prisma.user.findFirstOrThrow({
            where: { id: data.user.connect.id },
        });
        const aiResponse = await this.aiService.send({
            user,
            type: 'text',
            messages: formattedMessages,
        });
        await this.prisma.chatMessage.create({
            data: {
                conversation: {
                    connect: { id: conversation.id },
                },
                user: {
                    connect: { id: data.user.connect.id },
                },
                role: 'assistant',
                content: aiResponse,
                metadata: {
                    timestamp: new Date(),
                },
            },
        });
        return conversation;
    }
    async get(where) {
        const conversation = await this.prisma.chatConversation.findUnique({
            where,
            include: {
                messages: {
                    orderBy: { createdAt: 'asc' },
                },
            },
        });
        if (!conversation)
            throw new common_1.NotFoundException();
        return conversation;
    }
    async delete(where) {
        try {
            await this.prisma.chatConversation.update({
                where,
                data: { status: 'deleted' },
            });
            return true;
        }
        catch (e) {
            return false;
        }
    }
    async update(where, data) {
        return await this.prisma.chatConversation.update({
            where,
            data: {
                ...data,
                updatedAt: new Date(),
            },
        });
    }
    async findAll() {
        return await this.prisma.chatConversation.findMany({
            where: {
                status: { not: 'deleted' },
            },
        });
    }
    async find({ where, orderBy, skip = 0, take = 10, }) {
        return await this.prisma.chatConversation.findMany({
            where: {
                ...where,
                status: { not: 'deleted' },
            },
            orderBy: orderBy || { createdAt: 'asc' },
            take,
            skip,
        });
    }
    async findMessage({ where, orderBy, skip = 0, take, }) {
        return await this.prisma.chatMessage.findMany({
            where,
            orderBy: orderBy || { createdAt: 'asc' },
            ...(take ? { take, skip } : {}),
        });
    }
    async sendMessage(data) {
        try {
            let aiResponse = '';
            let conversationId = data.conversationId;
            await this.prisma.chatMessage.create({
                data: {
                    conversation: { connect: { id: conversationId } },
                    user: { connect: { id: data.user.id } },
                    role: 'user',
                    messageType: data.messageType || 'text',
                    content: data.content,
                    metadata: {
                        timestamp: new Date(),
                    },
                },
            });
            const messages = await this.prisma.chatMessage.findMany({
                where: { conversationId },
                orderBy: { createdAt: 'asc' },
            });
            const formattedMessages = messages.map(msg => ({
                role: msg.role,
                content: msg.content,
            }));
            aiResponse = await this.aiService.send({
                user: data.user,
                type: 'text',
                messages: formattedMessages,
            });
            await this.prisma.chatMessage.create({
                data: {
                    conversation: { connect: { id: conversationId } },
                    user: { connect: { id: data.user.id } },
                    role: 'assistant',
                    messageType: 'text',
                    content: aiResponse,
                    metadata: {
                        timestamp: new Date(),
                    },
                },
            });
            return {
                id: conversationId,
                content: aiResponse,
            };
        }
        catch (error) {
            this.logger.error('Error in sendMessage:', error);
            throw error;
        }
    }
    async updateConversationStatus(where, status) {
        return await this.update(where, { status });
    }
    async clearMessages(id) {
        return this.prisma.chatConversation.update({
            where: { id },
            data: {
                messages: {
                    deleteMany: {},
                },
            },
        });
    }
    async createChatMessage(data, options) {
        const message = await this.prisma.chatMessage.create({ data });
        if (options?.deductCredits && options.userId) {
            try {
                const creditAmount = options.creditAmount || 1;
                this.logger.log(`Deducting ${creditAmount} credits for message creation`);
                await this.aiService.useCredits(options.userId, creditAmount);
            }
            catch (error) {
                this.logger.error(`Error deducting credits: ${error.message}`, error);
            }
        }
        return message;
    }
};
exports.ChatService = ChatService;
exports.ChatService = ChatService = ChatService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        ai_service_1.AiService])
], ChatService);
//# sourceMappingURL=chat.service.js.map