"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateMessageParams = exports.FindAgentParams = exports.FindMessagesParams = exports.CreateAgentParams = exports.UpdateConversationParams = exports.FindConversationsParams = exports.SendMessageParams = exports.CreateChatMessageParams = exports.CreateConversationParams = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const client_1 = require("@prisma/client");
class CreateConversationParams {
    prompt;
    title;
    agentId;
}
exports.CreateConversationParams = CreateConversationParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'System prompt to initialize the conversation',
        example: 'You are an Espanol teacher. You speak only Espanol and English.',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateConversationParams.prototype, "prompt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Title for the conversation',
        example: 'Espanol Help',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateConversationParams.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Agent ID to associate with the conversation',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateConversationParams.prototype, "agentId", void 0);
class CreateChatMessageParams {
    content;
    conversationId;
    role;
    messageType;
    metadata;
}
exports.CreateChatMessageParams = CreateChatMessageParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Message content',
        example: 'How do we say "Hello" in Espanol?',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateChatMessageParams.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Conversation ID',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateChatMessageParams.prototype, "conversationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Message role',
        enum: client_1.ChatMessageRole,
        default: client_1.ChatMessageRole.user,
    }),
    (0, class_validator_1.IsEnum)(client_1.ChatMessageRole),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateChatMessageParams.prototype, "role", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Message type',
        enum: client_1.ChatMessageType,
        default: client_1.ChatMessageType.text,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.ChatMessageType),
    __metadata("design:type", String)
], CreateChatMessageParams.prototype, "messageType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Additional metadata',
        example: { timestamp: new Date() },
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], CreateChatMessageParams.prototype, "metadata", void 0);
class SendMessageParams {
    content;
    conversationId;
    messageType;
}
exports.SendMessageParams = SendMessageParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Message content',
        example: 'How do we say "Hello" in Espanol?',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], SendMessageParams.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Conversation ID',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], SendMessageParams.prototype, "conversationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Message type',
        enum: client_1.ChatMessageType,
        default: client_1.ChatMessageType.text,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.ChatMessageType),
    __metadata("design:type", String)
], SendMessageParams.prototype, "messageType", void 0);
class FindConversationsParams {
    status;
    sortField;
    sortOrder;
    take;
    skip;
}
exports.FindConversationsParams = FindConversationsParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        enum: client_1.ChatConversationStatus,
        default: client_1.ChatConversationStatus.active,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.ChatConversationStatus),
    __metadata("design:type", String)
], FindConversationsParams.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Order by field',
        enum: Object.keys(client_1.Prisma.ChatConversationScalarFieldEnum),
    }),
    (0, class_validator_1.IsEnum)(client_1.Prisma.ChatConversationScalarFieldEnum),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], FindConversationsParams.prototype, "sortField", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Order sort',
        enum: client_1.Prisma.SortOrder,
    }),
    (0, class_validator_1.IsEnum)(client_1.Prisma.SortOrder),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], FindConversationsParams.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Number or result to return',
        example: 10,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], FindConversationsParams.prototype, "take", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Number or result to skip',
        example: 0,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], FindConversationsParams.prototype, "skip", void 0);
class UpdateConversationParams {
    status;
}
exports.UpdateConversationParams = UpdateConversationParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        enum: client_1.ChatConversationStatus,
        description: 'New status for the conversation',
    }),
    (0, class_validator_1.IsEnum)(client_1.ChatConversationStatus),
    __metadata("design:type", String)
], UpdateConversationParams.prototype, "status", void 0);
class CreateAgentParams {
    systemPrompt;
}
exports.CreateAgentParams = CreateAgentParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'System prompt for the agent',
        example: 'You are a Spanish language teacher. You help users learn Spanish vocabulary, grammar, and cultural aspects.',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateAgentParams.prototype, "systemPrompt", void 0);
class FindMessagesParams {
    userId;
    conversationId;
    sortField;
    sortOrder;
    take;
    skip;
}
exports.FindMessagesParams = FindMessagesParams;
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], FindMessagesParams.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], FindMessagesParams.prototype, "conversationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Order by field',
        enum: Object.keys(client_1.Prisma.ChatMessageScalarFieldEnum),
    }),
    (0, class_validator_1.IsEnum)(client_1.Prisma.ChatMessageScalarFieldEnum),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], FindMessagesParams.prototype, "sortField", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Order sort',
        enum: client_1.Prisma.SortOrder,
    }),
    (0, class_validator_1.IsEnum)(client_1.Prisma.SortOrder),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], FindMessagesParams.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Number or result to return',
        example: 10,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], FindMessagesParams.prototype, "take", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Number or result to skip',
        example: 0,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], FindMessagesParams.prototype, "skip", void 0);
class FindAgentParams {
    name;
    description;
    systemPrompt;
    sortField;
    sortOrder;
    take;
    skip;
}
exports.FindAgentParams = FindAgentParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Filter by agent name',
        example: 'Spanish Teacher',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FindAgentParams.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Filter by agent description',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FindAgentParams.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Filter by system prompt',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FindAgentParams.prototype, "systemPrompt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Order by field',
        enum: Object.keys(client_1.Prisma.AgentScalarFieldEnum),
    }),
    (0, class_validator_1.IsEnum)(client_1.Prisma.AgentScalarFieldEnum),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], FindAgentParams.prototype, "sortField", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Order sort',
        enum: client_1.Prisma.SortOrder,
    }),
    (0, class_validator_1.IsEnum)(client_1.Prisma.SortOrder),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], FindAgentParams.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Number or result to return',
        example: 10,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], FindAgentParams.prototype, "take", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Number or result to skip',
        example: 0,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], FindAgentParams.prototype, "skip", void 0);
class CreateMessageParams {
    prompt;
    title;
    agentId;
}
exports.CreateMessageParams = CreateMessageParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Prompt for the message',
        example: 'How do I learn JavaScript?',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateMessageParams.prototype, "prompt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Title for the message',
        example: 'JavaScript Question',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateMessageParams.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Agent ID to associate with the message',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateMessageParams.prototype, "agentId", void 0);
//# sourceMappingURL=chat.swagger.js.map