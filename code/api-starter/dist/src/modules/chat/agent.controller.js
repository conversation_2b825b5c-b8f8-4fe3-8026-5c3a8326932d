"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const agent_service_1 = require("./agent.service");
const auth_guard_1 = require("../auth/auth.guard");
const acl_guard_1 = require("../../acl/acl.guard");
const acl_decorator_1 = require("../../acl/acl.decorator");
const client_1 = require("@prisma/client");
const chat_swagger_1 = require("./chat.swagger");
const story = __importStar(require("../../../specs/story/agent"));
const auth_decorator_1 = require("../auth/auth.decorator");
let AgentController = class AgentController {
    agentService;
    constructor(agentService) {
        this.agentService = agentService;
    }
    whereToPrisma(params) {
        const where = {};
        if (params.name) {
            where.name = { contains: params.name, mode: 'insensitive' };
        }
        if (params.description) {
            where.description = { contains: params.description, mode: 'insensitive' };
        }
        if (params.systemPrompt) {
            where.systemPrompt = { contains: params.systemPrompt, mode: 'insensitive' };
        }
        return where;
    }
    async create(user, params) {
        return await this.agentService.create({
            ...params,
            user: { connect: { id: user.id } },
        });
    }
    async get(id) {
        return await this.agentService.get({ id });
    }
    async find(params) {
        const { take, skip, sortField = 'id', sortOrder = 'asc', ...where } = params;
        return await this.agentService.find({
            where: this.whereToPrisma(where),
            take,
            skip,
        });
    }
    async update(id, params) {
        return await this.agentService.update({ id }, params);
    }
    async delete(id) {
        const success = await this.agentService.delete({ id });
        if (!success) {
            throw new common_1.NotFoundException('Agent not found');
        }
        return { success: true };
    }
};
exports.AgentController = AgentController;
__decorate([
    (0, common_1.Post)('create'),
    (0, acl_decorator_1.AccessTo)(story.agentToCreate.access),
    (0, swagger_1.ApiOperation)(story.agentToCreate.operation),
    (0, swagger_1.ApiResponse)(story.agentToCreate.codes['201'].response),
    (0, swagger_1.ApiResponse)(story.agentToCreate.codes['401'].response),
    (0, acl_decorator_1.AccessCreditNeeded)({
        text: 1,
    }),
    __param(0, (0, auth_decorator_1.ReqUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, chat_swagger_1.CreateAgentParams]),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, acl_decorator_1.AccessTo)(story.agentToRead.access),
    (0, swagger_1.ApiOperation)(story.agentToRead.operation),
    (0, swagger_1.ApiResponse)(story.agentToCreate.codes['201'].response),
    (0, swagger_1.ApiResponse)(story.agentToCreate.codes['401'].response),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "get", null);
__decorate([
    (0, common_1.Post)('find'),
    (0, acl_decorator_1.AccessTo)(story.agentToFind.access),
    (0, swagger_1.ApiOperation)(story.agentToFind.operation),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of agents matching criteria' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [chat_swagger_1.FindAgentParams]),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "find", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, acl_decorator_1.AccessTo)(story.agentToUpdate.access),
    (0, swagger_1.ApiOperation)(story.agentToUpdate.operation),
    (0, swagger_1.ApiResponse)(story.agentToUpdate.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.agentToUpdate.codes['401'].response),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, acl_decorator_1.AccessTo)(story.agentToDelete.access),
    (0, swagger_1.ApiOperation)(story.agentToDelete.operation),
    (0, swagger_1.ApiResponse)(story.agentToDelete.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.agentToDelete.codes['401'].response),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "delete", null);
exports.AgentController = AgentController = __decorate([
    (0, common_1.Controller)('chat/agent'),
    (0, swagger_1.ApiTags)('chat/agent'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    __metadata("design:paramtypes", [agent_service_1.AgentService])
], AgentController);
//# sourceMappingURL=agent.controller.js.map