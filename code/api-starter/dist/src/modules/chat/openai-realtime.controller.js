"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OpenAIRealtimeController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const auth_guard_1 = require("../auth/auth.guard");
const acl_guard_1 = require("../../acl/acl.guard");
const acl_decorator_1 = require("../../acl/acl.decorator");
const auth_decorator_1 = require("../auth/auth.decorator");
const config_service_1 = require("../../shared/config/config.service");
const story = __importStar(require("../../../specs/story/chat"));
const OpenAIConnector_1 = require("../../shared/ai/OpenAIConnector");
let OpenAIRealtimeController = class OpenAIRealtimeController {
    configService;
    openai;
    constructor(configService) {
        this.configService = configService;
        this.openai = new OpenAIConnector_1.OpenAIConnector({
            apiKey: this.configService.get('OPENAI_API_KEY'),
            organization: this.configService.get('OPENAI_ORGANIZATION'),
        });
    }
    async getSessionToken(user) {
        try {
            const response = await this.openai.createRealtimeSession({
                model: user.audioModel,
                voice: 'alloy',
            });
            return response;
        }
        catch (error) {
            console.error('Error getting OpenAI session token:', error);
            throw error;
        }
    }
};
exports.OpenAIRealtimeController = OpenAIRealtimeController;
__decorate([
    (0, common_1.Get)('session'),
    (0, acl_decorator_1.AccessTo)(story.openaiRealtimeSession.access),
    (0, swagger_1.ApiOperation)(story.openaiRealtimeSession.operation),
    (0, swagger_1.ApiResponse)(story.openaiRealtimeSession.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.openaiRealtimeSession.codes['401'].response),
    __param(0, (0, auth_decorator_1.ReqUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], OpenAIRealtimeController.prototype, "getSessionToken", null);
exports.OpenAIRealtimeController = OpenAIRealtimeController = __decorate([
    (0, common_1.Controller)('chat/openai-realtime'),
    (0, swagger_1.ApiTags)('chat/openai-realtime'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    __metadata("design:paramtypes", [config_service_1.ConfigService])
], OpenAIRealtimeController);
//# sourceMappingURL=openai-realtime.controller.js.map