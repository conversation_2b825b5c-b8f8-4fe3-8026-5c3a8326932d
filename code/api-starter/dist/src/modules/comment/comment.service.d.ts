import { Prisma, Comment, ReportComment } from '@prisma/client';
import { PrismaService } from 'src/shared/prisma/prisma.service';
import { NotificationService } from 'src/shared/notification/notification.service';
export declare class CommentService {
    prisma: PrismaService;
    notificationService: NotificationService;
    constructor(prisma: PrismaService, notificationService: NotificationService);
    create(data: Prisma.CommentCreateInput): Promise<Comment>;
    get({ id }: Prisma.CommentWhereUniqueInput): Promise<Comment>;
    delete(id: string): Promise<boolean>;
    update(where: Prisma.CommentWhereUniqueInput, params: Prisma.CommentUpdateInput): Promise<Comment>;
    findAll(): Promise<Comment[]>;
    find({ where, orderBy, skip, take, }: Prisma.CommentFindManyArgs): Promise<Comment[]>;
    private generateWhereReportsOptions;
    findCommentsWithReports({ where, orderBy, skip, take }: Prisma.CommentFindManyArgs, reported: boolean): Promise<any[]>;
    countReportedComments({ where }: Prisma.CommentFindManyArgs, reported: boolean): Promise<number>;
    list({ where }: Prisma.CommentFindManyArgs): Promise<Comment[]>;
    exists(where: Prisma.CommentWhereInput): Promise<boolean>;
    count(where: Prisma.CommentWhereInput): Promise<number>;
    getBy(where: Prisma.CommentWhereInput): Promise<Comment>;
    reportComment(data: Prisma.ReportCommentCreateInput): Promise<ReportComment>;
}
