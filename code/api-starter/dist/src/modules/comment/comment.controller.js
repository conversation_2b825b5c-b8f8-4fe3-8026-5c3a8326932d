"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommentController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const acl_decorator_1 = require("../../acl/acl.decorator");
const acl_guard_1 = require("../../acl/acl.guard");
const i18n_service_1 = require("../../shared/i18n/i18n.service");
const auth_decorator_1 = require("../auth/auth.decorator");
const auth_guard_1 = require("../auth/auth.guard");
const comment_service_1 = require("./comment.service");
const comment_swagger_1 = require("./comment.swagger");
const story = __importStar(require("../../../specs/story/comment"));
const report_swagger_1 = require("../report/report.swagger");
let CommentController = class CommentController {
    commentService;
    i18nService;
    constructor(commentService, i18nService) {
        this.commentService = commentService;
        this.i18nService = i18nService;
    }
    async find(params) {
        const { take, skip, sortField = 'id', sortOrder = 'asc', ...where } = params;
        return await this.commentService.find({
            where,
            orderBy: { [sortField]: sortOrder },
            take,
            skip,
        });
    }
    async count(params) {
        return await this.commentService.count(params);
    }
    async list(params) {
        return await this.commentService.list({ where: params });
    }
    async findReportedComments(params) {
        const { take, skip, sortField = 'createdAt', sortOrder = 'desc', reported, ...where } = params;
        return await this.commentService.findCommentsWithReports({
            where,
            orderBy: { [sortField]: sortOrder },
            take,
            skip,
        }, reported);
    }
    async countReportedComments(params) {
        const { reported, ...where } = params;
        return await this.commentService.countReportedComments({
            where,
        }, reported);
    }
    async create(user, comment) {
        const data = { ...comment, profile: { connect: { userId: user.id } } };
        return await this.commentService.create(data);
    }
    async createAnswer(user, comment) {
        let { commentId, ...data } = comment;
        data.user = { connect: { id: user.id } };
        if (commentId) {
            data.comment = { connect: { id: commentId } };
        }
        return await this.commentService.create(data);
    }
    async updateByAdmin(id, params) {
        return await this.commentService.update({ id }, params);
    }
    async update(id, params) {
        return await this.commentService.update({ id }, params);
    }
    async deleteAdmin(id) {
        return await this.commentService.delete(id);
    }
    async delete(id) {
        return await this.commentService.delete(id);
    }
    async reportComment(id, user, reportComment) {
        const data = {
            ...reportComment,
            reported: true,
            userId: user.id,
            comment: { connect: { id: id } },
        };
        return this.commentService.reportComment(data);
    }
};
exports.CommentController = CommentController;
__decorate([
    (0, common_1.Post)('find'),
    (0, swagger_1.ApiOperation)(story.commentToFind.operation),
    (0, swagger_1.ApiResponse)(story.commentToFind.codes['201'].response),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [comment_swagger_1.FindCommentParams]),
    __metadata("design:returntype", Promise)
], CommentController.prototype, "find", null);
__decorate([
    (0, common_1.Post)('count'),
    (0, swagger_1.ApiOperation)(story.commentToCount.operation),
    (0, swagger_1.ApiResponse)(story.commentToCount.codes['201'].response),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [comment_swagger_1.FindCommentParams]),
    __metadata("design:returntype", Promise)
], CommentController.prototype, "count", null);
__decorate([
    (0, common_1.Post)('list'),
    (0, acl_decorator_1.AccessTo)(story.commentToList.access),
    (0, swagger_1.ApiOperation)(story.commentToList.operation),
    (0, swagger_1.ApiResponse)(story.commentToList.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.commentToList.codes['401'].response),
    (0, swagger_1.ApiResponse)(story.commentToList.codes['404'].response),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [comment_swagger_1.ListCommentParams]),
    __metadata("design:returntype", Promise)
], CommentController.prototype, "list", null);
__decorate([
    (0, common_1.Post)('reported'),
    (0, acl_decorator_1.AccessTo)(story.commentWithReportToFind.access),
    (0, swagger_1.ApiOperation)(story.commentWithReportToFind.operation),
    (0, swagger_1.ApiResponse)(story.commentWithReportToFind.codes['201'].response),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [report_swagger_1.FindReportParams]),
    __metadata("design:returntype", Promise)
], CommentController.prototype, "findReportedComments", null);
__decorate([
    (0, common_1.Post)('reported/count'),
    (0, acl_decorator_1.AccessTo)(story.commentWithReportToCount.access),
    (0, swagger_1.ApiOperation)(story.commentWithReportToCount.operation),
    (0, swagger_1.ApiResponse)(story.commentWithReportToCount.codes['201'].response),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [report_swagger_1.FindReportParams]),
    __metadata("design:returntype", Promise)
], CommentController.prototype, "countReportedComments", null);
__decorate([
    (0, common_1.Post)(''),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.commentToCreate.access),
    (0, swagger_1.ApiOperation)(story.commentToCreate.operation),
    (0, swagger_1.ApiResponse)(story.commentToCreate.codes['201'].response),
    __param(0, (0, auth_decorator_1.ReqUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, comment_swagger_1.CreateCommentParams]),
    __metadata("design:returntype", Promise)
], CommentController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('answer'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.answerToCreate.access),
    (0, swagger_1.ApiOperation)(story.answerToCreate.operation),
    (0, swagger_1.ApiResponse)(story.answerToCreate.codes['201'].response),
    __param(0, (0, auth_decorator_1.ReqUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, comment_swagger_1.CreateCommentAnswerParams]),
    __metadata("design:returntype", Promise)
], CommentController.prototype, "createAnswer", null);
__decorate([
    (0, common_1.Patch)('admin/:id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.commentToUpdateByAdmin.access),
    (0, swagger_1.ApiOperation)(story.commentToUpdateByAdmin.operation),
    (0, swagger_1.ApiResponse)(story.commentToUpdateByAdmin.codes['200'].response),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, comment_swagger_1.UpdateCommentParams]),
    __metadata("design:returntype", Promise)
], CommentController.prototype, "updateByAdmin", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.commentToUpdate.access),
    (0, swagger_1.ApiOperation)(story.commentToUpdate.operation),
    (0, swagger_1.ApiResponse)(story.commentToUpdate.codes['200'].response),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, comment_swagger_1.UpdateCommentParams]),
    __metadata("design:returntype", Promise)
], CommentController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)('admin/:id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.commentToDeleteByAdmin.access),
    (0, swagger_1.ApiOperation)(story.commentToDeleteByAdmin.operation),
    (0, swagger_1.ApiResponse)(story.commentToDeleteByAdmin.codes['200'].response),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CommentController.prototype, "deleteAdmin", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.commentToDelete.access),
    (0, swagger_1.ApiOperation)(story.commentToDelete.operation),
    (0, swagger_1.ApiResponse)(story.commentToDelete.codes['200'].response),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CommentController.prototype, "delete", null);
__decorate([
    (0, common_1.Post)(':id/report'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.reportCommentToCreate.access),
    (0, swagger_1.ApiOperation)(story.reportCommentToCreate.operation),
    (0, swagger_1.ApiResponse)(story.reportCommentToCreate.codes['201'].response),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, auth_decorator_1.ReqUser)()),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, comment_swagger_1.SignalCommentParams]),
    __metadata("design:returntype", Promise)
], CommentController.prototype, "reportComment", null);
exports.CommentController = CommentController = __decorate([
    (0, swagger_1.ApiTags)('comment'),
    (0, common_1.Controller)('comment'),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [comment_service_1.CommentService,
        i18n_service_1.I18nService])
], CommentController);
//# sourceMappingURL=comment.controller.js.map