"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommentService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../shared/prisma/prisma.service");
const notification_service_1 = require("../../shared/notification/notification.service");
const notifications_1 = __importDefault(require("../../shared/notification/notifications"));
let CommentService = class CommentService {
    prisma;
    notificationService;
    constructor(prisma, notificationService) {
        this.prisma = prisma;
        this.notificationService = notificationService;
    }
    async create(data) {
        const comment = await this.prisma.comment.create({ data });
        if (comment && comment.commentId) {
            await this.notificationService.sendAndSave([comment.userId], notifications_1.default.commentAnswer(comment.commentId));
        }
        return comment;
    }
    async get({ id }) {
        return await this.prisma.comment.findUnique({
            where: {
                id,
            },
            include: {
                profile: true,
                anwsers: {
                    include: {
                        profile: true,
                    },
                },
            },
        });
    }
    async delete(id) {
        try {
            await this.prisma.comment.delete({ where: { id } });
            return true;
        }
        catch (e) {
            return false;
        }
    }
    async update(where, params) {
        return await this.prisma.comment.update({
            data: params,
            where,
            include: {
                profile: true,
                anwsers: {
                    include: {
                        profile: true,
                    },
                },
            },
        });
    }
    async findAll() {
        return await this.prisma.comment.findMany({
            include: {
                profile: true,
            },
        });
    }
    async find({ where, orderBy, skip = 0, take = 10, }) {
        return await this.prisma.comment.findMany({
            where,
            orderBy: [
                {
                    createdAt: 'desc',
                },
            ],
            include: {
                profile: true,
                anwsers: {
                    include: {
                        profile: true,
                    },
                },
            },
            skip,
            take,
        });
    }
    generateWhereReportsOptions(reported) {
        if (reported != null && reported != undefined) {
            return reported
                ? {
                    some: {
                        reported: true,
                    },
                }
                : {
                    none: {},
                };
        }
    }
    async findCommentsWithReports({ where, orderBy, skip, take }, reported) {
        const reportsOptions = this.generateWhereReportsOptions(reported);
        let reportedComments = await this.prisma.comment.findMany({
            select: {
                id: true,
                userId: true,
                profile: true,
                text: true,
                createdAt: true,
                updatedAt: true,
                reports: true,
            },
            where: {
                ...where,
                reports: reportsOptions,
            },
            orderBy,
            skip,
            take,
        });
        return reportedComments;
    }
    async countReportedComments({ where }, reported) {
        const reportsOptions = this.generateWhereReportsOptions(reported);
        return this.prisma.comment.count({
            where: {
                ...where,
                reports: reportsOptions,
            },
        });
    }
    async list({ where }) {
        return await this.prisma.comment.findMany({
            where,
            orderBy: [
                {
                    createdAt: 'desc',
                },
            ],
            include: {
                profile: true,
                anwsers: {
                    include: {
                        profile: true,
                    },
                },
            },
        });
    }
    async exists(where) {
        return (await this.prisma.comment.count({ where })) > 0 ? true : false;
    }
    async count(where) {
        return await this.prisma.comment.count({
            where,
        });
    }
    async getBy(where) {
        const data = await this.prisma.comment.findFirst({
            where,
            include: {
                profile: true,
                anwsers: {
                    include: {
                        profile: true,
                    },
                },
            },
        });
        if (!data)
            throw new common_1.NotFoundException();
        return data;
    }
    async reportComment(data) {
        return this.prisma.reportComment.create({ data });
    }
};
exports.CommentService = CommentService;
exports.CommentService = CommentService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        notification_service_1.NotificationService])
], CommentService);
//# sourceMappingURL=comment.service.js.map