"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SignalCommentParams = exports.UpdateCommentParams = exports.CreateCommentAnswerParams = exports.CreateCommentParams = exports.ListCommentParams = exports.FindCommentParams = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const client_1 = require("@prisma/client");
const client_2 = require("@prisma/client");
class FindCommentParams {
    objectType;
    objectId;
    take;
    skip;
    sortField;
    sortOrder;
}
exports.FindCommentParams = FindCommentParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'ObjectType :' + Object.keys(client_1.ObjectType).join(','),
        example: client_1.ObjectType.news,
    }),
    (0, class_validator_1.IsEnum)(Object.keys(client_1.ObjectType)),
    __metadata("design:type", String)
], FindCommentParams.prototype, "objectType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Object Id',
        example: 'seed.newsToRead.result.id',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FindCommentParams.prototype, "objectId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Number or result to return',
        example: 10,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], FindCommentParams.prototype, "take", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Number or result to skip',
        example: 0,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], FindCommentParams.prototype, "skip", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Order by field ',
        enum: Object.keys(client_2.Prisma.CommentScalarFieldEnum),
    }),
    (0, class_validator_1.IsEnum)(Object.keys(client_2.Prisma.CommentScalarFieldEnum)),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], FindCommentParams.prototype, "sortField", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Order sort',
        enum: client_2.Prisma.SortOrder,
    }),
    (0, class_validator_1.IsEnum)(client_2.Prisma.SortOrder),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], FindCommentParams.prototype, "sortOrder", void 0);
class ListCommentParams {
    objectType;
    objectId;
}
exports.ListCommentParams = ListCommentParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'ObjectType :' + Object.keys(client_1.ObjectType).join(','),
        example: client_1.ObjectType.news,
    }),
    (0, class_validator_1.IsEnum)(Object.keys(client_1.ObjectType)),
    __metadata("design:type", String)
], ListCommentParams.prototype, "objectType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: '',
        example: 'seed.newsToRead.result.id',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListCommentParams.prototype, "objectId", void 0);
class CreateCommentParams {
    text;
    objectId;
    objectType;
}
exports.CreateCommentParams = CreateCommentParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: '',
        example: 'mock.paragraph',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateCommentParams.prototype, "text", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: '',
        example: 'seed.newsToRead.result.id',
    }),
    __metadata("design:type", String)
], CreateCommentParams.prototype, "objectId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'ObjectType :' + Object.keys(client_1.ObjectType).join(','),
        example: client_1.ObjectType.news,
    }),
    (0, class_validator_1.IsEnum)(Object.keys(client_1.ObjectType)),
    __metadata("design:type", String)
], CreateCommentParams.prototype, "objectType", void 0);
class CreateCommentAnswerParams {
    commentId;
    text;
    objectId;
    objectType;
}
exports.CreateCommentAnswerParams = CreateCommentAnswerParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: '',
        example: '',
    }),
    __metadata("design:type", String)
], CreateCommentAnswerParams.prototype, "commentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: '',
        example: 'mock.paragraph',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateCommentAnswerParams.prototype, "text", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: '',
        example: '',
    }),
    __metadata("design:type", String)
], CreateCommentAnswerParams.prototype, "objectId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'ObjectType :' + Object.keys(client_1.ObjectType).join(','),
        example: client_1.ObjectType.news,
    }),
    (0, class_validator_1.IsEnum)(Object.keys(client_1.ObjectType)),
    __metadata("design:type", String)
], CreateCommentAnswerParams.prototype, "objectType", void 0);
class UpdateCommentParams {
    text;
}
exports.UpdateCommentParams = UpdateCommentParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Comment text',
        example: 'mock.paragraph',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateCommentParams.prototype, "text", void 0);
class SignalCommentParams {
    text;
    content;
}
exports.SignalCommentParams = SignalCommentParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: '',
        example: 'mock.paragraph',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SignalCommentParams.prototype, "text", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Content to report',
        example: 'mock.paragraph',
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], SignalCommentParams.prototype, "content", void 0);
//# sourceMappingURL=comment.swagger.js.map