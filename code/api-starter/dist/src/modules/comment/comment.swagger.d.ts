import { ObjectType } from '@prisma/client';
import { Prisma } from '@prisma/client';
export declare class FindCommentParams {
    objectType: ObjectType;
    objectId: string;
    take: number;
    skip: number;
    sortField: string;
    sortOrder: Prisma.SortOrder;
}
export declare class ListCommentParams {
    objectType: ObjectType;
    objectId: string;
}
export declare class CreateCommentParams {
    text: string;
    objectId: string;
    objectType: ObjectType;
}
export declare class CreateCommentAnswerParams {
    commentId: string;
    text: string;
    objectId: string;
    objectType: ObjectType;
}
export declare class UpdateCommentParams {
    text: string;
}
export declare class SignalCommentParams {
    text: string;
    content?: any;
}
