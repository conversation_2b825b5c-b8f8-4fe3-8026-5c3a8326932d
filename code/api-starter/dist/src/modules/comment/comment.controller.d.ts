import { I18nService } from 'src/shared/i18n/i18n.service';
import { User, Comment, ReportComment } from '@prisma/client';
import { CommentService } from 'src/modules/comment/comment.service';
import { CreateCommentAnswerParams, CreateCommentParams, FindCommentParams, SignalCommentParams, UpdateCommentParams, ListCommentParams } from './comment.swagger';
import { FindReportParams } from '../report/report.swagger';
export declare class CommentController {
    private readonly commentService;
    private readonly i18nService;
    constructor(commentService: CommentService, i18nService: I18nService);
    find(params: FindCommentParams): Promise<Comment[]>;
    count(params: FindCommentParams): Promise<number>;
    list(params: ListCommentParams): Promise<Comment[]>;
    findReportedComments(params: FindReportParams): Promise<any>;
    countReportedComments(params: FindReportParams): Promise<number>;
    create(user: User, comment: CreateCommentParams): Promise<Comment>;
    createAnswer(user: User, comment: CreateCommentAnswerParams): Promise<Comment>;
    updateByAdmin(id: string, params: UpdateCommentParams): Promise<Comment>;
    update(id: string, params: UpdateCommentParams): Promise<Comment>;
    deleteAdmin(id: string): Promise<boolean>;
    delete(id: string): Promise<boolean>;
    reportComment(id: string, user: User, reportComment: SignalCommentParams): Promise<ReportComment>;
}
