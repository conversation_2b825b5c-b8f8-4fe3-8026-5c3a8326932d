"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExempleController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const acl_decorator_1 = require("../../acl/acl.decorator");
const acl_guard_1 = require("../../acl/acl.guard");
const auth_guard_1 = require("../auth/auth.guard");
const exemple_service_1 = require("./exemple.service");
const exemple_swagger_1 = require("./exemple.swagger");
const auth_decorator_1 = require("../auth/auth.decorator");
const story = __importStar(require("../../../specs/story/exemple"));
const config_service_1 = require("../../shared/config/config.service");
let ExempleController = class ExempleController {
    exempleService;
    config;
    constructor(exempleService, config) {
        this.exempleService = exempleService;
        this.config = config;
    }
    async create(user, exemple) {
        const data = { ...exemple, userId: user.id };
        return await this.exempleService.create(data);
    }
    whereToPrisma(params) {
        let where = {};
        if (params.name) {
            where.name = {
                contains: params.name,
                mode: 'insensitive',
            };
        }
        return where;
    }
    async find(user, params) {
        const { take, skip, sortField = 'id', sortOrder = 'asc', ...where } = params;
        return await this.exempleService.find({
            where,
            take,
            skip,
        });
    }
    async count(params) {
        return await this.exempleService.count(this.whereToPrisma(params));
    }
    async read(id) {
        const exemple = await this.exempleService.get({ id });
        if (!exemple) {
            throw new common_1.NotFoundException();
        }
        return exemple;
    }
    async delete(id) {
        const exemple = await this.exempleService.delete({ id });
        if (!exemple) {
            throw new common_1.NotFoundException();
        }
        return { success: true };
    }
    async update(id, params) {
        const exemple = await this.exempleService.update({ id }, params);
        if (!exemple) {
            throw new common_1.NotFoundException();
        }
        return exemple;
    }
};
exports.ExempleController = ExempleController;
__decorate([
    (0, common_1.Post)(''),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.exempleToCreate.access),
    (0, swagger_1.ApiOperation)(story.exempleToCreate.operation),
    (0, swagger_1.ApiResponse)(story.exempleToCreate.codes['201'].response),
    (0, swagger_1.ApiResponse)(story.exempleToCreate.codes['401'].response),
    __param(0, (0, auth_decorator_1.ReqUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, exemple_swagger_1.CreateExempleParams]),
    __metadata("design:returntype", Promise)
], ExempleController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('find'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.exempleToFind.access),
    (0, swagger_1.ApiOperation)(story.exempleToFind.operation),
    (0, swagger_1.ApiResponse)(story.exempleToFind.codes['201'].response),
    __param(0, (0, auth_decorator_1.ReqUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, exemple_swagger_1.FindExempleParams]),
    __metadata("design:returntype", Promise)
], ExempleController.prototype, "find", null);
__decorate([
    (0, common_1.Post)('count'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.exempleToCount.access),
    (0, swagger_1.ApiOperation)(story.exempleToCount.operation),
    (0, swagger_1.ApiResponse)(story.exempleToCount.codes['201'].response),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [exemple_swagger_1.FindExempleParams]),
    __metadata("design:returntype", Promise)
], ExempleController.prototype, "count", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.exempleToRead.access),
    (0, swagger_1.ApiOperation)(story.exempleToRead.operation),
    (0, swagger_1.ApiResponse)(story.exempleToRead.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.exempleToRead.codes['401'].response),
    (0, swagger_1.ApiResponse)(story.exempleToRead.codes['404'].response),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ExempleController.prototype, "read", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.exempleToDelete.access),
    (0, swagger_1.ApiOperation)(story.exempleToDelete.operation),
    (0, swagger_1.ApiResponse)(story.exempleToDelete.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.exempleToDelete.codes['403'].response),
    (0, swagger_1.ApiResponse)(story.exempleToDelete.codes['404'].response),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ExempleController.prototype, "delete", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.exempleToUpdate.access),
    (0, swagger_1.ApiOperation)(story.exempleToUpdate.operation),
    (0, swagger_1.ApiResponse)(story.exempleToUpdate.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.exempleToUpdate.codes['401'].response),
    (0, swagger_1.ApiResponse)(story.exempleToUpdate.codes['404'].response),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, exemple_swagger_1.UpdateExempleParams]),
    __metadata("design:returntype", Promise)
], ExempleController.prototype, "update", null);
exports.ExempleController = ExempleController = __decorate([
    (0, common_1.Controller)('exemple'),
    (0, swagger_1.ApiTags)('exemple'),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [exemple_service_1.ExempleService,
        config_service_1.ConfigService])
], ExempleController);
//# sourceMappingURL=exemple.controller.js.map