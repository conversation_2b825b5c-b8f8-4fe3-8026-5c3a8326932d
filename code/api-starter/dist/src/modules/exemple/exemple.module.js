"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExempleModule = void 0;
const common_1 = require("@nestjs/common");
const exemple_controller_1 = require("./exemple.controller");
const exemple_upload_controller_1 = require("./exemple.upload.controller");
const exemple_service_1 = require("./exemple.service");
let ExempleModule = class ExempleModule {
};
exports.ExempleModule = ExempleModule;
exports.ExempleModule = ExempleModule = __decorate([
    (0, common_1.Module)({
        imports: [],
        controllers: [exemple_controller_1.ExempleController, exemple_upload_controller_1.ExempleUploadController],
        providers: [exemple_service_1.ExempleService, exemple_controller_1.ExempleController],
        exports: [exemple_service_1.ExempleService, exemple_controller_1.ExempleController],
    })
], ExempleModule);
//# sourceMappingURL=exemple.module.js.map