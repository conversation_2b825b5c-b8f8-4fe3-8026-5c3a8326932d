import { Exemple, Prisma, User } from '@prisma/client';
import { ExempleService } from './exemple.service';
import { CreateExempleParams, FindExempleParams, UpdateExempleParams } from './exemple.swagger';
import { ConfigService } from 'src/shared/config/config.service';
export declare class ExempleController {
    private readonly exempleService;
    config: ConfigService;
    constructor(exempleService: ExempleService, config: ConfigService);
    create(user: User, exemple: CreateExempleParams): Promise<Exemple>;
    whereToPrisma(params: any): Prisma.ExempleWhereInput;
    find(user: User, params: FindExempleParams): Promise<any>;
    count(params: FindExempleParams): Promise<number>;
    read(id: string): Promise<any>;
    delete(id: string): Promise<any>;
    update(id: string, params: UpdateExempleParams): Promise<Exemple>;
}
