"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExempleUploadController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const swagger_1 = require("@nestjs/swagger");
const acl_decorator_1 = require("../../acl/acl.decorator");
const acl_guard_1 = require("../../acl/acl.guard");
const auth_guard_1 = require("../auth/auth.guard");
const file_service_1 = require("../../shared/file/file.service");
const story = __importStar(require("../../../specs/story/exemple"));
const swagger_decorator_1 = require("../../shared/swagger/swagger.decorator");
const auth_decorator_1 = require("../auth/auth.decorator");
let ExempleUploadController = class ExempleUploadController extends file_service_1.FileService {
    async picture(id, user, image, fileUrl) {
        const uploadedFile = await this.uploadMedia({
            folder: 'exemple',
            file: image || fileUrl,
            width: 200,
            height: 200,
            thumbWidth: 200,
            thumbHeight: 200,
        });
        return await this.update({
            model: 'exemple',
            uploadedFile,
            data: {
                imageUrl: uploadedFile.url,
                image: uploadedFile.json,
            },
            where: {
                id,
            },
        });
    }
    async file(id, file, fileUrl) {
        const uploadedFile = await this.uploadFile({
            folder: 'exemple',
            file: file || fileUrl,
        });
        return await this.createFile({
            model: 'exempleFile',
            uploadedFile: uploadedFile,
            type: uploadedFile.type,
            ownerField: 'user',
            valueField: 'id',
            value: id,
        });
    }
};
exports.ExempleUploadController = ExempleUploadController;
__decorate([
    (0, common_1.Post)('/:id/picture'),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, swagger_decorator_1.ApiFile)('file'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.exemplePictureToUpload.access),
    (0, swagger_1.ApiOperation)(story.exemplePictureToUpload.operation),
    (0, swagger_1.ApiResponse)(story.exemplePictureToUpload.codes['201'].response),
    (0, swagger_1.ApiResponse)(story.exemplePictureToUpload.codes['400'].response),
    (0, swagger_1.ApiResponse)(story.exemplePictureToUpload.codes['403'].response),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file', { dest: '/tmp/' })),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, auth_decorator_1.ReqUser)()),
    __param(2, (0, common_1.UploadedFile)('file')),
    __param(3, (0, common_1.Body)('fileUrl')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object, String]),
    __metadata("design:returntype", Promise)
], ExempleUploadController.prototype, "picture", null);
__decorate([
    (0, common_1.Post)('/:id/file'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, swagger_decorator_1.ApiFile)('file'),
    (0, acl_decorator_1.AccessTo)(story.exempleFileToUpload.access),
    (0, swagger_1.ApiOperation)(story.exempleFileToUpload.operation),
    (0, swagger_1.ApiResponse)(story.exempleFileToUpload.codes['201'].response),
    (0, swagger_1.ApiResponse)(story.exempleFileToUpload.codes['400'].response),
    (0, swagger_1.ApiResponse)(story.exempleFileToUpload.codes['403'].response),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file', { dest: '/tmp/' })),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.UploadedFile)('file')),
    __param(2, (0, common_1.Body)('fileUrl')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, String]),
    __metadata("design:returntype", Promise)
], ExempleUploadController.prototype, "file", null);
exports.ExempleUploadController = ExempleUploadController = __decorate([
    (0, common_1.Controller)('exemple'),
    (0, swagger_1.ApiTags)('exemple'),
    (0, swagger_1.ApiBearerAuth)()
], ExempleUploadController);
//# sourceMappingURL=exemple.upload.controller.js.map