import { Exemple, Prisma } from '@prisma/client';
import { PrismaService } from 'src/shared/prisma/prisma.service';
export declare class ExempleService {
    prisma: PrismaService;
    constructor(prisma: PrismaService);
    create(data: Prisma.ExempleCreateInput): Promise<Exemple>;
    get(where: Prisma.ExempleWhereUniqueInput): Promise<Exemple>;
    delete(where: Prisma.ExempleWhereUniqueInput): Promise<any>;
    update(where: Prisma.ExempleWhereUniqueInput, params: Prisma.ExempleUpdateInput): Promise<Exemple>;
    findAll(): Promise<Exemple[]>;
    find({ where, orderBy, skip, take, }: Prisma.ExempleFindManyArgs): Promise<Exemple[]>;
    exists(where: Prisma.ExempleWhereInput): Promise<boolean>;
    count(where: Prisma.ExempleWhereInput): Promise<number>;
    getBy(where: Prisma.ExempleWhereInput): Promise<Exemple>;
}
