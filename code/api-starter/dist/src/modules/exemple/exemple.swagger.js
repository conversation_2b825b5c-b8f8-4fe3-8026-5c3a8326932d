"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FindExempleParams = exports.UpdateExempleParams = exports.CreateExempleParams = void 0;
const swagger_1 = require("@nestjs/swagger");
const client_1 = require("@prisma/client");
const class_validator_1 = require("class-validator");
class CreateExempleParams {
    name;
}
exports.CreateExempleParams = CreateExempleParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Exemple Name',
        example: 'mock.name',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateExempleParams.prototype, "name", void 0);
class UpdateExempleParams {
    name;
}
exports.UpdateExempleParams = UpdateExempleParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Exemple Name',
        example: 'mock.name',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateExempleParams.prototype, "name", void 0);
class FindExempleParams {
    name;
    sortField;
    sortOrder;
    take;
    skip;
}
exports.FindExempleParams = FindExempleParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: '',
        example: 'seed.exempleToRead.result.name',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FindExempleParams.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Order by field',
        enum: Object.keys(client_1.Prisma.ExempleScalarFieldEnum),
    }),
    (0, class_validator_1.IsEnum)(Object.keys(client_1.Prisma.ExempleScalarFieldEnum)),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], FindExempleParams.prototype, "sortField", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Order sort',
        enum: client_1.Prisma.SortOrder,
    }),
    (0, class_validator_1.IsEnum)(client_1.Prisma.SortOrder),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], FindExempleParams.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Number or result to return',
        example: 10,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], FindExempleParams.prototype, "take", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Number or result to skip',
        example: 0,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], FindExempleParams.prototype, "skip", void 0);
//# sourceMappingURL=exemple.swagger.js.map