"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IapController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const auth_decorator_1 = require("../auth/auth.decorator");
const iap_service_1 = require("./iap.service");
const iap_swagger_1 = require("./iap.swagger");
const auth_guard_1 = require("../auth/auth.guard");
const acl_guard_1 = require("../../acl/acl.guard");
const androidPackageName = process.env.ANDROID_PACKAGE_NAME;
let IapController = class IapController {
    iapService;
    constructor(iapService) {
        this.iapService = iapService;
    }
    async save(params, user) {
        const purchase = JSON.parse(JSON.stringify(params.purchase));
        const receipt = params.appType === 'ios'
            ? purchase.transactionReceipt
            : {
                packageName: androidPackageName,
                productId: purchase.productId,
                purchaseToken: purchase.purchaseToken,
                subscription: true,
            };
        return await this.iapService.processPurchase(params.appType, user.id, receipt);
    }
    async buyGold(params, user) {
        const purchase = JSON.parse(JSON.stringify(params.purchase));
        const receipt = params.appType === 'ios'
            ? purchase.transactionReceipt
            : {
                packageName: androidPackageName,
                productId: purchase.productId,
                purchaseToken: purchase.purchaseToken,
                subscription: false,
            };
        return await this.iapService.purchaseGold(params.appType, user.id, receipt);
    }
    async read(appType, user) {
        const userId = user ? user.id : 0;
        const subscription = await this.iapService.getUserSubscription(userId, appType);
        return {
            subscription,
            hasSubscription: this.iapService.checkIfHasSubscription(subscription),
        };
    }
};
exports.IapController = IapController;
__decorate([
    (0, common_1.Post)('save-receipt'),
    (0, swagger_1.ApiOperation)({
        summary: 'As a user, i want to subscribe to the premium account',
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Subscription saved',
    }),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, auth_decorator_1.ReqUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [iap_swagger_1.SaveReceiptParams, Object]),
    __metadata("design:returntype", Promise)
], IapController.prototype, "save", null);
__decorate([
    (0, common_1.Post)('gold'),
    (0, swagger_1.ApiOperation)({
        summary: 'As a user, i want to buy gold',
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Gold saved',
    }),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, auth_decorator_1.ReqUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [iap_swagger_1.SaveReceiptParams, Object]),
    __metadata("design:returntype", Promise)
], IapController.prototype, "buyGold", null);
__decorate([
    (0, common_1.Get)('user-subscription/:appType'),
    (0, swagger_1.ApiOperation)({
        summary: 'As a user, i want to find my subscription',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Subscription data',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'There is no subscription for this user',
    }),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    __param(0, (0, common_1.Param)('appType')),
    __param(1, (0, auth_decorator_1.ReqUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], IapController.prototype, "read", null);
exports.IapController = IapController = __decorate([
    (0, common_1.Controller)('iap'),
    (0, swagger_1.ApiTags)('iap'),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [iap_service_1.IapService])
], IapController);
//# sourceMappingURL=iap.controller.js.map