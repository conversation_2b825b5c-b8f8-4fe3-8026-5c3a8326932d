import { Prisma } from '@prisma/client';
import { PrismaService } from 'src/shared/prisma/prisma.service';
import { GoldUpdateParams, SubscriptionUpdateParams } from 'src/modules/iap/iap.swagger';
import { ConfigService } from 'src/shared/config/config.service';
export declare class IapService {
    prisma: PrismaService;
    configService: ConfigService;
    androidGoogleApi: import("googleapis").androidpublisher_v3.Androidpublisher;
    androidPackageName: any;
    constructor(prisma: PrismaService, configService: ConfigService);
    getActiveSubscriptions(): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        environment: string;
        origTxId: string;
        latestReceipt: string;
        app: string;
        productId: string;
        validationResponse: Prisma.JsonValue;
        startDate: Date;
        endDate: Date;
        isCancelled: boolean;
        fake: boolean;
    }[]>;
    updateSubscription(subscription: SubscriptionUpdateParams): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        environment: string;
        origTxId: string;
        latestReceipt: string;
        app: string;
        productId: string;
        validationResponse: Prisma.JsonValue;
        startDate: Date;
        endDate: Date;
        isCancelled: boolean;
        fake: boolean;
    }>;
    addGold(purchase: GoldUpdateParams): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string | null;
        description: string | null;
        isSpent: boolean;
        amount: number;
        status: import(".prisma/client").$Enums.TransactionStatus;
        processedAt: Date | null;
    }>;
    getUserSubscription(userId: any, appType: any): Promise<void | {
        startDate: Date;
        endDate: Date;
        productId: string;
        isCancelled: boolean;
        type: string;
    }>;
    processPurchaseWithIapValidation(app: any, userId: any, receipt: any): Promise<{
        startDate: Date;
        endDate: Date;
        productId: any;
        isCancelled: any;
    }>;
    processPurchaseWithoutIapValidation(app: any, userId: any, receipt: any): Promise<{
        startDate: Date;
        endDate: Date;
        productId: any;
        isCancelled: any;
    }>;
    processPurchase(app: any, userId: any, receipt: any): Promise<{
        startDate: Date;
        endDate: Date;
        productId: string;
        isCancelled: boolean;
        type: string;
    }>;
    purchaseGoldWithValidationIap(app: any, userId: any, receipt: any): Promise<{
        isCancelled: any;
        productId: any;
    }>;
    purchaseGoldWithoutValidationIap(app: any, userId: any, receipt: any): Promise<{
        isCancelled: any;
        productId: any;
    }>;
    purchaseGold(app: any, userId: any, receipt: any): Promise<{
        productId: string;
        isCancelled: boolean;
        type: string;
        balance: number;
    }>;
    validateAllSubscriptions(): Promise<void>;
    private validatePurchase;
    checkIfHasSubscription(subscription: any): boolean;
}
