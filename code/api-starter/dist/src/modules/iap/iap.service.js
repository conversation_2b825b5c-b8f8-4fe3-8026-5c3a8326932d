"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IapService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../shared/prisma/prisma.service");
const google_auth_library_1 = require("google-auth-library");
const googleapis_1 = require("googleapis");
const in_app_purchase_1 = __importDefault(require("in-app-purchase"));
const schedule_1 = require("@nestjs/schedule");
const moment_1 = __importDefault(require("moment"));
const assert_1 = __importDefault(require("assert"));
const config_service_1 = require("../../shared/config/config.service");
const env_utils_1 = require("../../shared/env/env.utils");
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
let IapService = class IapService {
    prisma;
    configService;
    androidGoogleApi = googleapis_1.google.androidpublisher({ version: 'v3' });
    androidPackageName;
    constructor(prisma, configService) {
        this.prisma = prisma;
        this.configService = configService;
        if (!(0, env_utils_1.isLocalEnv)() && configService.get('GOOGLE_SERVICE_ACCOUNT_EMAIL')) {
            googleapis_1.google.options({
                auth: new google_auth_library_1.JWT(configService.get('GOOGLE_SERVICE_ACCOUNT_EMAIL'), null, GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY, ['https://www.googleapis.com/auth/androidpublisher']),
            });
            const iapTestMode = configService.get('IAP_TEST_MODE') === 'true';
            this.androidPackageName = configService.get('ANDROID_PACKAGE_NAME');
            in_app_purchase_1.default.config({
                appleExcludeOldTransactions: true,
                applePassword: configService.get('APPLE_SHARED_SECRET'),
                googleServiceAccount: {
                    clientEmail: configService.get('GOOGLE_SERVICE_ACCOUNT_EMAIL'),
                    privateKey: GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY,
                },
                test: iapTestMode,
            });
        }
    }
    async getActiveSubscriptions() {
        return await this.prisma.subscription.findMany({
            where: { AND: [{ endDate: { gt: new Date() } }, { fake: false }] },
        });
    }
    async updateSubscription(subscription) {
        let subscriptionToCreate = JSON.parse(JSON.stringify(subscription));
        delete subscriptionToCreate.userId;
        subscriptionToCreate.user = { connect: { id: subscription.userId } };
        return await this.prisma.subscription.upsert({
            where: { origTxId: subscription.origTxId },
            create: {
                ...subscriptionToCreate,
                latestReceipt: JSON.stringify(subscriptionToCreate.latestReceipt),
            },
            update: {
                ...subscription,
                latestReceipt: JSON.stringify(subscription.latestReceipt),
            },
        });
    }
    async addGold(purchase) {
        const transactionValidation = {
            environment: purchase.environment,
            origTxId: purchase.origTxId,
            latestReceipt: JSON.stringify(purchase.latestReceipt),
            app: purchase.app,
            productId: purchase.productId,
            validationResponse: purchase.validationResponse,
        };
        const transactionToSave = {
            isSpent: false,
            amount: parseFloat(purchase.amount.toString()),
            userId: purchase.userId,
            transactionValidation: { create: transactionValidation },
        };
        const { balance } = await this.prisma.account.findUnique({
            where: { userId: purchase.userId },
        });
        const [transaction, user] = await this.prisma.$transaction([
            this.prisma.transaction.create({
                data: transactionToSave,
            }),
            this.prisma.account.update({
                where: { userId: purchase.userId },
                data: {
                    balance: parseFloat(balance.toString()) +
                        parseFloat(purchase.amount.toString()),
                },
            }),
        ]);
        return transaction;
    }
    async getUserSubscription(userId, appType) {
        if (!appType)
            return undefined;
        const [row] = await this.prisma.subscription.findMany({
            where: {
                AND: [{ userId: userId }, { app: appType }],
            },
            orderBy: { startDate: 'desc' },
            take: 1,
        });
        if (!row)
            return undefined;
        if (row &&
            row.validationResponse.autoRenewing &&
            (0, moment_1.default)(row.endDate).valueOf() < new Date().getTime()) {
            const validate = await this.validatePurchase({ ...row, userId });
            return validate;
        }
        return {
            startDate: row.startDate,
            endDate: row.endDate,
            productId: row.productId,
            isCancelled: !!row.isCancelled,
            type: 'iap',
        };
    }
    async processPurchaseWithIapValidation(app, userId, receipt) {
        await in_app_purchase_1.default.setup();
        const validationResponse = await in_app_purchase_1.default.validate(receipt);
        (0, assert_1.default)((app === 'android' && validationResponse.service === 'google') ||
            (app === 'ios' && validationResponse.service === 'apple'));
        const purchaseSection = in_app_purchase_1.default.getPurchaseSection(validationResponse);
        const firstPurchaseItem = purchaseSection[0];
        const isCancelled = in_app_purchase_1.default.isCanceled(firstPurchaseItem);
        const { productId } = firstPurchaseItem;
        const origTxId = app === 'ios'
            ? firstPurchaseItem.originalTransactionId
            : firstPurchaseItem.transactionId;
        const latestReceipt = app === 'ios'
            ? validationResponse.latest_receipt
            : JSON.stringify(receipt);
        const startDate = app === 'ios'
            ? new Date(firstPurchaseItem.originalPurchaseDateMs)
            : new Date(parseInt(firstPurchaseItem.startTimeMillis, 10));
        const endDate = app === 'ios'
            ? new Date(firstPurchaseItem.expiresDateMs)
            : new Date(parseInt(firstPurchaseItem.expiryTimeMillis, 10));
        let environment = '';
        if (app === 'ios') {
            environment = validationResponse.sandbox ? 'sandbox' : 'production';
        }
        await this.updateSubscription({
            userId,
            app,
            environment,
            productId,
            origTxId,
            latestReceipt,
            validationResponse,
            startDate,
            endDate,
            isCancelled,
        });
        if (app === 'android' && validationResponse.acknowledgementState === 0) {
            await this.androidGoogleApi.purchases.subscriptions.acknowledge({
                packageName: this.androidPackageName,
                subscriptionId: productId,
                token: receipt.purchaseToken,
            });
        }
        return { startDate, endDate, productId, isCancelled };
    }
    async processPurchaseWithoutIapValidation(app, userId, receipt) {
        const firstPurchaseItem = receipt;
        const { productId } = firstPurchaseItem;
        const origTxId = app === 'ios'
            ? firstPurchaseItem.originalTransactionId
            : firstPurchaseItem.transactionId;
        const latestReceipt = app === 'ios'
            ? firstPurchaseItem.latest_receipt
            : JSON.stringify(receipt);
        const startDate = app === 'ios'
            ? new Date(firstPurchaseItem.originalPurchaseDateMs)
            : new Date(parseInt(firstPurchaseItem.startTimeMillis, 10));
        const endDate = app === 'ios'
            ? new Date(firstPurchaseItem.expiresDateMs)
            : new Date(parseInt(firstPurchaseItem.expiryTimeMillis, 10));
        const isCancelled = firstPurchaseItem.cancellationDate;
        await this.updateSubscription({
            userId,
            app,
            environment: 'sandbox',
            productId,
            origTxId,
            latestReceipt,
            validationResponse: {},
            startDate,
            endDate,
            isCancelled,
        });
        return { startDate, endDate, productId, isCancelled };
    }
    async processPurchase(app, userId, receipt) {
        let objectOfpurchase = {
            startDate: new Date(),
            endDate: new Date(),
            productId: '',
            isCancelled: true,
        };
        if (!(0, env_utils_1.isSeedEnv)() && !(0, env_utils_1.isTestEnv)() && !(0, env_utils_1.isLocalEnv)()) {
            objectOfpurchase = await this.processPurchaseWithIapValidation(app, userId, receipt);
        }
        else {
            objectOfpurchase = await this.processPurchaseWithoutIapValidation(app, userId, receipt);
        }
        return {
            startDate: objectOfpurchase.startDate,
            endDate: objectOfpurchase.endDate,
            productId: objectOfpurchase.productId,
            isCancelled: !!objectOfpurchase.isCancelled,
            type: 'iap',
        };
    }
    async purchaseGoldWithValidationIap(app, userId, receipt) {
        await in_app_purchase_1.default.setup();
        const validationResponse = await in_app_purchase_1.default.validate(receipt?.service, receipt);
        (0, assert_1.default)((app === 'android' && validationResponse.service === 'google') ||
            (app === 'ios' && validationResponse.service === 'apple'));
        const purchaseSection = in_app_purchase_1.default.getPurchaseSection(validationResponse);
        const firstPurchaseItem = purchaseSection[0];
        const isCancelled = in_app_purchase_1.default.isCanceled(firstPurchaseItem);
        const { productId } = firstPurchaseItem;
        const origTxId = app === 'ios'
            ? firstPurchaseItem.originalTransactionId
            : firstPurchaseItem.transactionId;
        const latestReceipt = app === 'ios'
            ? validationResponse.latest_receipt
            : JSON.stringify(receipt);
        let environment = '';
        if (app === 'ios') {
            environment = validationResponse.sandbox ? 'sandbox' : 'production';
        }
        const amount = productId.replace(/[^0-9]/g, '');
        await this.addGold({
            userId,
            app,
            environment,
            productId,
            origTxId,
            latestReceipt,
            validationResponse,
            isCancelled,
            amount,
        });
        if (app === 'android' && validationResponse.acknowledgementState === 0) {
            await this.androidGoogleApi.purchases.products.acknowledge({
                packageName: this.androidPackageName,
                productId: productId,
                token: receipt.purchaseToken,
            });
        }
        return { isCancelled, productId };
    }
    async purchaseGoldWithoutValidationIap(app, userId, receipt) {
        const firstPurchaseItem = receipt;
        const { productId } = firstPurchaseItem;
        const origTxId = app === 'ios'
            ? firstPurchaseItem.originalTransactionId
            : firstPurchaseItem.transactionId;
        const isCancelled = firstPurchaseItem.cancellationDate;
        const latestReceipt = app === 'ios' ? receipt.latest_receipt : JSON.stringify(receipt);
        const amount = productId.replace(/[^0-9]/g, '');
        await this.addGold({
            userId,
            app,
            environment: 'sandbox',
            productId,
            origTxId,
            latestReceipt,
            validationResponse: {},
            isCancelled,
            amount,
        });
        return { isCancelled, productId };
    }
    async purchaseGold(app, userId, receipt) {
        let objectOfpurchase = { isCancelled: true, productId: '' };
        if (!(0, env_utils_1.isSeedEnv)() && !(0, env_utils_1.isTestEnv)() && !(0, env_utils_1.isLocalEnv)()) {
            objectOfpurchase = await this.purchaseGoldWithValidationIap(app, userId, receipt);
        }
        else {
            objectOfpurchase = await this.purchaseGoldWithoutValidationIap(app, userId, receipt);
        }
        const { balance } = await this.prisma.account.findUnique({
            where: { userId },
        });
        return {
            productId: objectOfpurchase.productId,
            isCancelled: !!objectOfpurchase.isCancelled,
            type: 'iap',
            balance,
        };
    }
    async validateAllSubscriptions() {
        const subscriptions = await this.getActiveSubscriptions();
        for (const subscription of subscriptions) {
            try {
                await this.validatePurchase(subscription);
            }
            catch (err) {
                console.error('Failed to validate subscription', subscription.id);
            }
        }
    }
    async validatePurchase(subscription) {
        if (subscription.app === 'ios') {
            await this.processPurchase(subscription.app, subscription.userId, subscription.latestReceipt);
        }
        else {
            await this.processPurchase(subscription.app, subscription.userId, JSON.parse(subscription.latestReceipt));
        }
    }
    checkIfHasSubscription(subscription) {
        if (!subscription)
            return false;
        if (subscription.isCancelled)
            return false;
        const nowMs = new Date().getTime();
        return ((0, moment_1.default)(subscription.startDate).valueOf() <= nowMs &&
            (0, moment_1.default)(subscription.endDate).valueOf() >= nowMs);
    }
};
exports.IapService = IapService;
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_DAY_AT_MIDNIGHT),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], IapService.prototype, "validateAllSubscriptions", null);
exports.IapService = IapService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        config_service_1.ConfigService])
], IapService);
//# sourceMappingURL=iap.service.js.map