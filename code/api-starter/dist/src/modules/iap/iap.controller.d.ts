import { User } from '@prisma/client';
import { IapService } from 'src/modules/iap/iap.service';
import { SaveReceiptParams } from 'src/modules/iap/iap.swagger';
export declare class IapController {
    private readonly iapService;
    constructor(iapService: IapService);
    save(params: SaveReceiptParams, user: User): Promise<{
        startDate: Date;
        endDate: Date;
        productId: string;
        isCancelled: boolean;
        type: string;
    }>;
    buyGold(params: SaveReceiptParams, user: User): Promise<{
        productId: string;
        isCancelled: boolean;
        type: string;
        balance: number;
    }>;
    read(appType: string, user: User): Promise<any>;
}
