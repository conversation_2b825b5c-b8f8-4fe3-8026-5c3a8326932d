"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CronController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const env_utils_1 = require("../../shared/env/env.utils");
const cron_service_1 = require("./cron.service");
let CronController = class CronController {
    cronService;
    constructor(cronService) {
        this.cronService = cronService;
    }
    async compute() {
        if ((0, env_utils_1.isSeedEnv)() || (0, env_utils_1.isLocalEnv)() || (0, env_utils_1.isDevelop)() || (0, env_utils_1.isStagingEnv)()) {
        }
        else {
            throw new common_1.ForbiddenException('This route is inaccessible in production environment');
        }
    }
};
exports.CronController = CronController;
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'computes ranks during seed' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'endpoint inaccessible' }),
    (0, swagger_1.ApiCreatedResponse)({ description: 'ranks created' }),
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CronController.prototype, "compute", null);
exports.CronController = CronController = __decorate([
    (0, common_1.Controller)('cron'),
    (0, swagger_1.ApiTags)('cron'),
    __metadata("design:paramtypes", [cron_service_1.CronService])
], CronController);
//# sourceMappingURL=cron.controller.js.map