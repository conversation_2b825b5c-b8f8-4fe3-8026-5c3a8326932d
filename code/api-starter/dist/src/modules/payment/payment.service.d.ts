import { Payment, Prisma, Transaction, ObjectType } from '@prisma/client';
import { AccountService } from '../account/account.service';
import { PrismaService } from 'src/shared/prisma/prisma.service';
import Stripe from 'stripe';
import { AccessService } from '../access/access.service';
export declare class paymentService {
    prisma: PrismaService;
    accountService: AccountService;
    accessService: AccessService;
    private stripe;
    constructor(prisma: PrismaService, accountService: AccountService, accessService: AccessService);
    getEmailByUserId(userId: string): Promise<string>;
    createSetupSession(userId: string, host: string): Promise<{
        url: string;
    }>;
    getCheckoutSession(sessionId: string): Promise<Stripe.Checkout.Session>;
    createPaymentSession(userId: string, plan: string, idempotencyKey: string, host: string, objectId: string, objectType: ObjectType): Promise<{
        url: string;
    }>;
    createCartPaymentSession(userId: string, cartItems: {
        id: string;
        name: string;
        price: number;
        quantity: number;
    }[], idempotencyKey: string, host: string): Promise<{
        url: string;
    }>;
    createSubscriptionSession(userId: string, plan: string, idempotencyKey: string, host: string): Promise<{
        url: string;
    }>;
    handleStripeWebhook(body: string | Buffer, headers: string): Promise<{
        received: boolean;
    }>;
    create(data: Prisma.PaymentCreateInput): Promise<Payment>;
    get(where: Prisma.PaymentWhereUniqueInput): Promise<Payment>;
    delete(where: Prisma.PaymentWhereUniqueInput): Promise<Payment>;
    find({ where, orderBy, skip, take, }: Prisma.PaymentFindManyArgs): Promise<Payment[]>;
    transactions({ where, }: Prisma.TransactionFindManyArgs): Promise<Transaction[]>;
    update(where: Prisma.PaymentWhereUniqueInput, params: Prisma.PaymentUpdateInput): Promise<Payment>;
    private handleCartPaymentIntentSucceeded;
    private handlePaymentIntentSucceeded;
    private handleCreditPaymentIntentSucceeded;
    private handlePaymentIntentFailed;
    private handleCheckoutSessionCompleted;
    private handleInvoicePaymentSucceeded;
    private handleInvoicePaymentFailed;
}
