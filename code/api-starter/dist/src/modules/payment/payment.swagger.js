"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateHistoryParams = exports.CreateContentParams = exports.UpdateSourceParams = exports.FindSourceParams = exports.CreateSourceParams = exports.UpdatePaymentParams = exports.FindPaymentParams = exports.CreateCartPaymentParams = exports.CreateSubscriptionParams = exports.CreateOneTimePaymentParams = exports.CreatePaymentParams = void 0;
const swagger_1 = require("@nestjs/swagger");
const client_1 = require("@prisma/client");
const class_validator_1 = require("class-validator");
class CreatePaymentParams {
    date;
}
exports.CreatePaymentParams = CreatePaymentParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Date of the payment',
        example: new Date(),
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Date)
], CreatePaymentParams.prototype, "date", void 0);
class CreateOneTimePaymentParams {
    plan;
    host;
    objectId;
    objectType;
}
exports.CreateOneTimePaymentParams = CreateOneTimePaymentParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Subscription plan',
        example: 'mock.word',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateOneTimePaymentParams.prototype, "plan", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Host Url',
        example: 'mock.url',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateOneTimePaymentParams.prototype, "host", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Id of the resource',
        example: 'mock.id',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateOneTimePaymentParams.prototype, "objectId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Type of the resource',
        example: 'post',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateOneTimePaymentParams.prototype, "objectType", void 0);
class CreateSubscriptionParams {
    plan;
    host;
    idempotencyKey;
}
exports.CreateSubscriptionParams = CreateSubscriptionParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Subscription plan',
        example: 'mock.word',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateSubscriptionParams.prototype, "plan", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Host Url',
        example: 'mock.url',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateSubscriptionParams.prototype, "host", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'idempotencyKey',
        example: 'mock.id',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateSubscriptionParams.prototype, "idempotencyKey", void 0);
class CartItem {
    id;
    name;
    price;
    quantity;
}
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'mock.id',
        description: 'Unique ID of the item',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CartItem.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Laptop', description: 'Product name' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CartItem.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'mock.integer', description: 'Price of the product' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CartItem.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'mock.integer',
        description: 'Quantity of the product',
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CartItem.prototype, "quantity", void 0);
const mockCart = [
    {
        id: 'item_123',
        name: 'Mock Product',
        price: 19.99,
        quantity: 2,
    },
    {
        id: 'item_456',
        name: 'Another Product',
        price: 49.99,
        quantity: 1,
    },
];
class CreateCartPaymentParams {
    cartItems;
    host;
    idempotencyKey;
}
exports.CreateCartPaymentParams = CreateCartPaymentParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Items of the cart',
        example: JSON.stringify(mockCart),
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    __metadata("design:type", Array)
], CreateCartPaymentParams.prototype, "cartItems", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Host Url',
        example: 'mock.url',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateCartPaymentParams.prototype, "host", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'idempotencyKey',
        example: 'mock.id',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateCartPaymentParams.prototype, "idempotencyKey", void 0);
class FindPaymentParams {
    name;
    take;
    skip;
    sortField;
    sortOrder;
}
exports.FindPaymentParams = FindPaymentParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: '',
        example: 'mock.name',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FindPaymentParams.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Number or result to return',
        example: 10,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], FindPaymentParams.prototype, "take", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Number or result to skip',
        example: 0,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], FindPaymentParams.prototype, "skip", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Order by field ',
        enum: Object.keys(client_1.Prisma.NewsScalarFieldEnum),
    }),
    (0, class_validator_1.IsEnum)(Object.keys(client_1.Prisma.NewsScalarFieldEnum)),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], FindPaymentParams.prototype, "sortField", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Order sort',
        enum: client_1.Prisma.SortOrder,
    }),
    (0, class_validator_1.IsEnum)(client_1.Prisma.SortOrder),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], FindPaymentParams.prototype, "sortOrder", void 0);
class UpdatePaymentParams {
    name;
}
exports.UpdatePaymentParams = UpdatePaymentParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Payment title',
        example: 'mock.name',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdatePaymentParams.prototype, "name", void 0);
class CreateSourceParams {
    url;
    paymentId;
}
exports.CreateSourceParams = CreateSourceParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Source url',
        example: 'https://www.theaivalley.com/',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateSourceParams.prototype, "url", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Payment Id',
        example: '',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateSourceParams.prototype, "paymentId", void 0);
class FindSourceParams {
    paymentId;
    take;
    skip;
    sortField;
    sortOrder;
}
exports.FindSourceParams = FindSourceParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: '',
        example: 'clvdj60m10001ci0bkhat2i1e',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FindSourceParams.prototype, "paymentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Number or result to return',
        example: 10,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], FindSourceParams.prototype, "take", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Number or result to skip',
        example: 0,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], FindSourceParams.prototype, "skip", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Order by field ',
        enum: Object.keys(client_1.Prisma.NewsScalarFieldEnum),
    }),
    (0, class_validator_1.IsEnum)(Object.keys(client_1.Prisma.NewsScalarFieldEnum)),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], FindSourceParams.prototype, "sortField", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Order sort',
        enum: client_1.Prisma.SortOrder,
    }),
    (0, class_validator_1.IsEnum)(client_1.Prisma.SortOrder),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], FindSourceParams.prototype, "sortOrder", void 0);
class UpdateSourceParams {
    url;
}
exports.UpdateSourceParams = UpdateSourceParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Payment title',
        example: 'mock.name',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateSourceParams.prototype, "url", void 0);
class CreateContentParams {
    language;
    tone;
    style;
}
exports.CreateContentParams = CreateContentParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Language of the payment content',
        example: 'French',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateContentParams.prototype, "language", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Tone of the payment content',
        example: 'Captivating narrative that ties individual themes into a larger, cohesive story',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateContentParams.prototype, "tone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Style of the payment content',
        example: 'Accessible language. Enriching content. Aim to spark curiosity and provide meaningful insights to the readers.',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateContentParams.prototype, "style", void 0);
class CreateHistoryParams {
    prompt;
}
exports.CreateHistoryParams = CreateHistoryParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'The user prompt',
        example: 'Add a date',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateHistoryParams.prototype, "prompt", void 0);
//# sourceMappingURL=payment.swagger.js.map