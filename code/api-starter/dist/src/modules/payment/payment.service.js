"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.paymentService = void 0;
const common_1 = require("@nestjs/common");
const account_service_1 = require("../account/account.service");
const prisma_service_1 = require("../../shared/prisma/prisma.service");
const stripe_1 = __importDefault(require("stripe"));
const plan_config_1 = require("../../shared/config/plan.config");
const moment_1 = __importDefault(require("moment"));
const access_service_1 = require("../access/access.service");
let paymentService = class paymentService {
    prisma;
    accountService;
    accessService;
    stripe;
    constructor(prisma, accountService, accessService) {
        this.prisma = prisma;
        this.accountService = accountService;
        this.accessService = accessService;
        this.stripe = new stripe_1.default(process.env.SECRET_STRIPE_KEY, {
            apiVersion: '2024-04-10',
        });
    }
    async getEmailByUserId(userId) {
        const account = await this.prisma.account.findUnique({
            where: { userId },
        });
        if (!account || !account.email) {
            throw new common_1.NotFoundException('Email not found for the given user ID');
        }
        return account.email;
    }
    async createSetupSession(userId, host) {
        try {
            const account = await this.prisma.account.findUnique({
                where: { userId },
            });
            const stripeSession = await this.stripe.checkout.sessions.create({
                success_url: `${host}/plan?success=true`,
                cancel_url: `${host}/plan?canceled=true`,
                payment_method_types: ['card'],
                mode: 'setup',
                billing_address_collection: 'auto',
                customer: account.stripeCustomerId,
                metadata: {
                    userId,
                },
            });
            return { url: stripeSession.url };
        }
        catch (error) {
            console.log('[STRIPE_ERROR]', error);
        }
    }
    async getCheckoutSession(sessionId) {
        return await this.stripe.checkout.sessions.retrieve(sessionId);
    }
    async createPaymentSession(userId, plan, idempotencyKey, host, objectId, objectType) {
        try {
            const planDetails = plan_config_1.PLAN_CONFIG.options[plan];
            if (!planDetails) {
                throw new Error('Invalid plan selected');
            }
            const account = await this.prisma.account.findUnique({
                where: { userId },
            });
            let stripeCustomerId = account?.stripeCustomerId;
            if (!stripeCustomerId) {
                const customer = await this.stripe.customers.create({
                    email: account?.email,
                    metadata: { userId },
                });
                stripeCustomerId = customer.id;
                await this.prisma.account.update({
                    where: { userId },
                    data: { stripeCustomerId },
                });
            }
            const stripeSession = await this.stripe.checkout.sessions.create({
                success_url: host,
                cancel_url: `${host}?canceled=true`,
                payment_method_types: ['card'],
                mode: 'payment',
                billing_address_collection: 'auto',
                customer: stripeCustomerId,
                line_items: [
                    {
                        price_data: {
                            currency: planDetails.currency,
                            product_data: {
                                name: plan,
                                description: `One-time payment for ${plan} plan`,
                            },
                            unit_amount: planDetails.price * 100,
                        },
                        quantity: 1,
                    },
                ],
                metadata: {
                    userId,
                    idempotencyKey,
                    objectType,
                    objectId,
                    plan,
                    type: plan_config_1.PLAN_CONFIG.paymentType,
                },
            });
            const transaction = await this.prisma.transaction.create({
                data: {
                    id: idempotencyKey,
                    userId,
                    amount: planDetails.price,
                    status: 'PENDING',
                    description: `One-time payment for ${plan}`,
                    isSpent: false,
                },
            });
            await this.prisma.account.update({
                where: { userId },
                data: {
                    currentTransactionId: transaction.id,
                },
            });
            return { url: stripeSession.url };
        }
        catch (error) {
            console.log('[STRIPE_ERROR]', error);
            throw new Error('Failed to create Stripe payment session');
        }
    }
    async createCartPaymentSession(userId, cartItems, idempotencyKey, host) {
        try {
            if (!cartItems || cartItems.length === 0) {
                throw new Error('Cart is empty');
            }
            const account = await this.prisma.account.findUnique({
                where: { userId },
            });
            let stripeCustomerId = account?.stripeCustomerId;
            if (!stripeCustomerId) {
                const customer = await this.stripe.customers.create({
                    email: account?.email,
                    metadata: { userId },
                });
                stripeCustomerId = customer.id;
                await this.prisma.account.update({
                    where: { userId },
                    data: { stripeCustomerId },
                });
            }
            const lineItems = cartItems.map(item => ({
                price_data: {
                    currency: plan_config_1.PLAN_CONFIG?.currency || 'usd',
                    product_data: {
                        name: item.name,
                        description: `Purchased ${item.name}`,
                    },
                    unit_amount: Math.round(item.price * 100),
                },
                quantity: item.quantity,
            }));
            const productIds = cartItems.map(item => item.id);
            const stripeSession = await this.stripe.checkout.sessions.create({
                success_url: `${host}?success=true`,
                cancel_url: `${host}/cancel`,
                payment_method_types: ['card'],
                mode: 'payment',
                billing_address_collection: 'auto',
                customer: stripeCustomerId,
                line_items: lineItems,
                metadata: {
                    userId,
                    idempotencyKey,
                    type: 'cart',
                    objectType: plan_config_1.PLAN_CONFIG?.paymentEntity,
                    customer: stripeCustomerId,
                    productIds: JSON.stringify(productIds),
                },
            });
            const totalAmount = cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
            const transaction = await this.prisma.transaction.create({
                data: {
                    id: idempotencyKey,
                    userId,
                    amount: totalAmount,
                    status: 'PENDING',
                    description: `Payment for ${cartItems.length} items`,
                    isSpent: false,
                },
            });
            await this.prisma.account.update({
                where: { userId },
                data: {
                    currentTransactionId: transaction.id,
                },
            });
            return { url: stripeSession.url };
        }
        catch (error) {
            console.log('[STRIPE_ERROR]', error);
            throw new Error('Failed to create Stripe payment session');
        }
    }
    async createSubscriptionSession(userId, plan, idempotencyKey, host) {
        try {
            const planDetails = plan_config_1.PLAN_CONFIG.options[plan];
            if (!planDetails) {
                throw new Error('Invalid plan selected');
            }
            const account = await this.prisma.account.findUnique({
                where: { userId },
            });
            let stripeCustomerId = account.stripeCustomerId;
            const existingSubscriptions = await this.stripe.subscriptions.list({
                customer: stripeCustomerId,
                status: 'active',
            });
            if (existingSubscriptions.data.length > 0) {
                for (const subscription of existingSubscriptions.data) {
                    if (subscription.status === 'active') {
                        await this.stripe.subscriptions.cancel(subscription.id);
                    }
                }
            }
            const stripeSession = await this.stripe.checkout.sessions.create({
                success_url: host,
                cancel_url: `${host}?canceled=true`,
                payment_method_types: ['card'],
                mode: 'subscription',
                billing_address_collection: 'auto',
                customer: stripeCustomerId,
                line_items: [
                    {
                        price_data: {
                            currency: planDetails.currency,
                            product_data: {
                                name: plan,
                                description: `Subscription for the ${plan} plan`,
                            },
                            unit_amount: planDetails.price * 100,
                            recurring: { interval: planDetails.interval },
                        },
                        quantity: 1,
                    },
                ],
                metadata: {
                    userId,
                    idempotencyKey,
                },
            });
            const transation = await this.prisma.transaction.create({
                data: {
                    id: idempotencyKey,
                    amount: plan_config_1.PLAN_CONFIG.options[plan].price,
                    status: 'PENDING',
                    description: `Subscription for plan ${plan}`,
                    isSpent: false,
                    userId,
                },
            });
            await this.prisma.account.update({
                where: { userId: userId },
                data: {
                    currentTransactionId: transation.id,
                },
            });
            return { url: stripeSession.url };
        }
        catch (error) {
            console.log('[STRIPE_ERROR]', error);
        }
    }
    async handleStripeWebhook(body, headers) {
        let event;
        try {
            event = await this.stripe.webhooks.constructEvent(body, headers, process.env.STRIPE_WEBHOOK_SECRET);
        }
        catch (error) {
            console.log(`⚠️  Webhook signature verification failed.`, error.message);
            throw new common_1.NotFoundException('Webhook signature verification failed');
        }
        switch (event.type) {
            case 'checkout.session.completed':
                const session = event.data.object;
                await this.handleCheckoutSessionCompleted(session);
                break;
            case 'payment_intent.succeeded':
                const paymentIntentSucceeded = event.data
                    .object;
                const paymentType = paymentIntentSucceeded.metadata.type;
                if (paymentType === 'credit') {
                    await this.handleCreditPaymentIntentSucceeded(paymentIntentSucceeded);
                }
                break;
            case 'setup_intent.succeeded':
                const SetupIntentSucceeded = event.data.object;
                console.log({ SetupIntentSucceeded });
                break;
            case 'payment_intent.payment_failed':
                const paymentIntentFailed = event.data.object;
                await this.handlePaymentIntentFailed(paymentIntentFailed);
                break;
            case 'invoice.payment_succeeded':
                const invoicePaymentSucceeded = event.data.object;
                await this.handleInvoicePaymentSucceeded(invoicePaymentSucceeded);
                break;
            case 'invoice.payment_failed':
                const invoicePaymentFailed = event.data.object;
                await this.handleInvoicePaymentFailed(invoicePaymentFailed);
                break;
            default:
                console.log(`Unhandled event type ${event.type}`);
        }
        return { received: true };
    }
    async create(data) {
        return await this.prisma.payment.create({ data });
    }
    async get(where) {
        const payment = await this.prisma.payment.findUnique({
            where,
        });
        return payment;
    }
    async delete(where) {
        const exists = await this.prisma.payment.findUnique({ where });
        if (!exists)
            throw new common_1.NotFoundException();
        return await this.prisma.payment.delete({ where });
    }
    async find({ where, orderBy, skip = 0, take = 10, }) {
        return await this.prisma.payment.findMany({
            where,
            orderBy,
            skip,
            take,
        });
    }
    async transactions({ where, }) {
        return await this.prisma.transaction.findMany({
            where,
        });
    }
    async update(where, params) {
        const exists = await this.prisma.payment.findUnique({ where });
        if (!exists)
            throw new common_1.NotFoundException();
        return await this.prisma.payment.update({
            data: params,
            where,
        });
    }
    async handleCartPaymentIntentSucceeded(paymentIntend) {
        try {
            const { userId, objectType, productIds } = paymentIntend;
            for (const objectId of JSON.parse(productIds)) {
                if (objectId && objectType) {
                    await this.accessService.create({
                        userId,
                        objectId,
                        objectType,
                    });
                }
            }
            await this.prisma.payment.create({
                data: {
                    account: { connect: { userId } },
                    date: moment_1.default.utc(paymentIntend.created * 1000).toDate(),
                    metadata: {
                        stripeCustomerId: paymentIntend.customer,
                        price: paymentIntend.amount / 100,
                        type: paymentIntend.type,
                        idempotencyKey: paymentIntend.idempotencyKey,
                        productIds: paymentIntend.cartIds,
                    },
                },
            });
        }
        catch (error) {
            console.error('Error in handlePaymentIntentSucceeded', error);
        }
    }
    async handlePaymentIntentSucceeded(paymentIntend) {
        try {
            const { userId, objectId, objectType } = paymentIntend;
            if (objectId && objectType) {
                await this.accessService.create({
                    userId,
                    objectId,
                    objectType,
                });
            }
            await this.prisma.payment.create({
                data: {
                    account: { connect: { userId } },
                    date: moment_1.default.utc(paymentIntend.created * 1000).toDate(),
                    metadata: {
                        stripeCustomerId: paymentIntend.customer,
                        price: paymentIntend.amount / 100,
                        type: paymentIntend.type,
                        idempotencyKey: paymentIntend.idempotencyKey,
                        plan: paymentIntend?.plan ?? null,
                    },
                },
            });
        }
        catch (error) {
            console.error('Error in handlePaymentIntentSucceeded', error);
        }
    }
    async handleCreditPaymentIntentSucceeded(paymentIntent) {
        const { userId, plan, idempotencyKey } = paymentIntent.metadata;
        const credits = plan_config_1.PLAN_CONFIG.options[plan]?.credits;
        if (!credits) {
            console.error('Invalid plan:', plan);
            return;
        }
        let transaction;
        try {
            await this.prisma.creditHistory.create({
                data: {
                    change: credits,
                    userId,
                    counted: false,
                },
            });
            await this.accountService.updateCreditHistory(userId, idempotencyKey);
            transaction = await this.prisma.transaction.update({
                where: { id: idempotencyKey },
                data: {
                    status: 'COMPLETED',
                    processedAt: moment_1.default.utc().toDate(),
                },
            });
        }
        catch (error) {
            console.error('Error updating credit history:', error);
            transaction = await this.prisma.transaction.update({
                where: { id: idempotencyKey },
                data: {
                    status: 'FAILED',
                },
            });
        }
        console.log({ transaction });
    }
    async handlePaymentIntentFailed(paymentIntent) {
        const { idempotencyKey } = paymentIntent.metadata;
        try {
            await this.prisma.transaction.update({
                where: { id: idempotencyKey },
                data: {
                    status: 'FAILED',
                },
            });
        }
        catch (error) {
            console.error('Error updating transaction status to FAILED:', error);
        }
    }
    async handleCheckoutSessionCompleted(session) {
        if (session.mode === 'setup') {
            const setupIntentId = session.setup_intent;
            const setupIntent = await this.stripe.setupIntents.retrieve(setupIntentId);
            const paymentMethodId = setupIntent.payment_method;
            const userId = session.metadata.userId;
            if (userId && paymentMethodId) {
                await this.stripe.paymentMethods.attach(paymentMethodId, {
                    customer: session.customer,
                });
                await this.stripe.customers.update(session.customer, {
                    invoice_settings: {
                        default_payment_method: paymentMethodId,
                    },
                });
            }
        }
        else {
            await this.prisma.transaction.update({
                where: { id: session.metadata.idempotencyKey },
                data: {
                    status: 'COMPLETED',
                    processedAt: moment_1.default.utc().toDate(),
                },
            });
            if (session.mode === 'payment' && session.payment_status === 'paid') {
                const paymentIntent = {
                    ...session.metadata,
                    created: session.created,
                    customer: session.customer,
                    amount: session.amount_total,
                };
                if (session.metadata.type === 'one-time-payment') {
                    this.handlePaymentIntentSucceeded(paymentIntent);
                }
                if (session.metadata.type === 'cart') {
                    this.handleCartPaymentIntentSucceeded(paymentIntent);
                }
            }
        }
    }
    async handleInvoicePaymentSucceeded(invoice) {
        const customerId = invoice.customer;
        const account = await this.prisma.account.findUnique({
            where: { stripeCustomerId: customerId },
        });
        const userId = account.userId;
        const subscriptionId = invoice?.subscription;
        const subscription = await this.stripe.subscriptions.retrieve(subscriptionId);
        const { current_period_start, items, id, customer, current_period_end } = subscription;
        const priceId = items.data[0].price.id;
        const price = await this.stripe.prices.retrieve(priceId);
        const productId = price.product;
        const product = await this.stripe.products.retrieve(productId);
        await this.prisma.payment.create({
            data: {
                account: { connect: { userId } },
                date: moment_1.default.utc(invoice.created * 1000).toDate(),
                metadata: {
                    stripeCustomerId: customer,
                    stripeSubscriptionId: id,
                    stripePriceId: items.data[0].price.id,
                    stripeCurrentPeriodEnd: moment_1.default
                        .utc(current_period_end * 1000)
                        .toDate(),
                },
            },
        });
        await this.prisma.account.update({
            where: { userId: userId },
            data: {
                subscription: product.name,
                startSubscriptionDate: moment_1.default.utc(current_period_start * 1000).toDate(),
                endSubscriptionDate: moment_1.default.utc(current_period_end * 1000).toDate(),
            },
        });
    }
    async handleInvoicePaymentFailed(invoice) {
        const customerId = invoice.customer;
        const account = await this.prisma.account.findUnique({
            where: { stripeCustomerId: customerId },
        });
        await this.prisma.account.update({
            where: { userId: account.userId },
            data: {
                subscription: 'free',
            },
        });
    }
};
exports.paymentService = paymentService;
exports.paymentService = paymentService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        account_service_1.AccountService,
        access_service_1.AccessService])
], paymentService);
//# sourceMappingURL=payment.service.js.map