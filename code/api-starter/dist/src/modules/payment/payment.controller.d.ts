import { Payment, Transaction, User } from '@prisma/client';
import { paymentService } from 'src/modules/payment/payment.service';
import { CreateCartPaymentParams, CreateOneTimePaymentParams, CreatePaymentParams, CreateSubscriptionParams } from 'src/modules/payment/payment.swagger';
import Strip<PERSON> from 'stripe';
export declare class PaymentController {
    private readonly paymentService;
    dynamicField: any[];
    constructor(paymentService: paymentService);
    stripeSetupSession(user: User, host: string): Promise<{
        url: string;
    }>;
    stripeSubscriptionSession(user: User, params: CreateSubscriptionParams): Promise<{
        url: string;
    }>;
    stripeCartPaymentSession(user: User, params: CreateCartPaymentParams): Promise<{
        url: string;
    }>;
    stripePaymentIntentSession(user: User, params: CreateOneTimePaymentParams): Promise<{
        url: string;
    }>;
    getSession(id: string): Promise<Stripe.Checkout.Session>;
    handleWebhook(rawBody: Buffer, signature: string): Promise<{
        received: boolean;
    }>;
    generate(user: User, payment: CreatePaymentParams): Promise<Payment>;
    transactions(user: User): Promise<Transaction[]>;
    read(id: string): Promise<Payment>;
    delete(id: string): Promise<boolean>;
}
