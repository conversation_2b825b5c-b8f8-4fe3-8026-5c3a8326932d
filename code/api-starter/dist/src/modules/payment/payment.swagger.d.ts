import { ObjectType, Prisma } from '@prisma/client';
export declare class CreatePaymentParams {
    date: Date;
}
export declare class CreateOneTimePaymentParams {
    plan: string;
    host: string;
    objectId: string;
    objectType: ObjectType;
}
export declare class CreateSubscriptionParams {
    plan: string;
    host: string;
    idempotencyKey: string;
}
declare class CartItem {
    id: string;
    name: string;
    price: number;
    quantity: number;
}
export declare class CreateCartPaymentParams {
    cartItems: CartItem[];
    host: string;
    idempotencyKey: string;
}
export declare class FindPaymentParams {
    name: string;
    take: number;
    skip: number;
    sortField: string;
    sortOrder: Prisma.SortOrder;
}
export declare class UpdatePaymentParams {
    name: string;
}
export declare class CreateSourceParams {
    url: string;
    paymentId: string;
}
export declare class FindSourceParams {
    paymentId: string;
    take: number;
    skip: number;
    sortField: string;
    sortOrder: Prisma.SortOrder;
}
export declare class UpdateSourceParams {
    url: string;
}
export declare class CreateContentParams {
    language: string;
    tone: string;
    style: string;
}
export declare class CreateHistoryParams {
    prompt: string;
}
export {};
