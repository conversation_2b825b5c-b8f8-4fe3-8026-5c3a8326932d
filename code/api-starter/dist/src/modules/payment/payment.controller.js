"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const acl_decorator_1 = require("../../acl/acl.decorator");
const acl_guard_1 = require("../../acl/acl.guard");
const client_1 = require("@prisma/client");
const auth_guard_1 = require("../auth/auth.guard");
const payment_service_1 = require("./payment.service");
const payment_swagger_1 = require("./payment.swagger");
const story = __importStar(require("../../../specs/story/payment"));
const auth_decorator_1 = require("../auth/auth.decorator");
const uuid_1 = require("uuid");
let PaymentController = class PaymentController {
    paymentService;
    dynamicField = [];
    constructor(paymentService) {
        this.paymentService = paymentService;
    }
    async stripeSetupSession(user, host) {
        return await this.paymentService.createSetupSession(user.id, host);
    }
    async stripeSubscriptionSession(user, params) {
        return await this.paymentService.createSubscriptionSession(user.id, params.plan, params.idempotencyKey, params.host);
    }
    async stripeCartPaymentSession(user, params) {
        return await this.paymentService.createCartPaymentSession(user.id, params.cartItems, params.idempotencyKey, params.host);
    }
    async stripePaymentIntentSession(user, params) {
        const idempotencyKey = (0, uuid_1.v4)();
        return await this.paymentService.createPaymentSession(user.id, params.plan, idempotencyKey, params.host, params.objectId, params.objectType);
    }
    async getSession(id) {
        return await this.paymentService.getCheckoutSession(id);
    }
    async handleWebhook(rawBody, signature) {
        return this.paymentService.handleStripeWebhook(rawBody, signature);
    }
    async generate(user, payment) {
        const data = { ...payment, account: { connect: { userId: user.id } } };
        return await this.paymentService.create(data);
    }
    async transactions(user) {
        const result = await this.paymentService.transactions({
            where: {
                userId: user.id,
                status: client_1.TransactionStatus.PENDING,
                createdAt: {
                    gte: new Date(new Date().getTime() - 24 * 60 * 60 * 1000),
                },
            },
        });
        return result;
    }
    async read(id) {
        return await this.paymentService.get({ id });
    }
    async delete(id) {
        await this.paymentService.delete({ id });
        return true;
    }
};
exports.PaymentController = PaymentController;
__decorate([
    (0, common_1.Post)('/stripe/setup'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.paymentToCreate.access),
    (0, swagger_1.ApiOperation)(story.paymentToCreate.operation),
    (0, swagger_1.ApiResponse)(story.paymentToCreate.codes['201'].response),
    (0, swagger_1.ApiResponse)(story.paymentToCreate.codes['401'].response),
    __param(0, (0, auth_decorator_1.ReqUser)()),
    __param(1, (0, common_1.Headers)('origin')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "stripeSetupSession", null);
__decorate([
    (0, common_1.Post)('/stripe/subscription'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.paymentToCreateSubscription.access),
    (0, swagger_1.ApiOperation)(story.paymentToCreateSubscription.operation),
    (0, swagger_1.ApiResponse)(story.paymentToCreateSubscription.codes['201'].response),
    (0, swagger_1.ApiResponse)(story.paymentToCreateSubscription.codes['401'].response),
    __param(0, (0, auth_decorator_1.ReqUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, payment_swagger_1.CreateSubscriptionParams]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "stripeSubscriptionSession", null);
__decorate([
    (0, common_1.Post)('/stripe/cart/payment'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.paymentToCreateCartPayment.access),
    (0, swagger_1.ApiOperation)(story.paymentToCreateCartPayment.operation),
    (0, swagger_1.ApiResponse)(story.paymentToCreateCartPayment.codes['201'].response),
    (0, swagger_1.ApiResponse)(story.paymentToCreateCartPayment.codes['401'].response),
    __param(0, (0, auth_decorator_1.ReqUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, payment_swagger_1.CreateCartPaymentParams]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "stripeCartPaymentSession", null);
__decorate([
    (0, common_1.Post)('/stripe/one-time-payment'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.paymentToCreateResource.access),
    (0, swagger_1.ApiOperation)(story.paymentToCreateResource.operation),
    (0, swagger_1.ApiResponse)(story.paymentToCreateResource.codes['201'].response),
    (0, swagger_1.ApiResponse)(story.paymentToCreateResource.codes['401'].response),
    __param(0, (0, auth_decorator_1.ReqUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, payment_swagger_1.CreateOneTimePaymentParams]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "stripePaymentIntentSession", null);
__decorate([
    (0, common_1.Get)('session/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "getSession", null);
__decorate([
    (0, common_1.Post)('webhook'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Headers)('stripe-signature')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Buffer, String]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "handleWebhook", null);
__decorate([
    (0, common_1.Post)(''),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.paymentToCreate.access),
    (0, swagger_1.ApiOperation)(story.paymentToCreate.operation),
    (0, swagger_1.ApiResponse)(story.paymentToCreate.codes['201'].response),
    (0, swagger_1.ApiResponse)(story.paymentToCreate.codes['401'].response),
    __param(0, (0, auth_decorator_1.ReqUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, payment_swagger_1.CreatePaymentParams]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "generate", null);
__decorate([
    (0, common_1.Get)('transactions'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.paymentToReadTransactions.access),
    (0, swagger_1.ApiOperation)(story.paymentToReadTransactions.operation),
    (0, swagger_1.ApiResponse)(story.paymentToReadTransactions.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.paymentToReadTransactions.codes['404'].response),
    __param(0, (0, auth_decorator_1.ReqUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "transactions", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.paymentToRead.access),
    (0, swagger_1.ApiOperation)(story.paymentToRead.operation),
    (0, swagger_1.ApiResponse)(story.paymentToRead.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.paymentToRead.codes['404'].response),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "read", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.paymentToDelete.access),
    (0, swagger_1.ApiOperation)(story.paymentToDelete.operation),
    (0, swagger_1.ApiResponse)(story.paymentToDelete.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.paymentToDelete.codes['403'].response),
    (0, swagger_1.ApiResponse)(story.paymentToDelete.codes['404'].response),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "delete", null);
exports.PaymentController = PaymentController = __decorate([
    (0, common_1.Controller)('payment'),
    (0, swagger_1.ApiTags)('payment'),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [payment_service_1.paymentService])
], PaymentController);
//# sourceMappingURL=payment.controller.js.map