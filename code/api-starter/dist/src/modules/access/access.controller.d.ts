import { Access, Prisma, User } from '@prisma/client';
import { AccessService } from './access.service';
import { CreateAccessParams, FindAccessParams, UpdateAccessParams } from './access.swagger';
export declare class AccessController {
    private readonly accessService;
    constructor(accessService: AccessService);
    create(user: User, access: CreateAccessParams): Promise<Access>;
    whereToPrisma(params: FindAccessParams): Prisma.AccessWhereInput;
    find(params: FindAccessParams): Promise<any>;
    read(id: string): Promise<any>;
    deleteAll(): Promise<{
        success: boolean;
        count: number;
    }>;
    delete(id: string): Promise<any>;
    update(id: string, params: UpdateAccessParams): Promise<Access>;
}
