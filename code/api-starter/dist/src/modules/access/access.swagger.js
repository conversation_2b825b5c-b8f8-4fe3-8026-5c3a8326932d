"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateAccessParams = exports.CreateAccessParams = exports.ListAccessParams = exports.FindAccessParams = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const client_1 = require("@prisma/client");
const client_2 = require("@prisma/client");
class FindAccessParams {
    objectType;
    objectId;
    sortField;
    sortOrder;
    take;
    skip;
}
exports.FindAccessParams = FindAccessParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'ObjectType :' + Object.keys(client_1.ObjectType).join(','),
        example: 'post',
    }),
    (0, class_validator_1.IsEnum)(Object.keys(client_1.ObjectType)),
    __metadata("design:type", String)
], FindAccessParams.prototype, "objectType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Object Id',
        example: 'mock.id',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FindAccessParams.prototype, "objectId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Order by field',
        enum: Object.keys(client_2.Prisma.ExempleScalarFieldEnum),
    }),
    (0, class_validator_1.IsEnum)(Object.keys(client_2.Prisma.ExempleScalarFieldEnum)),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], FindAccessParams.prototype, "sortField", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Order sort',
        enum: client_2.Prisma.SortOrder,
    }),
    (0, class_validator_1.IsEnum)(client_2.Prisma.SortOrder),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], FindAccessParams.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Number or result to return',
        example: 10,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], FindAccessParams.prototype, "take", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Number or result to skip',
        example: 0,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], FindAccessParams.prototype, "skip", void 0);
class ListAccessParams {
    objectType;
    objectId;
}
exports.ListAccessParams = ListAccessParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'ObjectType :' + Object.keys(client_1.ObjectType).join(','),
        example: 'post',
    }),
    (0, class_validator_1.IsEnum)(Object.keys(client_1.ObjectType)),
    __metadata("design:type", String)
], ListAccessParams.prototype, "objectType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: '',
        example: 'mock.id',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListAccessParams.prototype, "objectId", void 0);
class CreateAccessParams {
    objectId;
    objectType;
}
exports.CreateAccessParams = CreateAccessParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Object Id',
        example: 'mock.id',
    }),
    __metadata("design:type", String)
], CreateAccessParams.prototype, "objectId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'ObjectType :' + Object.keys(client_1.ObjectType).join(','),
        example: 'post',
    }),
    (0, class_validator_1.IsEnum)(Object.keys(client_1.ObjectType)),
    __metadata("design:type", String)
], CreateAccessParams.prototype, "objectType", void 0);
class UpdateAccessParams {
    objectType;
}
exports.UpdateAccessParams = UpdateAccessParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Object Type',
        example: 'post',
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateAccessParams.prototype, "objectType", void 0);
//# sourceMappingURL=access.swagger.js.map