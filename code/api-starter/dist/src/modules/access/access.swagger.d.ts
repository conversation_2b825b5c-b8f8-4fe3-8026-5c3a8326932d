import { ObjectType } from '@prisma/client';
import { Prisma } from '@prisma/client';
export declare class FindAccessParams {
    objectType: ObjectType;
    objectId: string;
    sortField: string;
    sortOrder: Prisma.SortOrder;
    take: number;
    skip: number;
}
export declare class ListAccessParams {
    objectType: ObjectType;
    objectId: string;
}
export declare class CreateAccessParams {
    objectId: string;
    objectType: ObjectType;
}
export declare class UpdateAccessParams {
    objectType: ObjectType;
}
