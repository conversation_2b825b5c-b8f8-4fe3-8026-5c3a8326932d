"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccessController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const acl_decorator_1 = require("../../acl/acl.decorator");
const acl_guard_1 = require("../../acl/acl.guard");
const auth_guard_1 = require("../auth/auth.guard");
const access_service_1 = require("./access.service");
const access_swagger_1 = require("./access.swagger");
const auth_decorator_1 = require("../auth/auth.decorator");
const story = __importStar(require("../../../specs/story/access"));
let AccessController = class AccessController {
    accessService;
    constructor(accessService) {
        this.accessService = accessService;
    }
    async create(user, access) {
        const data = { ...access, userId: user.id };
        return await this.accessService.create(data);
    }
    whereToPrisma(params) {
        let where = {};
        const OR = [];
        if (params.objectId) {
            OR.push({
                objectId: {
                    contains: params.objectId,
                    mode: 'insensitive',
                },
            });
        }
        if (params.objectType) {
            OR.push({
                objectType: params.objectType,
            });
        }
        if (OR.length > 0) {
            where.OR = OR;
        }
        return where;
    }
    async find(params) {
        const { take, skip, sortField = 'objectType', sortOrder = 'asc', ...where } = params;
        return await this.accessService.find({
            where,
            take,
            skip,
        });
    }
    async read(id) {
        const access = await this.accessService.get({ objectId: id });
        if (!access) {
            throw new common_1.NotFoundException();
        }
        return access;
    }
    async deleteAll() {
        const result = await this.accessService.deleteAll();
        return { success: true, count: result.count };
    }
    async delete(id) {
        const access = await this.accessService.delete({ objectId: id });
        if (!access) {
            throw new common_1.NotFoundException();
        }
        return { success: true };
    }
    async update(id, params) {
        const access = await this.accessService.update({ objectId: id }, params);
        if (!access) {
            throw new common_1.NotFoundException();
        }
        return access;
    }
};
exports.AccessController = AccessController;
__decorate([
    (0, common_1.Post)(''),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.accessToCreate.access),
    (0, swagger_1.ApiOperation)(story.accessToCreate.operation),
    (0, swagger_1.ApiResponse)(story.accessToCreate.codes['201'].response),
    (0, swagger_1.ApiResponse)(story.accessToCreate.codes['401'].response),
    __param(0, (0, auth_decorator_1.ReqUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, access_swagger_1.CreateAccessParams]),
    __metadata("design:returntype", Promise)
], AccessController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('find'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.accessToFind.access),
    (0, swagger_1.ApiOperation)(story.accessToFind.operation),
    (0, swagger_1.ApiResponse)(story.accessToFind.codes['200'].response),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [access_swagger_1.FindAccessParams]),
    __metadata("design:returntype", Promise)
], AccessController.prototype, "find", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.accessToRead.access),
    (0, swagger_1.ApiOperation)(story.accessToRead.operation),
    (0, swagger_1.ApiResponse)(story.accessToRead.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.accessToRead.codes['404'].response),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AccessController.prototype, "read", null);
__decorate([
    (0, common_1.Delete)('delete'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.accessToDeleteAll.access),
    (0, swagger_1.ApiOperation)(story.accessToDeleteAll.operation),
    (0, swagger_1.ApiResponse)(story.accessToDeleteAll.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.accessToDeleteAll.codes['403'].response),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AccessController.prototype, "deleteAll", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.accessToDelete.access),
    (0, swagger_1.ApiOperation)(story.accessToDelete.operation),
    (0, swagger_1.ApiResponse)(story.accessToDelete.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.accessToDelete.codes['403'].response),
    (0, swagger_1.ApiResponse)(story.accessToDelete.codes['404'].response),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AccessController.prototype, "delete", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.accessToUpdate.access),
    (0, swagger_1.ApiOperation)(story.accessToUpdate.operation),
    (0, swagger_1.ApiResponse)(story.accessToUpdate.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.accessToUpdate.codes['401'].response),
    (0, swagger_1.ApiResponse)(story.accessToUpdate.codes['404'].response),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, access_swagger_1.UpdateAccessParams]),
    __metadata("design:returntype", Promise)
], AccessController.prototype, "update", null);
exports.AccessController = AccessController = __decorate([
    (0, common_1.Controller)('access'),
    (0, swagger_1.ApiTags)('access'),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [access_service_1.AccessService])
], AccessController);
//# sourceMappingURL=access.controller.js.map