import { Prisma, User } from '@prisma/client';
import { RatingService } from 'src/modules/rating/rating.service';
import { CheckRatingParams, CreateRatingParams } from 'src/modules/rating/rating.swagger';
export declare class RatingController {
    private readonly ratingService;
    constructor(ratingService: RatingService);
    create(user: User, rating: CreateRatingParams): Promise<any>;
    read(id: string): Promise<any>;
    whereToPrisma(params: any): Prisma.RatingWhereInput;
    checkRatingByUser(params: CheckRatingParams, user: User): Promise<boolean>;
}
