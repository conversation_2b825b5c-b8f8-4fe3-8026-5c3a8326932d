"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RatingController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const acl_guard_1 = require("../../acl/acl.guard");
const auth_guard_1 = require("../auth/auth.guard");
const rating_service_1 = require("./rating.service");
const rating_swagger_1 = require("./rating.swagger");
const auth_decorator_1 = require("../auth/auth.decorator");
let RatingController = class RatingController {
    ratingService;
    constructor(ratingService) {
        this.ratingService = ratingService;
    }
    async create(user, rating) {
        const data = {
            userId: user.id,
            ...rating,
        };
        const createdRating = await this.ratingService.create(data);
        const avCount = await this.ratingService.getRating({
            objectType: createdRating.objectType,
            objectId: createdRating.objectId,
        });
        if (avCount._avg && !avCount._avg.count) {
            throw new common_1.NotFoundException();
        }
        return this.ratingService.updateRatingInResources(createdRating, avCount);
    }
    async read(id) {
        const rating = await this.ratingService.get({ id });
        if (!rating) {
            throw new common_1.NotFoundException();
        }
        return rating;
    }
    whereToPrisma(params) {
        let where = {};
        if (params.objectType) {
            where.objectType = params.objectType;
        }
        if (params.objectId) {
            where.objectId = params.objectId;
        }
        if (params.userId) {
            where.userId = params.userId;
        }
        return where;
    }
    async checkRatingByUser(params, user) {
        const { ...where } = params;
        const ratings = await this.ratingService.find({
            where: this.whereToPrisma({ ...where, userId: user.id }),
        });
        return ratings && ratings.length > 0;
    }
};
exports.RatingController = RatingController;
__decorate([
    (0, common_1.Post)(''),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    __param(0, (0, auth_decorator_1.ReqUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, rating_swagger_1.CreateRatingParams]),
    __metadata("design:returntype", Promise)
], RatingController.prototype, "create", null);
__decorate([
    (0, common_1.Get)('rate/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], RatingController.prototype, "read", null);
__decorate([
    (0, common_1.Post)('checkRating'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, auth_decorator_1.ReqUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [rating_swagger_1.CheckRatingParams, Object]),
    __metadata("design:returntype", Promise)
], RatingController.prototype, "checkRatingByUser", null);
exports.RatingController = RatingController = __decorate([
    (0, common_1.Controller)('rating'),
    (0, swagger_1.ApiTags)('rating'),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [rating_service_1.RatingService])
], RatingController);
//# sourceMappingURL=rating.controller.js.map