"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RatingService = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
const prisma_service_1 = require("../../shared/prisma/prisma.service");
let RatingService = class RatingService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(data) {
        return await this.prisma.rating.create({ data });
    }
    async getRating(where) {
        return await this.prisma.rating.aggregate({
            _avg: {
                count: true,
            },
            where,
        });
    }
    async get(where) {
        return await this.prisma.rating.findUnique({
            where,
        });
    }
    async findAll() {
        return await this.prisma.rating.findMany();
    }
    async find({ where, orderBy, skip = 0, take = 10, }) {
        return await this.prisma.rating.findMany({
            where,
            orderBy,
            skip,
            take,
        });
    }
    getResourceToRating(objectType) {
        let resource;
        switch (objectType) {
            case 'news':
                resource = client_1.ObjectType.news;
                break;
            default:
                resource = '';
        }
        return resource;
    }
    async updateRatingInResources(rating, avCount) {
        const resource = this.getResourceToRating(rating.userId);
        console.log('resource', resource);
        try {
            if (resource != '')
                await this.prisma[resource].update({ id: rating.id }, { rating: Math.ceil(avCount?._avg?.count) });
            return {
                ...rating,
                averageRating: { count: Math.ceil(avCount._avg?.count) },
            };
        }
        catch (e) {
            return {
                ...rating,
                averageRating: { count: Math.ceil(avCount._avg?.count) },
            };
        }
    }
    async updateRatingByResource(objectType, objectId, avg) {
        try {
            await this.prisma[objectType].update({ id: objectId }, { rating: Math.ceil(avg.count) });
            return { count: Math.ceil(avg.count) };
        }
        catch (e) {
            return { count: Math.ceil(avg.count) };
        }
    }
};
exports.RatingService = RatingService;
exports.RatingService = RatingService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], RatingService);
//# sourceMappingURL=rating.service.js.map