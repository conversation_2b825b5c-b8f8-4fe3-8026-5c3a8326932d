import { Rating, Prisma } from '@prisma/client';
import { PrismaService } from 'src/shared/prisma/prisma.service';
export declare class RatingService {
    prisma: PrismaService;
    constructor(prisma: PrismaService);
    create(data: Prisma.RatingCreateInput): Promise<Rating>;
    getRating(where: Prisma.RatingWhereInput): Promise<any>;
    get(where: Prisma.RatingWhereUniqueInput): Promise<Rating>;
    findAll(): Promise<Rating[]>;
    find({ where, orderBy, skip, take, }: Prisma.RatingFindManyArgs): Promise<Rating[]>;
    getResourceToRating(objectType: any): string;
    updateRatingInResources(rating: Rating, avCount: any): Promise<any>;
    updateRatingByResource(objectType: string, objectId: string, avg: any): Promise<{
        count: number;
    }>;
}
