"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CheckRatingParams = exports.CreateRatingParams = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const mock_utils_1 = require("../../mock/mock.utils");
const client_1 = require("@prisma/client");
class CreateRatingParams {
    objectId;
    objectType;
    count;
}
exports.CreateRatingParams = CreateRatingParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Formation ID or news ID',
        example: mock_utils_1.mock.id(),
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateRatingParams.prototype, "objectId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'ObjectType :' + Object.keys(client_1.ObjectType).join(','),
        example: client_1.ObjectType.comment,
    }),
    (0, class_validator_1.IsEnum)(Object.keys(client_1.ObjectType)),
    __metadata("design:type", String)
], CreateRatingParams.prototype, "objectType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Count',
        example: 3,
    }),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CreateRatingParams.prototype, "count", void 0);
class CheckRatingParams {
    objectId;
    objectType;
}
exports.CheckRatingParams = CheckRatingParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: '',
        example: 'seed.ratingToRead.result.objectId',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CheckRatingParams.prototype, "objectId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'ObjectType :' + Object.keys(client_1.ObjectType).join(','),
        example: client_1.ObjectType.comment,
    }),
    (0, class_validator_1.IsEnum)(Object.keys(client_1.ObjectType)),
    __metadata("design:type", String)
], CheckRatingParams.prototype, "objectType", void 0);
//# sourceMappingURL=rating.swagger.js.map