import { News, Prisma } from '@prisma/client';
import { PrismaService } from 'src/shared/prisma/prisma.service';
export declare class NewService {
    prisma: PrismaService;
    constructor(prisma: PrismaService);
    create(data: Prisma.NewsCreateInput): Promise<News>;
    get(where: Prisma.NewsWhereUniqueInput): Promise<News>;
    delete(where: Prisma.NewsWhereUniqueInput): Promise<News>;
    update(where: Prisma.NewsWhereUniqueInput, params: Prisma.NewsUpdateInput): Promise<News>;
    findAll(): Promise<News[]>;
    find({ where, orderBy, skip, take, }: Prisma.NewsFindManyArgs): Promise<News[]>;
    count(where: Prisma.NewsWhereInput): Promise<number>;
    getBy(where: Prisma.NewsWhereInput): Promise<News>;
}
