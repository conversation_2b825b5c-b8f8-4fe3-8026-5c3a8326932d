"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeedModule = void 0;
const content_module_1 = require("../content/content.module");
const common_1 = require("@nestjs/common");
const admin_module_1 = require("../admin/admin.module");
const auth_module_1 = require("../auth/auth.module");
const comment_module_1 = require("../comment/comment.module");
const exemple_module_1 = require("../exemple/exemple.module");
const field_module_1 = require("../field/field.module");
const home_module_1 = require("../home/<USER>");
const like_module_1 = require("../like/like.module");
const news_module_1 = require("../news/news.module");
const profile_module_1 = require("../profile/profile.module");
const rating_module_1 = require("../rating/rating.module");
const report_module_1 = require("../report/report.module");
const seed_controller_1 = require("./seed.controller");
const seed_service_1 = require("./seed.service");
const user_module_1 = require("../user/user.module");
const env_utils_1 = require("../../shared/env/env.utils");
const analytic_module_1 = require("../analytic/analytic.module");
const highlight_module_1 = require("../highlight/highlight.module");
const cron_module_1 = require("../cron/cron.module");
const iap_module_1 = require("../iap/iap.module");
const account_module_1 = require("../account/account.module");
const workflow_module_1 = require("../workflow/workflow.module");
const chat_module_1 = require("../chat/chat.module");
let SeedModule = class SeedModule {
};
exports.SeedModule = SeedModule;
exports.SeedModule = SeedModule = __decorate([
    (0, common_1.Module)({
        imports: [
            analytic_module_1.AnalyticModule,
            auth_module_1.AuthModule,
            user_module_1.UserModule,
            exemple_module_1.ExempleModule,
            comment_module_1.CommentModule,
            report_module_1.ReportModule,
            rating_module_1.RatingModule,
            admin_module_1.AdminModule,
            home_module_1.HomeModule,
            like_module_1.LikeModule,
            profile_module_1.ProfileModule,
            news_module_1.NewsModule,
            field_module_1.FieldModule,
            highlight_module_1.HighlightModule,
            content_module_1.ContentModule,
            cron_module_1.CronModule,
            iap_module_1.IapModule,
            account_module_1.AccountModule,
            workflow_module_1.WorkflowModule,
            chat_module_1.ChatModule,
        ],
        controllers: !(0, env_utils_1.isProductionEnv)() ? [seed_controller_1.SeedController] : [],
        providers: [seed_service_1.SeedService],
        exports: [seed_service_1.SeedService],
    })
], SeedModule);
//# sourceMappingURL=seed.module.js.map