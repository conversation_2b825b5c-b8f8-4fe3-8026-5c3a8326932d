"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeedMap = void 0;
const demo_1 = require("../../../specs/demo");
const seed_1 = require("../../../specs/seed");
const env_utils_1 = require("../../shared/env/env.utils");
class SeedMap {
    configs = (0, env_utils_1.isStagingEnv)() ? demo_1.demoSeedConfig : seed_1.testSeedConfig;
    parent = {};
    child = {};
    results = {};
    alias = {};
    async addDependency(seedName, depency) {
        if (this.alias[depency] !== undefined) {
            depency = this.alias[depency];
        }
        if (seedName == depency)
            return;
        if (!this.child[seedName]) {
            this.child[seedName] = {};
        }
        this.child[seedName][depency] = true;
        if (!this.parent[depency]) {
            this.parent[depency] = {};
        }
        this.parent[depency][seedName] = true;
    }
    async addDependencies(seedName, name) {
        const names = name.split('.');
        if (names[0] === 'seed' || names[0] === 'auth') {
            this.addDependency(seedName, names[1]);
        }
    }
    async parseData(seedName, data) {
        if (!this.child[seedName]) {
            this.child[seedName] = {};
        }
        if (!this.parent[seedName]) {
            this.parent[seedName] = {};
        }
        if (typeof data === 'string') {
            await this.addDependencies(seedName, data);
        }
        else if (Array.isArray(data)) {
            for (const item of data) {
                await this.parseData(seedName, item);
            }
        }
        else if (typeof data === 'object') {
            if (data.saveAs) {
                const { saveAs, ...newData } = data;
                this.alias[saveAs] = seedName;
                await this.parseData(saveAs, newData);
            }
            else {
                for (const key in data) {
                    await this.parseData(seedName, data[key]);
                }
            }
        }
    }
    asDependency(seedName) {
        if (this.results[seedName] !== undefined) {
            console.log(`Skipping ${seedName}: Already seeded.`);
            return;
        }
        const depsReady = Object.keys(this.child[seedName]).every(dep => this.results[dep]);
        return depsReady;
    }
    async fillSeed(seedName, seedFunction) {
        try {
            console.log(`Seeding: ${seedName}...`);
            await seedFunction(seedName, this.configs[seedName]);
            this.results[seedName] = true;
            console.log(`Seeded: ${seedName}`);
        }
        catch (error) {
            console.error(`Error seeding ${seedName}:`, error);
        }
    }
    async execSeed(seedFunction) {
        let pendingSeeds = Object.keys(this.configs).filter(item => !this.results[item]);
        const seedPromises = [];
        for (const seed of pendingSeeds) {
            if (this.asDependency(seed)) {
                const seedPromise = this.fillSeed(seed, seedFunction);
                if (seedPromise) {
                    seedPromises.push(seedPromise);
                }
            }
        }
        if (seedPromises.length === 0) {
            pendingSeeds = Object.keys(this.configs).filter(item => !this.results[item]);
            for (const seed of pendingSeeds) {
                await this.fillSeed(seed, seedFunction);
            }
            console.log('Processus de seeding terminé.');
            return;
        }
        else {
            await Promise.all(seedPromises);
        }
        const completedSeeds = Object.keys(this.results).length;
        console.log({ pendingSeeds, completedSeeds });
        if (completedSeeds !== Object.keys(this.configs).length) {
            console.log('Processus de seeding terminé.');
            await this.execSeed(seedFunction);
        }
        else {
            console.log('Processus de seeding terminé.');
        }
    }
    async init(seedFunction) {
        console.log('Analyse des besoins...');
        for (const key of Object.keys(this.configs)) {
            await this.parseData(key, this.configs[key]);
        }
        console.log('Début du processus de seeding...');
        await this.execSeed(seedFunction);
    }
}
exports.SeedMap = SeedMap;
//# sourceMappingURL=seed.map.js.map