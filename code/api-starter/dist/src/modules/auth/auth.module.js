"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthModule = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const passport_1 = require("@nestjs/passport");
const admin_auth_controller_1 = require("./admin.auth.controller");
const user_auth_controller_1 = require("./user.auth.controller");
const auth_email_strategy_1 = require("./passport/email/auth.email.strategy");
const jwt_strategy_1 = require("./passport/jwt/jwt.strategy");
const auth_service_1 = require("./auth.service");
const auth_email_service_1 = require("./passport/email/auth.email.service");
const google_strategy_1 = require("./passport/google/google.strategy");
const auth_user_service_1 = require("./auth.user.service");
const user_module_1 = require("../user/user.module");
const admin_module_1 = require("../admin/admin.module");
const account_module_1 = require("../account/account.module");
const profile_module_1 = require("../profile/profile.module");
const account_service_1 = require("../account/account.service");
const auth_controller_1 = require("./auth.controller");
const google_controller_1 = require("./passport/google/google.controller");
const linkedIn_controller_1 = require("./passport/linkedIn/linkedIn.controller");
const linkedin_strategy_1 = require("./passport/linkedIn/linkedin.strategy");
const twitter_controller_1 = require("./passport/twitter/twitter.controller");
const twitter_strategy_1 = require("./passport/twitter/twitter.strategy");
const facebook_controller_1 = require("./passport/facebook/facebook.controller");
const facebook_strategy_1 = require("./passport/facebook/facebook.strategy");
const config_service_1 = require("../../shared/config/config.service");
let AuthModule = class AuthModule {
};
exports.AuthModule = AuthModule;
exports.AuthModule = AuthModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        imports: [
            jwt_1.JwtModule.registerAsync({
                imports: [],
                useFactory: async (configService) => ({
                    secret: configService.get('APP_SECRET'),
                    signOptions: {
                        expiresIn: 60 * 60 * 24 * 30,
                    },
                }),
                inject: [config_service_1.ConfigService],
            }),
            passport_1.PassportModule.registerAsync({
                useFactory: () => ({
                    defaultStrategy: 'jwt',
                }),
            }),
            user_module_1.UserModule,
            admin_module_1.AdminModule,
            account_module_1.AccountModule,
            profile_module_1.ProfileModule,
        ],
        controllers: [
            user_auth_controller_1.AuthUserController,
            admin_auth_controller_1.AuthAdminController,
            auth_controller_1.AuthController,
            google_controller_1.GoogleAuthController,
            linkedIn_controller_1.AuthLinkedInController,
            twitter_controller_1.AuthTwitterController,
            facebook_controller_1.AuthFacebookController,
        ],
        providers: [
            jwt_strategy_1.JwtStrategy,
            auth_email_strategy_1.EmailStrategy,
            auth_email_service_1.AuthEmailService,
            auth_service_1.AuthService,
            auth_user_service_1.AuthUserService,
            google_strategy_1.GoogleStrategy,
            account_service_1.AccountService,
            auth_service_1.AuthService,
            auth_email_service_1.AuthEmailService,
            linkedin_strategy_1.LinkedinStrategy,
            user_auth_controller_1.AuthUserController,
            auth_controller_1.AuthController,
            admin_auth_controller_1.AuthAdminController,
            twitter_strategy_1.TwitterStrategy,
            facebook_strategy_1.FacebookStrategy,
        ],
        exports: [
            auth_service_1.AuthService,
            auth_email_service_1.AuthEmailService,
            auth_user_service_1.AuthUserService,
            user_auth_controller_1.AuthUserController,
            admin_auth_controller_1.AuthAdminController,
            auth_controller_1.AuthController,
        ],
    })
], AuthModule);
//# sourceMappingURL=auth.module.js.map