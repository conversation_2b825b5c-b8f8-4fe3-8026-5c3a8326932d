"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActionController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const acl_decorator_1 = require("../../acl/acl.decorator");
const acl_guard_1 = require("../../acl/acl.guard");
const auth_guard_1 = require("../auth/auth.guard");
const action_service_1 = require("./action.service");
const action_swagger_1 = require("./action.swagger");
const story = __importStar(require("../../../specs/story/action"));
let ActionController = class ActionController {
    actionService;
    constructor(actionService) {
        this.actionService = actionService;
    }
    async create(action) {
        const { projectId, ...rest } = action;
        const data = {
            ...rest,
            project: { connect: { id: action.projectId } },
        };
        return await this.actionService.create(data);
    }
    whereToPrisma(params) {
        let where = {};
        if (params.name) {
            where.name = {
                contains: params.name,
                mode: 'insensitive',
            };
        }
        return where;
    }
    async list(params) {
        return await this.actionService.list(params);
    }
    async read(id) {
        return await this.actionService.get({ id });
    }
    async delete(id) {
        return await this.actionService.delete({ id });
    }
    async update(id, params) {
        return await this.actionService.update({ id }, params);
    }
    async updateStepStatus(stepId, params) {
        return await this.actionService.updateStepStatus(stepId, params.status);
    }
};
exports.ActionController = ActionController;
__decorate([
    (0, common_1.Post)(''),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.actionToCreate.access),
    (0, swagger_1.ApiOperation)(story.actionToCreate.operation),
    (0, swagger_1.ApiResponse)(story.actionToCreate.codes['201'].response),
    (0, swagger_1.ApiResponse)(story.actionToCreate.codes['401'].response),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [action_swagger_1.CreateActionParams]),
    __metadata("design:returntype", Promise)
], ActionController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('project/:projectId/skill/:skill'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, swagger_1.ApiOperation)(story.actionToFind.operation),
    (0, swagger_1.ApiResponse)(story.actionToFind.codes['201'].response),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [action_swagger_1.ListActionParams]),
    __metadata("design:returntype", Promise)
], ActionController.prototype, "list", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.actionToRead.access),
    (0, swagger_1.ApiOperation)(story.actionToRead.operation),
    (0, swagger_1.ApiResponse)(story.actionToRead.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.actionToRead.codes['404'].response),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ActionController.prototype, "read", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.actionToDelete.access),
    (0, swagger_1.ApiOperation)(story.actionToDelete.operation),
    (0, swagger_1.ApiResponse)(story.actionToDelete.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.actionToDelete.codes['403'].response),
    (0, swagger_1.ApiResponse)(story.actionToDelete.codes['404'].response),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ActionController.prototype, "delete", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.actionToUpdate.access),
    (0, swagger_1.ApiOperation)(story.actionToUpdate.operation),
    (0, swagger_1.ApiResponse)(story.actionToUpdate.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.actionToUpdate.codes['401'].response),
    (0, swagger_1.ApiResponse)(story.actionToUpdate.codes['404'].response),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, action_swagger_1.UpdateActionParams]),
    __metadata("design:returntype", Promise)
], ActionController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':stepId/status'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)(story.actionToUpdate.access),
    (0, swagger_1.ApiOperation)(story.actionToUpdate.operation),
    (0, swagger_1.ApiResponse)(story.actionToUpdate.codes['200'].response),
    (0, swagger_1.ApiResponse)(story.actionToUpdate.codes['401'].response),
    (0, swagger_1.ApiResponse)(story.actionToUpdate.codes['404'].response),
    __param(0, (0, common_1.Param)('stepId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ActionController.prototype, "updateStepStatus", null);
exports.ActionController = ActionController = __decorate([
    (0, common_1.Controller)('action'),
    (0, swagger_1.ApiTags)('action'),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [action_service_1.ActionService])
], ActionController);
//# sourceMappingURL=action.controller.js.map