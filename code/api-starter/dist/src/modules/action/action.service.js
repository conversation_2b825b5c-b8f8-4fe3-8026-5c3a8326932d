"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActionService = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
const prisma_service_1 = require("../../shared/prisma/prisma.service");
let ActionService = class ActionService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create({ projectId, config }) {
        const requirements = config.requirements;
        return await this.prisma.action.create({
            data: {
                name: config.name,
                type: config.type,
                status: config?.status ?? undefined,
                skill: config.skill,
                ownerId: config.ownerId ?? undefined,
                objectName: config.objectName ?? undefined,
                ownerType: config.ownerType,
                requirements: {
                    create: requirements.map(req => ({
                        name: req.name,
                        order: req.order,
                        steps: {
                            create: req.steps.map(step => ({
                                name: step.name,
                                order: step.order,
                                type: step.type,
                            })),
                        },
                    })),
                },
            },
            include: {
                requirements: {
                    orderBy: {
                        order: 'asc',
                    },
                    include: {
                        steps: {
                            orderBy: {
                                order: 'asc',
                            },
                        },
                    },
                },
            },
        });
    }
    async get(where) {
        return await this.prisma.action.findUnique({
            where,
            include: {
                requirements: {
                    orderBy: {
                        order: 'asc',
                    },
                    include: {
                        steps: {
                            orderBy: {
                                order: 'asc',
                            },
                        },
                    },
                },
            },
        });
    }
    async list({ ownerId, ownerType, skill, }) {
        return await this.prisma.action.findMany({
            where: {
                ownerId,
                ownerType,
                skill,
            },
        });
    }
    async delete(where) {
        try {
            await this.prisma.action.delete({ where });
            return true;
        }
        catch (e) {
            console.error(e);
            return false;
        }
    }
    async update(where, params) {
        return await this.prisma.action.update({
            data: params,
            where,
        });
    }
    async findAll() {
        return await this.prisma.action.findMany();
    }
    async find({ where, orderBy, skip = 0, take = 10, }) {
        return await this.prisma.action.findMany({
            where,
            include: {
                requirements: {
                    include: {
                        steps: {
                            include: {
                                workflow: {
                                    select: {
                                        id: true,
                                        name: true,
                                        actionType: true,
                                        ownerId: true,
                                        ownerType: true,
                                        objectName: true,
                                        status: true,
                                        tasks: true,
                                    },
                                },
                            },
                        },
                    },
                },
            },
            orderBy,
            skip,
            take,
        });
    }
    async exists(where) {
        return (await this.prisma.action.count({ where })) > 0 ? true : false;
    }
    async count(where) {
        return await this.prisma.action.count({
            where,
        });
    }
    async getBy(where) {
        const data = await this.prisma.action.findFirst({
            where,
        });
        if (!data)
            throw new common_1.NotFoundException();
        return data;
    }
    async updateStepStatus(stepId, newStatus) {
        const step = await this.prisma.step.update({
            where: { id: stepId },
            data: { status: newStatus },
            include: {
                requirement: {
                    include: {
                        steps: true,
                        action: true,
                    },
                },
            },
        });
        const requirement = step.requirement;
        const action = requirement.action;
        const allStepsCompleted = requirement.steps.every(s => s.status === client_1.WorkflowStatus.COMPLETED);
        let requirementStatus;
        if (allStepsCompleted) {
            requirementStatus = client_1.WorkflowStatus.COMPLETED;
        }
        else if (requirement.steps.some(s => s.status === client_1.WorkflowStatus.COMPLETED)) {
            requirementStatus = client_1.WorkflowStatus.IN_PROGRESS;
        }
        else {
            requirementStatus = client_1.WorkflowStatus.PENDING;
        }
        await this.prisma.requirement.update({
            where: { id: requirement.id },
            data: { status: requirementStatus },
        });
        if (requirementStatus === client_1.WorkflowStatus.COMPLETED) {
            const incompleteRequirements = await this.prisma.requirement.findMany({
                where: {
                    actionId: action.id,
                    status: { not: client_1.WorkflowStatus.COMPLETED },
                },
            });
            if (incompleteRequirements.length === 0) {
                await this.prisma.action.update({
                    where: { id: action.id },
                    data: { status: client_1.WorkflowStatus.COMPLETED },
                });
            }
        }
        const updatedAction = await this.get({ id: action.id });
        return updatedAction;
    }
};
exports.ActionService = ActionService;
exports.ActionService = ActionService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], ActionService);
//# sourceMappingURL=action.service.js.map