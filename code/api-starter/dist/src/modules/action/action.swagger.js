"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FindActionParams = exports.ListActionParams = exports.UpdateActionParams = exports.DeleteActionParams = exports.GetActionParams = exports.CreateActionParams = void 0;
const swagger_1 = require("@nestjs/swagger");
const client_1 = require("@prisma/client");
const class_validator_1 = require("class-validator");
class CreateActionParams {
    name;
    projectId;
    status;
    result;
    requirements;
}
exports.CreateActionParams = CreateActionParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'The name of the action',
        example: 'CreateGrantAction',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateActionParams.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'The ID of the project associated with the action',
        example: 1,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], CreateActionParams.prototype, "projectId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'The status of the action',
        enum: client_1.WorkflowStatus,
    }),
    (0, class_validator_1.IsEnum)(client_1.WorkflowStatus),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateActionParams.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'The result of the action (optional JSON)',
        type: 'object',
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], CreateActionParams.prototype, "result", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'The requirements associated with the action (optional)',
        type: 'array',
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], CreateActionParams.prototype, "requirements", void 0);
class GetActionParams {
    name;
}
exports.GetActionParams = GetActionParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'The name of the action',
        example: 'GetGrantAction',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], GetActionParams.prototype, "name", void 0);
class DeleteActionParams {
    name;
}
exports.DeleteActionParams = DeleteActionParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'The name of the action',
        example: 'DeleteGrantAction',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], DeleteActionParams.prototype, "name", void 0);
class UpdateActionParams {
    name;
}
exports.UpdateActionParams = UpdateActionParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'The new name of the action',
        example: 'UpdatedGrantAction',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateActionParams.prototype, "name", void 0);
class ListActionParams {
    projectId;
    skill;
    ownerId;
    ownerType;
}
exports.ListActionParams = ListActionParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'Project Id',
        example: '',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ListActionParams.prototype, "projectId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Skill',
        enum: Object.keys(client_1.Skill),
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListActionParams.prototype, "skill", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Owner id',
        example: 'seed.workflowToRead.result.id',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListActionParams.prototype, "ownerId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        description: 'OwnerType :' + Object.keys(client_1.OwnerType).join(','),
        example: client_1.OwnerType.comment,
    }),
    (0, class_validator_1.IsEnum)(Object.keys(client_1.OwnerType)),
    __metadata("design:type", String)
], ListActionParams.prototype, "ownerType", void 0);
class FindActionParams {
    name;
    sortField;
    sortOrder;
    take;
    skip;
}
exports.FindActionParams = FindActionParams;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Filter by action name',
        example: 'CreateGrantAction',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FindActionParams.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Field to sort by',
        enum: Object.keys(client_1.Prisma.ActionScalarFieldEnum),
    }),
    (0, class_validator_1.IsEnum)(client_1.Prisma.ActionScalarFieldEnum),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], FindActionParams.prototype, "sortField", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Order to sort by (asc or desc)',
        enum: client_1.Prisma.SortOrder,
        example: client_1.Prisma.SortOrder.asc,
    }),
    (0, class_validator_1.IsEnum)(client_1.Prisma.SortOrder),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], FindActionParams.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Number of results to return',
        example: 10,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], FindActionParams.prototype, "take", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Number of results to skip',
        example: 0,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], FindActionParams.prototype, "skip", void 0);
//# sourceMappingURL=action.swagger.js.map