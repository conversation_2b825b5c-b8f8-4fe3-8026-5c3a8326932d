import { Action, Prisma, WorkflowStatus } from '@prisma/client';
import { PrismaService } from 'src/shared/prisma/prisma.service';
export declare class ActionService {
    prisma: PrismaService;
    constructor(prisma: PrismaService);
    create({ projectId, config }: {
        projectId: any;
        config: any;
    }): Promise<Action>;
    get(where: Prisma.ActionWhereUniqueInput): Promise<Action>;
    list({ ownerId, ownerType, skill, }: Prisma.ActionWhereInput): Promise<Action[]>;
    delete(where: Prisma.ActionWhereUniqueInput): Promise<boolean>;
    update(where: Prisma.ActionWhereUniqueInput, params: Prisma.ActionUpdateInput): Promise<Action>;
    findAll(): Promise<Action[]>;
    find({ where, orderBy, skip, take, }: Prisma.ActionFindManyArgs): Promise<Action[]>;
    exists(where: Prisma.ActionWhereInput): Promise<boolean>;
    count(where: Prisma.ActionWhereInput): Promise<number>;
    getBy(where: Prisma.ActionWhereInput): Promise<Action>;
    updateStepStatus(stepId: string, newStatus: WorkflowStatus): Promise<Action>;
}
