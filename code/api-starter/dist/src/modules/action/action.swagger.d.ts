import { OwnerType, Prisma, Skill, WorkflowStatus } from '@prisma/client';
export declare class CreateActionParams {
    name: string;
    projectId: number;
    status: WorkflowStatus;
    result?: JSON;
    requirements?: any[];
}
export declare class GetActionParams {
    name: string;
}
export declare class DeleteActionParams {
    name: string;
}
export declare class UpdateActionParams {
    name?: string;
}
export declare class ListActionParams {
    projectId: string;
    skill: Skill;
    ownerId: string;
    ownerType: OwnerType;
}
export declare class FindActionParams {
    name?: string;
    sortField?: keyof typeof Prisma.ActionScalarFieldEnum;
    sortOrder?: Prisma.SortOrder;
    take?: number;
    skip?: number;
}
