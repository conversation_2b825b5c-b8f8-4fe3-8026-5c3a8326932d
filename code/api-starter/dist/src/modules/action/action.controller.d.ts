import { Action, Prisma, WorkflowStatus } from '@prisma/client';
import { ActionService } from './action.service';
import { CreateActionParams, ListActionParams, UpdateActionParams } from './action.swagger';
export declare class ActionController {
    private readonly actionService;
    constructor(actionService: ActionService);
    create(action: CreateActionParams): Promise<Action>;
    whereToPrisma(params: any): Prisma.ActionWhereInput;
    list(params: ListActionParams): Promise<Action[]>;
    read(id: string): Promise<any>;
    delete(id: string): Promise<boolean>;
    update(id: string, params: UpdateActionParams): Promise<Action>;
    updateStepStatus(stepId: string, params: {
        status: WorkflowStatus;
    }): Promise<Action>;
}
