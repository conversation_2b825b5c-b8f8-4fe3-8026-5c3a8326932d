"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createApplyGrantAction = void 0;
const client_1 = require("@prisma/client");
const createApplyGrantAction = (name, id) => ({
    name: 'Fill <PERSON>',
    type: 'fillGrant',
    status: 'IN_PROGRESS',
    skill: 'finance',
    objectName: name,
    ownerId: id,
    requirements: [
        {
            name: 'grant',
            order: 1,
            steps: [
                {
                    name: 'createGrantName',
                    order: 1,
                    type: client_1.ActionStepStatus.form,
                },
                {
                    name: 'UploadGrantFiles',
                    order: 2,
                    type: client_1.ActionStepStatus.upload,
                },
                {
                    name: 'analyzeGrant',
                    order: 3,
                    type: client_1.ActionStepStatus.workflow,
                },
            ],
        },
        {
            name: 'application',
            order: 2,
            steps: [
                {
                    name: 'getEnterprise',
                    order: 1,
                    type: client_1.ActionStepStatus.form,
                },
                {
                    name: 'fillApplication',
                    order: 2,
                    type: client_1.ActionStepStatus.workflow,
                },
            ],
        },
    ],
});
exports.createApplyGrantAction = createApplyGrantAction;
//# sourceMappingURL=grant.action.config.js.map