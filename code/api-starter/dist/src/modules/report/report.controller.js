"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReportController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const acl_decorator_1 = require("../../acl/acl.decorator");
const acl_guard_1 = require("../../acl/acl.guard");
const client_1 = require("@prisma/client");
const auth_decorator_1 = require("../auth/auth.decorator");
const auth_guard_1 = require("../auth/auth.guard");
const report_service_1 = require("./report.service");
const report_json_1 = require("../../../specs/story/comment/report.json");
const report_swagger_1 = require("./report.swagger");
let ReportController = class ReportController {
    reportService;
    constructor(reportService) {
        this.reportService = reportService;
    }
    async find(params) {
        return await this.reportService.find(params);
    }
    async signal(user, body) {
        const data = {
            ...body,
            userId: user.id,
        };
        return await this.reportService.create(data);
    }
    async read(id) {
        return this.reportService.get({ id });
    }
    async update(id, params) {
        return await this.reportService.update({ id }, params);
    }
};
exports.ReportController = ReportController;
__decorate([
    (0, common_1.Post)('find'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)({
        resource: 'comment',
        action: 'read',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ReportController.prototype, "find", null);
__decorate([
    (0, common_1.Post)(''),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)({
        resource: 'report',
        action: 'create',
    }),
    (0, swagger_1.ApiOperation)(report_json_1.routes['/report'].POST.operation),
    (0, swagger_1.ApiOperation)(report_json_1.routes['/report'].POST.codes['200'].response),
    __param(0, (0, auth_decorator_1.ReqUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, report_swagger_1.CreateReportParams]),
    __metadata("design:returntype", Promise)
], ReportController.prototype, "signal", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)(report_json_1.routes['/report/:id'].GET.operation),
    (0, swagger_1.ApiResponse)(report_json_1.routes['/report/:id'].GET.codes['200'].response),
    (0, swagger_1.ApiResponse)(report_json_1.routes['/report/:id'].GET.codes['400'].response),
    (0, swagger_1.ApiResponse)(report_json_1.routes['/report/:id'].GET.codes['404'].response),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)({
        resource: 'report',
        action: 'read',
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ReportController.prototype, "read", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, acl_guard_1.AccessGuard),
    (0, acl_decorator_1.AccessTo)({
        resource: report_json_1.resource,
        action: 'create',
    }),
    (0, swagger_1.ApiOperation)(report_json_1.routes['/report/:id'].PATCH.operation),
    (0, swagger_1.ApiResponse)(report_json_1.routes['/report/:id'].PATCH.codes['200'].response),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, report_swagger_1.UpdateReportParams]),
    __metadata("design:returntype", Promise)
], ReportController.prototype, "update", null);
exports.ReportController = ReportController = __decorate([
    (0, swagger_1.ApiTags)('report'),
    (0, common_1.Controller)('/report'),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [report_service_1.ReportService])
], ReportController);
//# sourceMappingURL=report.controller.js.map