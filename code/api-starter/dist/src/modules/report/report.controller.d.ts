import { Prisma, Report, User } from '@prisma/client';
import { ReportService } from 'src/modules/report/report.service';
import { CreateReportParams, UpdateReportParams } from './report.swagger';
export declare class ReportController {
    private readonly reportService;
    constructor(reportService: ReportService);
    find(params: Prisma.ReportFindManyArgs): Promise<Report[]>;
    signal(user: User, body: CreateReportParams): Promise<Report>;
    read(id: string): Promise<any>;
    update(id: string, params: UpdateReportParams): Promise<Report>;
}
