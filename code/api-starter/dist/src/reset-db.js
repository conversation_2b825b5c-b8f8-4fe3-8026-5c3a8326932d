"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.isSeed = exports.isReset = exports.isReseting = exports.prisma = exports.seedDumpPath = exports.seedSqlPath = exports.seedJsonPath = void 0;
exports.eraseDb = eraseDb;
exports.restoreDb = restoreDb;
exports.disconnect = disconnect;
exports.connect = connect;
const client_1 = require("@prisma/client");
const child_process_1 = require("child_process");
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = __importDefault(require("path"));
const env_utils_1 = require("./shared/env/env.utils");
const root = process.cwd();
exports.seedJsonPath = path_1.default.join(root, 'seed.json');
exports.seedSqlPath = path_1.default.join(root, 'seed.sql');
exports.seedDumpPath = path_1.default.join(root, 'seed.dump');
exports.prisma = new client_1.PrismaClient();
exports.isReseting = false;
exports.isReset = false;
exports.isSeed = false;
if (process.env.DATABASE_URL === undefined) {
    dotenv_1.default.config();
}
const getDatabaseUrl = () => {
    return process.env.DATABASE_URL ? process.env.DATABASE_URL.split('?')[0] : '';
};
const checkAndCreateDatabase = async () => {
    const dbUrl = new URL(process.env.DATABASE_URL);
    const dbName = dbUrl.pathname.split('/')[1];
    const dbUser = dbUrl.username;
    const dbHost = dbUrl.hostname;
    const dbPort = dbUrl.port;
    const dbPassword = dbUrl.password;
    if (!dbName) {
        console.error('Database name could not be extracted from DATABASE_URL.');
        return;
    }
    const createDbCommand = `PGPASSWORD="${dbPassword}" createdb -h ${dbHost} -p ${dbPort} -U ${dbUser} ${dbName}`;
    try {
        await execPromise(createDbCommand);
        console.log(`Database ${dbName} created.`);
        await execPromise('npm run prisma:sync');
    }
    catch (error) {
        if (error && error.toString().indexOf('already exists') > 0) {
            console.log(`Database ${dbName} already exists.`);
        }
        else {
            console.log('Error attempting to create database:', error);
        }
    }
};
async function eraseDb() {
    console.log('Erasing database...');
    const tablenames = await exports.prisma.$queryRaw `SELECT tablename FROM pg_tables WHERE schemaname='public'`;
    for (const i in tablenames) {
        if (tablenames[i].tablename !== '_prisma_migrations') {
            try {
                await exports.prisma.$queryRawUnsafe(`DROP TABLE "public"."${tablenames[i].tablename}" CASCADE;`);
            }
            catch (error) {
                console.warn({ error });
            }
        }
    }
    console.log('Database erased');
}
async function restoreDb() {
    console.log('Restoring database using dump file...');
    const dbUrl = new URL(process.env.DATABASE_URL);
    const dbName = dbUrl.pathname.split('/')[1];
    const dbUser = dbUrl.username;
    const dbHost = dbUrl.hostname;
    const dbPort = dbUrl.port || 5432;
    const dbPassword = dbUrl.password;
    const restoreCommand = `PGPASSWORD="${dbPassword}" pg_restore --clean --no-owner --host=${dbHost} --port=${dbPort} --username=${dbUser} --dbname=${dbName} ${exports.seedDumpPath}`;
    try {
        console.log(restoreCommand);
        await execPromise(restoreCommand);
        console.log('Database restored from dump file');
    }
    catch (error) {
        console.error('Error restoring the database:', error);
    }
}
async function disconnect() {
    await exports.prisma.$disconnect();
    return true;
}
const execPromise = async (cmd) => {
    return new Promise((resolve, reject) => {
        (0, child_process_1.exec)(cmd, (error, output, errorMessage) => {
            if (output && output.indexOf('command not found')) {
                console.log('output', output.substring(0, 100));
            }
            if (errorMessage && errorMessage.indexOf('command not found')) {
                console.log('error', errorMessage.substring(0, 100));
            }
            if (error) {
                reject(error);
            }
            else {
                resolve(true);
            }
        });
    });
};
async function connect() {
    await exports.prisma.$connect();
    return true;
}
checkAndCreateDatabase()
    .then(async () => {
    exports.prisma.$connect();
    if (!(0, env_utils_1.isProductionEnv)()) {
        await eraseDb();
        await restoreDb();
    }
})
    .finally(() => {
    exports.prisma.$disconnect();
});
//# sourceMappingURL=reset-db.js.map