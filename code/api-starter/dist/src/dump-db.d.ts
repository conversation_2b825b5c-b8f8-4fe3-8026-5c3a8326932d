import { PrismaClient } from '@prisma/client';
export declare const seedJsonPath: string;
export declare const seedSqlPath: string;
export declare const seedDumpPath: string;
export declare const prisma: PrismaClient;
export declare let isReseting: boolean;
export declare let isReset: boolean;
export declare let isSeed: boolean;
export declare function saveDb(): Promise<void>;
export declare function disconnect(): Promise<boolean>;
export declare function connect(): Promise<boolean>;
