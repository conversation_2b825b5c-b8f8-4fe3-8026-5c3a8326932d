"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.prisma = void 0;
const client_1 = require("@prisma/client");
const child_process_1 = require("child_process");
const dotenv_1 = __importDefault(require("dotenv"));
if (process.env.DATABASE_URL === undefined) {
    dotenv_1.default.config();
}
exports.prisma = new client_1.PrismaClient();
const checkAndCreateDatabase = async () => {
    const dbUrl = new URL(process.env.DATABASE_URL);
    const dbName = dbUrl.pathname.split('/')[1];
    const dbUser = dbUrl.username;
    const dbHost = dbUrl.hostname;
    const dbPort = dbUrl.port;
    const dbPassword = dbUrl.password;
    if (!dbName) {
        console.error('Database name could not be extracted from DATABASE_URL.');
        return;
    }
    const createDbCommand = `PGPASSWORD="${dbPassword}" createdb -h ${dbHost} -p ${dbPort} -U ${dbUser} ${dbName}`;
    try {
        await execPromise(createDbCommand);
        console.log(`Database ${dbName} created.`);
        if (process.env.NODE_STAGE === ' staging')
            await execPromise('npm run prisma:sync');
    }
    catch (error) {
        if (error && error.toString().indexOf('already exists') > 0) {
            console.log(`Database ${dbName} already exists.`);
        }
        else {
            console.log('Error attempting to create database:', error);
        }
    }
};
const execPromise = async (cmd) => {
    return new Promise((resolve, reject) => {
        (0, child_process_1.exec)(cmd, (error, stdout, stderr) => {
            if (error) {
                reject(error);
                return;
            }
            resolve(stdout.trim());
        });
    });
};
checkAndCreateDatabase().catch(error => {
    console.error('Failed to initialize database:', error);
});
//# sourceMappingURL=init-db.js.map