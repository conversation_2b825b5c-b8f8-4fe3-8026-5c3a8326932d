"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContentFetcher = void 0;
const cheerio_1 = __importDefault(require("cheerio"));
const promises_1 = __importDefault(require("fs/promises"));
const axios_1 = __importDefault(require("axios"));
const Cache_1 = require("./Cache");
const apiConfig_1 = require("./apiConfig");
class ContentFetcher {
    cache;
    cacheKey = '';
    constructor(cacheKey) {
        this.cacheKey = cacheKey;
        this.cache = new Cache_1.Cache(apiConfig_1.APPLICATION_SETTINGS.cacheDirectory);
    }
    static isLinkValid(href, baseUrl) {
        const unwantedPatterns = /(?:subscribe|twitter|linkedin|facebook|instagram|advertise|publications|upgrade|privacy|newsletters)/;
        return (!!href &&
            !href.startsWith('http') &&
            !unwantedPatterns.test(href) &&
            !href.includes(baseUrl));
    }
    async processAllLinks(links) {
        const results = [];
        let accumulatedMappings = {};
        for (const url of links) {
            console.log(`Processing ${url}`);
            const data = await this.fetchAndProcessContent(url, accumulatedMappings);
            if (data) {
                results.push({ url: data.url, content: data.content });
                accumulatedMappings = data.mappings;
            }
        }
        return { contents: results, mappings: accumulatedMappings };
    }
    async fetchAndProcessContent(url, existingMappings) {
        try {
            const response = await axios_1.default.get(url);
            let body = response.data;
            body = body.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
            body = body.replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '');
            body = body.replace(/<(?!a|img)[^>]+>/gi, '');
            body = body.replace(/\s\s+/g, ' ');
            const $ = cheerio_1.default.load(body);
            $('[class]').removeAttr('class');
            $('[style]').removeAttr('style');
            const bodyHtml = $('body').html();
            const baseUrl = new URL(url).origin;
            let links = $('a')
                .map((_, element) => {
                const href = $(element).attr('href');
                if (ContentFetcher.isLinkValid(href, baseUrl)) {
                    return new URL(href, baseUrl).toString();
                }
                else {
                    return undefined;
                }
            })
                .get()
                .filter((link) => {
                return (link !== baseUrl &&
                    link !== baseUrl + '/' &&
                    link !== `${baseUrl}/authors`);
            });
            if (links.length === 0) {
                const { updatedHtml, mappings } = await this.replaceAndStoreMappings(bodyHtml, existingMappings);
                return { url, content: updatedHtml, mappings };
            }
            const articleUrl = links[0];
            const articleResponse = await axios_1.default.get(articleUrl);
            let articleBody = articleResponse.data;
            articleBody = articleBody.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
            articleBody = articleBody.replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '');
            articleBody = articleBody.replace(/<(?!a|img)[^>]+>/gi, '');
            articleBody = articleBody.replace(/\s\s+/g, ' ');
            const article$ = cheerio_1.default.load(articleBody);
            article$('[class]').removeAttr('class');
            article$('[style]').removeAttr('style');
            const articleBodyHtml = article$('body').html();
            const { updatedHtml, mappings } = await this.replaceAndStoreMappings(articleBodyHtml, existingMappings);
            return { url: articleUrl, content: updatedHtml, mappings };
        }
        catch (error) {
            console.error(`Error fetching or processing content for ${url}:`, error);
            return null;
        }
    }
    async replaceAndStoreMappings(html, existingMapping) {
        const $ = cheerio_1.default.load(html);
        const mapping = { ...existingMapping };
        const findHighestId = (prefix) => {
            return Object.keys(mapping)
                .filter(key => key.startsWith(prefix))
                .reduce((max, key) => {
                const id = parseInt(key.replace(prefix, ''), 10);
                return id > max ? id : max;
            }, 0);
        };
        let hrefCounter = findHighestId('dummy_href_') + 1;
        let srcCounter = findHighestId('dummy_src_') + 1;
        let srcsetCounter = findHighestId('dummy_srcset_') + 1;
        $('a[href]').each((_, element) => {
            const href = $(element).attr('href');
            if (href) {
                const id = `dummy_href_${hrefCounter++}`;
                mapping[id] = href;
                $(element).attr('href', id);
            }
        });
        $('img[src]').each((_, element) => {
            const src = $(element).attr('src');
            if (src) {
                const id = `dummy_src_${srcCounter++}`;
                mapping[id] = src;
                $(element).attr('src', id);
            }
        });
        $('img[srcset]').each((_, element) => {
            const srcset = $(element).attr('srcset');
            if (srcset) {
                const id = `dummy_srcset_${srcsetCounter++}`;
                mapping[id] = srcset;
                $(element).attr('srcset', id);
            }
        });
        const updatedHtml = $('body').html() || html;
        return { updatedHtml, mappings: mapping };
    }
    async restoreOriginalData(dummyHtml, jsonFilePath) {
        const $ = cheerio_1.default.load(dummyHtml);
        const mapping = JSON.parse(await promises_1.default.readFile(jsonFilePath, 'utf-8'));
        $('a[href]').each((_, element) => {
            const id = $(element).attr('href');
            if (id && mapping[id]) {
                $(element).attr('href', mapping[id]);
            }
        });
        $('img[src]').each((_, element) => {
            const id = $(element).attr('src');
            if (id && mapping[id]) {
                $(element).attr('src', mapping[id]);
            }
        });
        $('img[srcset]').each((_, element) => {
            const id = $(element).attr('srcset');
            if (id && mapping[id]) {
                $(element).attr('srcset', mapping[id]);
            }
        });
        return $.html();
    }
    async replaceDummyLinks(content, mapping) {
        let updatedContent = content;
        for (const [dummy, actual] of Object.entries(mapping)) {
            const regex = new RegExp(`\\b${dummy}\\b`, 'gi');
            updatedContent = updatedContent.replace(regex, actual);
        }
        return updatedContent;
    }
}
exports.ContentFetcher = ContentFetcher;
//# sourceMappingURL=ContentFetcher.js.map