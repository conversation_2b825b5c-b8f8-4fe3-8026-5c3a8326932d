"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Cache = void 0;
const fs_1 = require("fs");
const path_1 = __importDefault(require("path"));
class Cache {
    cacheDirectory;
    constructor(cacheDirectory = './cache') {
        this.cacheDirectory = cacheDirectory;
        if (!(0, fs_1.existsSync)(this.cacheDirectory)) {
            (0, fs_1.mkdirSync)(this.cacheDirectory, { recursive: true });
        }
    }
    generateCacheKey(prompt) {
        return 'cache_' + prompt.replace(/\W+/g, '_').substring(0, 50);
    }
    getFromCache(cacheKey) {
        const cachePath = path_1.default.join(this.cacheDirectory, cacheKey);
        if ((0, fs_1.existsSync)(cachePath)) {
            return (0, fs_1.readFileSync)(cachePath, 'utf8');
        }
        return null;
    }
    getJsonFromCache(cacheKey) {
        const content = this.getFromCache(cacheKey + '.json');
        if (content == '{}')
            return null;
        return JSON.parse(content);
    }
    saveJsonToCache(cacheKey, content) {
        this.saveToCache(cacheKey + '.json', JSON.stringify(content));
    }
    saveToCache(cacheKey, content) {
        const cachePath = path_1.default.join(this.cacheDirectory, cacheKey);
        console.log('Saving to cache:', cachePath);
        const folderPath = path_1.default.dirname(cachePath);
        console.log('folderPath', folderPath);
        if (!(0, fs_1.existsSync)(folderPath)) {
            (0, fs_1.mkdirSync)(folderPath, { recursive: true });
        }
        (0, fs_1.writeFileSync)(cachePath, content, 'utf8');
    }
    cacheExist(cacheKey) {
        const cachePath = path_1.default.join(this.cacheDirectory, cacheKey);
        return (0, fs_1.existsSync)(cachePath);
    }
    cacheFolderExist(cacheKey) {
        const cachePath = path_1.default.join(this.cacheDirectory, cacheKey);
        const folderPath = path_1.default.dirname(cachePath);
        return (0, fs_1.existsSync)(folderPath);
    }
    async createFile(cacheKey, fileContent = '', fileName = '') {
        const cachePath = path_1.default.join(this.cacheDirectory, cacheKey);
        if (!(0, fs_1.existsSync)(cachePath)) {
            (0, fs_1.mkdirSync)(cachePath, { recursive: true });
        }
        (0, fs_1.writeFileSync)(cachePath + `/${fileName}.md`, fileContent);
        return cachePath + `/${fileName}.md`;
    }
}
exports.Cache = Cache;
//# sourceMappingURL=Cache.js.map