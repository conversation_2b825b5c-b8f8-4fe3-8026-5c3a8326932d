export declare class Cache {
    private readonly cacheDirectory;
    constructor(cacheDirectory?: string);
    generateCacheKey(prompt: string): string;
    getFromCache(cacheKey: string): any | null;
    getJsonFromCache(cacheKey: string): any;
    saveJsonToCache(cacheKey: string, content: any): void;
    saveToCache(cacheKey: string, content: string): void;
    cacheExist(cacheKey: string): boolean;
    cacheFolderExist(cacheKey: string): boolean;
    createFile(cacheKey: string, fileContent?: string, fileName?: string): Promise<string>;
}
