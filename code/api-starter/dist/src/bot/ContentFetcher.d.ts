export declare class ContentFetcher {
    private readonly cache;
    private readonly cacheKey;
    constructor(cacheKey: string);
    private static isLinkValid;
    processAllLinks(links: string[]): Promise<{
        contents: {
            url: string;
            content: string;
        }[];
        mappings: {
            [key: string]: any;
        };
    }>;
    private fetchAndProcessContent;
    private replaceAndStoreMappings;
    restoreOriginalData(dummyHtml: string, jsonFilePath: string): Promise<string>;
    replaceDummyLinks(content: string, mapping: {
        [key: string]: string;
    }): Promise<string>;
}
