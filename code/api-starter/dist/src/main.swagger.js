"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppSwagger = void 0;
const swagger_1 = require("@nestjs/swagger");
const package_json_1 = require("../package.json");
const basePath = '/';
const options = new swagger_1.DocumentBuilder()
    .setTitle('API')
    .setDescription('API Documentation')
    .setVersion(package_json_1.version)
    .addBearerAuth()
    .addServer(basePath)
    .build();
const AppSwagger = (app) => {
    const swaggerDoc = swagger_1.SwaggerModule.createDocument(app, options);
    swagger_1.SwaggerModule.setup('/docs', app, swaggerDoc, {
        explorer: true,
        swaggerOptions: {
            docExpansion: 'list',
            filter: true,
            showRequestDuration: true,
        },
    });
    app.use('/docs/swagger.json', (req, res) => {
        res.header('Access-Control-Allow-Origin', '*');
        res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
        res.send(swaggerDoc);
    });
};
exports.AppSwagger = AppSwagger;
//# sourceMappingURL=main.swagger.js.map