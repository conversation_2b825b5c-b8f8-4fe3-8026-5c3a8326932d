"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.App = App;
exports.useApp = useApp;
require("source-map-support/register");
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const logging_interceptor_1 = require("./shared/interceptor/logging.interceptor");
const permission_interceptor_1 = require("./shared/interceptor/permission.interceptor");
const transform_interceptor_1 = require("./shared/interceptor/transform.interceptor");
const request_interceptor_1 = require("./shared/interceptor/request.interceptor");
const main_module_1 = require("./main.module");
const i18n_interceptor_1 = require("./shared/i18n/i18n.interceptor");
const config_service_1 = require("./shared/config/config.service");
async function App() {
    console.log('NODE_STAGE - ', process.env.NODE_STAGE);
    const app = await core_1.NestFactory.create(main_module_1.MainModule, {
        cors: true,
        snapshot: process.env.NODE_ENV !== 'production',
        abortOnError: false,
    });
    useApp(app);
    return app;
}
async function useApp(app) {
    const configService = app.get(config_service_1.ConfigService);
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        transform: true,
        stopAtFirstError: false,
        exceptionFactory: errors => {
            const formattedErrors = errors.reduce((acc, error) => {
                const constraints = error.constraints || {};
                acc[error.property] = Object.values(constraints);
                return acc;
            }, {});
            throw new common_1.BadRequestException({
                statusCode: 400,
                error: 'Validation Failed',
                message: 'Validation errors in your request',
                errors: formattedErrors,
            });
        },
    }));
    app.useGlobalInterceptors(new logging_interceptor_1.LoggingInterceptor(), new request_interceptor_1.RequestInterceptor(), new transform_interceptor_1.TransformInterceptor(), new permission_interceptor_1.PermissionInterceptor(), new i18n_interceptor_1.i18nRequestInterceptor(configService), new i18n_interceptor_1.i18nResponseInterceptor());
}
//# sourceMappingURL=app.js.map