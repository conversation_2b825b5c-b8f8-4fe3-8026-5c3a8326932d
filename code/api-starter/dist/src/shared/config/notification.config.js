"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NOTIFICATION_CONFIG = void 0;
exports.NOTIFICATION_CONFIG = {
    "commentCreatePostParams": {
        "requestId": "createComment_request_2",
        "requestPath": "/comment/create",
        "requestType": "POST",
        "useWorkflow": false,
        "dynamicRedirectionParams": [
            "id"
        ],
        "link": "/posts/:id",
        "target": "owner",
        "title": "New Comment on Your Post",
        "message": "A new comment has been added to your post."
    },
    "workflowPostCreatePostWorkflowParams": {
        "requestId": "generatePost_create_1",
        "requestPath": "/workflow/post/create",
        "requestType": "POST",
        "useWorkflow": true,
        "dynamicRedirectionParams": [
            "id"
        ],
        "link": "/posts/:id",
        "target": "me",
        "title": "Post Generated",
        "message": "Your blog post has been successfully created!"
    }
};
//# sourceMappingURL=notification.config.js.map