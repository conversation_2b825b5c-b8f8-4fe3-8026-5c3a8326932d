"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateComponentNamesString = exports.generateConfigString = void 0;
const generateConfigString = components => {
    return components
        .map(component => {
        const { componentName, fieldName, label, placeholder, type, required, requiredError, options, } = component;
        let componentString = `## ${componentName} Config Example:\n{\n  componentName: '${componentName}',\n  fieldName: '${fieldName}',\n  label: '${label}',\n`;
        if (placeholder) {
            componentString += `  placeholder: '${placeholder}',\n`;
        }
        if (type) {
            componentString += `  type: '${type}',\n`;
        }
        if (required) {
            componentString += `  required: ${required},\n  requiredError: '${requiredError}',\n`;
        }
        if (options) {
            componentString += `  options: ${JSON.stringify(options, null, 2)},\n`;
        }
        componentString += `},\n`;
        return componentString;
    })
        .join('\n');
};
exports.generateConfigString = generateConfigString;
const generateComponentNamesString = components => {
    return components
        .map(component => `\`${component.componentName}\``)
        .join(', ');
};
exports.generateComponentNamesString = generateComponentNamesString;
//# sourceMappingURL=component.js.map