"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.extractInterface = extractInterface;
const fs_1 = require("fs");
const helpers_1 = require("./helpers");
function extractInterface(componentName, interfaceName) {
    console.log({ componentName, interfaceName });
    let fileContents;
    try {
        fileContents = (0, fs_1.readFileSync)(`specs/code/${(0, helpers_1.capitalizeFirstLetter)(componentName)}.interface.txt`, 'utf8');
    }
    catch (error) {
        console.error(`Error reading file for component '${componentName}'`);
        return '';
    }
    try {
        const regex = new RegExp(`export interface ${interfaceName} {([\\s\\S]*?)}\n`, 'm');
        const match = regex.exec(fileContents);
        if (!match) {
            console.log('Interface not found.');
            return '';
        }
        return `export interface ${interfaceName} {${match[1]}}`;
    }
    catch (regexError) {
        console.error('Error processing regular expression');
        return '';
    }
}
//# sourceMappingURL=files.js.map