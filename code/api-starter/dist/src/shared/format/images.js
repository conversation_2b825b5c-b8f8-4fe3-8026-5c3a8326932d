"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getFileMetadata = exports.convertImageToSvg = exports.resizeImage = exports.downloadImage = void 0;
exports.removeImageBackground = removeImageBackground;
exports.transformIconColor = transformIconColor;
exports.invertColors = invertColors;
const sharp_1 = __importDefault(require("sharp"));
const fs_1 = require("fs");
const path_1 = require("path");
const jimp_1 = __importDefault(require("jimp"));
const potrace_1 = __importDefault(require("potrace"));
const axios_1 = __importDefault(require("axios"));
async function runRembg(inputPath, outputPath, debugMode = false) {
    try {
        const image = await jimp_1.default.read(inputPath);
        const width = image.bitmap.width;
        const height = image.bitmap.height;
        const getColor = (x, y) => {
            const idx = (y * width + x) << 2;
            return {
                r: image.bitmap.data[idx],
                g: image.bitmap.data[idx + 1],
                b: image.bitmap.data[idx + 2],
                a: image.bitmap.data[idx + 3],
            };
        };
        const setAlpha = (x, y, alpha) => {
            const idx = (y * width + x) << 2;
            image.bitmap.data[idx + 3] = alpha;
        };
        const isSimilar = (color1, color2, tolerance = 10) => {
            return (Math.abs(color1.r - color2.r) <= tolerance &&
                Math.abs(color1.g - color2.g) <= tolerance &&
                Math.abs(color1.b - color2.b) <= tolerance);
        };
        const borderColor = getColor(0, 0);
        for (let y = 0; y < height; y++) {
            let leftBorder = 0;
            let rightBorder = width - 1;
            while (leftBorder < width &&
                isSimilar(getColor(leftBorder, y), borderColor)) {
                setAlpha(leftBorder, y, 0);
                leftBorder++;
            }
            while (rightBorder > leftBorder &&
                isSimilar(getColor(rightBorder, y), borderColor)) {
                setAlpha(rightBorder, y, 0);
                rightBorder--;
            }
            if (leftBorder < rightBorder) {
                continue;
            }
        }
        const refinementRadius = 2;
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const currentAlpha = getColor(x, y).a;
                if (currentAlpha > 0 && currentAlpha < 255) {
                    let sum = 0;
                    let count = 0;
                    for (let dy = -refinementRadius; dy <= refinementRadius; dy++) {
                        for (let dx = -refinementRadius; dx <= refinementRadius; dx++) {
                            const nx = x + dx;
                            const ny = y + dy;
                            if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
                                sum += getColor(nx, ny).a;
                                count++;
                            }
                        }
                    }
                    const avgAlpha = sum / count;
                    setAlpha(x, y, avgAlpha > 128 ? 255 : 0);
                }
            }
        }
        if (debugMode) {
            const debugImage = new jimp_1.default(width, height, 0xff0000ff);
            image.scan(0, 0, width, height, (x, y, idx) => {
                if (image.bitmap.data[idx + 3] !== 0) {
                    debugImage.setPixelColor(image.getPixelColor(x, y), x, y);
                }
            });
            await debugImage.writeAsync(outputPath.replace('.png', '_debug.png'));
        }
        await image.writeAsync(outputPath);
        console.log('Image processed successfully: background removed and edges refined');
    }
    catch (err) {
        console.error('An error occurred:', err);
    }
}
async function removeWhiteBackground(inputPath, outputPath, debugMode = false) {
    try {
        const image = await jimp_1.default.read(inputPath);
        const width = image.bitmap.width;
        const height = image.bitmap.height;
        const getColor = (x, y) => {
            const idx = (y * width + x) << 2;
            return {
                r: image.bitmap.data[idx],
                g: image.bitmap.data[idx + 1],
                b: image.bitmap.data[idx + 2],
                a: image.bitmap.data[idx + 3],
            };
        };
        const setAlpha = (x, y, alpha) => {
            const idx = (y * width + x) << 2;
            image.bitmap.data[idx + 3] = alpha;
        };
        const isNearWhite = (color, tolerance = 150) => {
            return (color.r >= 255 - tolerance &&
                color.g >= 255 - tolerance &&
                color.b >= 255 - tolerance);
        };
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const color = getColor(x, y);
                if (isNearWhite(color)) {
                    setAlpha(x, y, 0);
                }
            }
        }
        const refinementRadius = 10;
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const currentAlpha = getColor(x, y).a;
                if (currentAlpha > 0 && currentAlpha < 255) {
                    let sum = 0;
                    let count = 0;
                    for (let dy = -refinementRadius; dy <= refinementRadius; dy++) {
                        for (let dx = -refinementRadius; dx <= refinementRadius; dx++) {
                            const nx = x + dx;
                            const ny = y + dy;
                            if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
                                sum += getColor(nx, ny).a;
                                count++;
                            }
                        }
                    }
                    const avgAlpha = sum / count;
                    setAlpha(x, y, avgAlpha > 128 ? 255 : 0);
                }
            }
        }
        if (debugMode) {
            const debugImage = new jimp_1.default(width, height, 0xff0000ff);
            image.scan(0, 0, width, height, (x, y, idx) => {
                if (image.bitmap.data[idx + 3] !== 0) {
                    debugImage.setPixelColor(image.getPixelColor(x, y), x, y);
                }
            });
            await debugImage.writeAsync(outputPath.replace('.png', '_debug.png'));
        }
        await image.writeAsync(outputPath);
        console.log('Image traitée avec succès : fond blanc supprimé et bords affinés');
        return outputPath;
    }
    catch (err) {
        console.error('Une erreur est survenue:', err);
    }
}
async function removeImageBackground(imgSource, outputPath) {
    try {
        await fs_1.promises.mkdir((0, path_1.dirname)(outputPath), { recursive: true });
        const tempOutputPath = `${outputPath}.temp.png`;
        await runRembg(imgSource, tempOutputPath);
        const tempWhiteOutputPath = `${outputPath}.temp.white.png`;
        await removeWhiteBackground(tempOutputPath, tempWhiteOutputPath);
        const buffer = await fs_1.promises.readFile(tempWhiteOutputPath);
        const image = (0, sharp_1.default)(buffer);
        const metadata = await image.metadata();
        const originalWidth = metadata.width;
        const originalHeight = metadata.height;
        const { info: trimInfo, data: trimmedBuffer } = await image
            .trim()
            .toBuffer({ resolveWithObject: true });
        const padding = Math.min(originalWidth, originalHeight) * 0.1;
        const newWidth = trimInfo.width + padding * 2;
        const newHeight = trimInfo.height + padding * 2;
        const resizedBuffer = await (0, sharp_1.default)(trimmedBuffer)
            .extend({
            top: Math.max(0, Math.floor((newHeight - trimInfo.height) / 2)),
            bottom: Math.max(0, Math.ceil((newHeight - trimInfo.height) / 2)),
            left: Math.max(0, Math.floor((newWidth - trimInfo.width) / 2)),
            right: Math.max(0, Math.ceil((newWidth - trimInfo.width) / 2)),
            background: { r: 0, g: 0, b: 0, alpha: 0 },
        })
            .toBuffer();
        await fs_1.promises.writeFile(outputPath, resizedBuffer);
        return outputPath;
    }
    catch (error) {
        throw error;
    }
}
async function transformIconColor(inputImagePath, outputImagePath, brightness) {
    try {
        await (0, sharp_1.default)(inputImagePath)
            .modulate({
            saturation: 0,
            brightness,
        })
            .linear(1.5, -0.5)
            .toFile(outputImagePath);
        console.log(`Icon color transformed and saved to: ${outputImagePath}`);
        return outputImagePath;
    }
    catch (error) {
        console.error('Error transforming icon color:', error);
        throw error;
    }
}
async function invertColors(inputImage, output) {
    try {
        const image = await jimp_1.default.read(inputImage);
        await image.invert().writeAsync(output);
        console.log(`Colors inverted and saved to: ${output}`);
        return output;
    }
    catch (error) {
        console.error('Error inverting colors:', error);
        throw error;
    }
}
async function convertWhiteToTransparent(inputPath, outputPath, tolerance = 100) {
    try {
        await (0, sharp_1.default)(inputPath)
            .ensureAlpha()
            .raw()
            .toBuffer({ resolveWithObject: true })
            .then(({ data, info }) => {
            const pixels = new Uint8ClampedArray(data.buffer);
            const { width, height, channels } = info;
            for (let i = 0; i < pixels.length; i += channels) {
                const r = pixels[i];
                const g = pixels[i + 1];
                const b = pixels[i + 2];
                if (r > 255 - tolerance &&
                    g > 255 - tolerance &&
                    b > 255 - tolerance) {
                    pixels[i + 3] = 0;
                }
            }
            return (0, sharp_1.default)(pixels, { raw: { width, height, channels } })
                .png()
                .toFile(outputPath);
        });
        console.log('Conversion du fond blanc en transparent terminée avec succès !');
    }
    catch (error) {
        console.error('Erreur lors de la conversion du fond blanc en transparent:', error);
    }
}
const downloadImage = async (url, convertFolderPath, filename = (0, path_1.basename)(url)) => {
    await fs_1.promises.mkdir(convertFolderPath, { recursive: true });
    const response = await axios_1.default.get(url, { responseType: 'arraybuffer' });
    const filePath = (0, path_1.join)(convertFolderPath, filename);
    await fs_1.promises.writeFile(filePath, response.data);
    return filePath;
};
exports.downloadImage = downloadImage;
const resizeImage = async (filePath, width, height) => {
    const tempFilename = `resized-${(0, path_1.basename)(filePath)}`;
    const resizedPath = (0, path_1.join)((0, path_1.dirname)(filePath), tempFilename);
    await (0, sharp_1.default)(filePath)
        .resize(width, height, {
        fit: sharp_1.default.fit.inside,
        withoutEnlargement: true,
    })
        .toFile(resizedPath);
    return resizedPath;
};
exports.resizeImage = resizeImage;
const convertFile = (filePath, convertFolderPath) => {
    return new Promise((resolve, reject) => {
        potrace_1.default.trace(filePath, async (err, svg) => {
            if (err) {
                return reject(err);
            }
            const filename = (0, path_1.basename)(filePath);
            const newFilename = filename.replace(/\.[^.]+$/, '.svg');
            const outputPath = (0, path_1.join)(convertFolderPath, newFilename);
            await fs_1.promises.writeFile(outputPath, svg);
            resolve(svg);
        });
    });
};
const convertImageToSvg = async (url, convertFolderPath, width, height) => {
    try {
        await fs_1.promises.mkdir(convertFolderPath, { recursive: true });
    }
    catch (error) {
        console.error(`Error creating directory ${convertFolderPath}:`, error);
        return;
    }
    try {
        const filePath = await (0, exports.downloadImage)(url, convertFolderPath);
        const resizedFilePath = await (0, exports.resizeImage)(filePath, width, height);
        const svg = await convertFile(resizedFilePath, convertFolderPath);
        return svg;
    }
    catch (error) {
        console.error(`Error processing ${url}:`, error);
    }
};
exports.convertImageToSvg = convertImageToSvg;
const getFileMetadata = (filePath) => {
    return {
        filename: (0, path_1.basename)(filePath),
        mimetype: `image/${(0, path_1.extname)(filePath).toLowerCase().substring(1)}`,
        encoding: '7bit',
        path: filePath,
        createReadStream: () => (0, fs_1.createReadStream)(filePath),
    };
};
exports.getFileMetadata = getFileMetadata;
//# sourceMappingURL=images.js.map