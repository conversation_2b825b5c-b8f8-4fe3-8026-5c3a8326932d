"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.schema = void 0;
exports.createJsonPrompt = createJsonPrompt;
exports.promptToJson = promptToJson;
exports.createPromptToJson = createPromptToJson;
const json5_1 = __importDefault(require("json5"));
const schema_1 = require("./schema");
exports.schema = {
    anyOf: schema_1.anyOf,
    string: schema_1.string,
    array: schema_1.array,
    boolean: schema_1.boolean,
    object: schema_1.object,
    enums: schema_1.enums,
};
function createJsonPrompt(prompt, json, exampleOutput = [
    {
        foo: 'bar',
    },
    {
        list: ['foo', 'bar'],
    },
]) {
    return `# How to respond to this prompt

## Output schema
The output structure must be a valid JSON object with this JSON schema:

${JSON.stringify((0, schema_1.object)(json, Object.keys(json), 'The JSON output'))}
- Use single quotes or escape double quotes to not break the json.

# Prompt
${prompt}

## Example output

${exampleOutput
        .map(example => `\`\`\`json
${JSON.stringify(example)}
\`\`\``)
        .join('\n\n')}
## Generate output
It is critical that you output a single valid JSON object with no markdown, extraneous text or wrappers.
`;
}
async function promptToJson(prompt, json, sendPrompt) {
    const response = await sendPrompt(createJsonPrompt(prompt, json));
    return json5_1.default.parse(response);
}
function createPromptToJson(params) {
    return async (prompt) => {
        const response = await params.sendPrompt(createJsonPrompt(prompt, params.schema, params.examples));
        return json5_1.default.parse(response);
    };
}
const jsonSchema = {
    name: (0, schema_1.string)('Name of the person'),
    age: {
        type: 'number',
        description: 'Age of the person',
    },
};
const prompt = 'Describe a person with a name and age.';
const result = createJsonPrompt(prompt, jsonSchema);
console.log(result);
//# sourceMappingURL=prompts.js.map