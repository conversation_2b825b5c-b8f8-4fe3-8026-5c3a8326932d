"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.array = array;
exports.boolean = boolean;
exports.enums = enums;
exports.anyOf = anyOf;
exports.string = string;
exports.object = object;
exports.func = func;
function array(items, description) {
    return {
        type: 'array',
        description,
        items,
    };
}
function boolean(description) {
    return {
        type: 'boolean',
        description,
    };
}
function enums(enums, description) {
    return {
        type: 'string',
        description,
        enum: enums,
    };
}
function anyOf(items) {
    return {
        anyOf: items,
    };
}
function string(description) {
    return {
        type: 'string',
        description,
    };
}
function object(properties, required, description) {
    return {
        type: 'object',
        description,
        properties,
        required,
    };
}
function func(name, parameters, description) {
    return {
        name,
        description,
        parameters,
    };
}
//# sourceMappingURL=schema.js.map