import { ValueTypes } from './schema';
import { anyOf, string, array, boolean, object, enums } from './schema';
export declare const schema: {
    anyOf: typeof anyOf;
    string: typeof string;
    array: typeof array;
    boolean: typeof boolean;
    object: typeof object;
    enums: typeof enums;
};
export declare function createJsonPrompt(prompt: string, json: Record<string, ValueTypes>, exampleOutput?: Array<{
    [key: string]: unknown;
}>): string;
export declare function promptToJson<T extends Record<string, ValueTypes>>(prompt: string, json: T, sendPrompt: (prompt: string) => Promise<string>): Promise<{ [K in keyof T]: T[K] extends infer T_1 ? T_1 extends T[K] ? T_1 extends import("./schema").EnumSchema<infer E extends string> ? E : T_1 extends import("./schema").BooleanSchema ? boolean : T_1 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 4> : T_1 extends import("./schema").StringSchema ? string : T_1 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_2 ? T_2 extends P_1[any] ? T_2 extends import("./schema").EnumSchema<infer E extends string> ? E : T_2 extends import("./schema").BooleanSchema ? boolean : T_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 3> : T_2 extends import("./schema").StringSchema ? string : T_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_3 ? T_3 extends P_1[any] ? T_3 extends import("./schema").EnumSchema<infer E extends string> ? E : T_3 extends import("./schema").BooleanSchema ? boolean : T_3 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 2> : T_3 extends import("./schema").StringSchema ? string : T_3 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_4 ? T_4 extends P_1[any] ? T_4 extends import("./schema").EnumSchema<infer E extends string> ? E : T_4 extends import("./schema").BooleanSchema ? boolean : T_4 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 1> : T_4 extends import("./schema").StringSchema ? string : T_4 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_5 ? T_5 extends P_1[any] ? T_5 extends import("./schema").EnumSchema<infer E extends string> ? E : T_5 extends import("./schema").BooleanSchema ? boolean : T_5 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : T_5 extends import("./schema").StringSchema ? string : T_5 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : T_5 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never : never : never : T_4 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never)[] : never : never : never : T_3 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 1> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_5 ? T_5 extends P_1[any] ? T_5 extends import("./schema").EnumSchema<infer E extends string> ? E : T_5 extends import("./schema").BooleanSchema ? boolean : T_5 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : T_5 extends import("./schema").StringSchema ? string : T_5 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : T_5 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never)[] : never)[] : never : never : never : T_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 2> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_4 ? T_4 extends P_1[any] ? T_4 extends import("./schema").EnumSchema<infer E extends string> ? E : T_4 extends import("./schema").BooleanSchema ? boolean : T_4 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 1> : T_4 extends import("./schema").StringSchema ? string : T_4 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_5 ? T_5 extends P_1[any] ? T_5 extends import("./schema").EnumSchema<infer E extends string> ? E : T_5 extends import("./schema").BooleanSchema ? boolean : T_5 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : T_5 extends import("./schema").StringSchema ? string : T_5 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : T_5 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never : never : never : T_4 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never)[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 1> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_5 ? T_5 extends P_1[any] ? T_5 extends import("./schema").EnumSchema<infer E extends string> ? E : T_5 extends import("./schema").BooleanSchema ? boolean : T_5 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : T_5 extends import("./schema").StringSchema ? string : T_5 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : T_5 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never)[] : never)[] : never)[] : never : never : never : T_1 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 3> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_3 ? T_3 extends P_1[any] ? T_3 extends import("./schema").EnumSchema<infer E extends string> ? E : T_3 extends import("./schema").BooleanSchema ? boolean : T_3 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 2> : T_3 extends import("./schema").StringSchema ? string : T_3 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_4 ? T_4 extends P_1[any] ? T_4 extends import("./schema").EnumSchema<infer E extends string> ? E : T_4 extends import("./schema").BooleanSchema ? boolean : T_4 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 1> : T_4 extends import("./schema").StringSchema ? string : T_4 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_5 ? T_5 extends P_1[any] ? T_5 extends import("./schema").EnumSchema<infer E extends string> ? E : T_5 extends import("./schema").BooleanSchema ? boolean : T_5 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : T_5 extends import("./schema").StringSchema ? string : T_5 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : T_5 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never : never : never : T_4 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never)[] : never : never : never : T_3 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 1> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_5 ? T_5 extends P_1[any] ? T_5 extends import("./schema").EnumSchema<infer E extends string> ? E : T_5 extends import("./schema").BooleanSchema ? boolean : T_5 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : T_5 extends import("./schema").StringSchema ? string : T_5 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : T_5 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never)[] : never)[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 2> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_4 ? T_4 extends P_1[any] ? T_4 extends import("./schema").EnumSchema<infer E extends string> ? E : T_4 extends import("./schema").BooleanSchema ? boolean : T_4 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 1> : T_4 extends import("./schema").StringSchema ? string : T_4 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_5 ? T_5 extends P_1[any] ? T_5 extends import("./schema").EnumSchema<infer E extends string> ? E : T_5 extends import("./schema").BooleanSchema ? boolean : T_5 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : T_5 extends import("./schema").StringSchema ? string : T_5 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : T_5 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never : never : never : T_4 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never)[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 1> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_5 ? T_5 extends P_1[any] ? T_5 extends import("./schema").EnumSchema<infer E extends string> ? E : T_5 extends import("./schema").BooleanSchema ? boolean : T_5 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : T_5 extends import("./schema").StringSchema ? string : T_5 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : T_5 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never)[] : never)[] : never)[] : never)[] : never : never : never; }>;
export declare function createPromptToJson<T extends Record<string, ValueTypes>>(params: {
    schema: T;
    sendPrompt: (prompt: string) => Promise<string>;
    examples?: Array<{
        [key: string]: unknown;
    }>;
}): (prompt: string) => Promise<{ [K in keyof T]: T[K] extends infer T_1 ? T_1 extends T[K] ? T_1 extends import("./schema").EnumSchema<infer E extends string> ? E : T_1 extends import("./schema").BooleanSchema ? boolean : T_1 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 4> : T_1 extends import("./schema").StringSchema ? string : T_1 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_2 ? T_2 extends P_1[any] ? T_2 extends import("./schema").EnumSchema<infer E extends string> ? E : T_2 extends import("./schema").BooleanSchema ? boolean : T_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 3> : T_2 extends import("./schema").StringSchema ? string : T_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_3 ? T_3 extends P_1[any] ? T_3 extends import("./schema").EnumSchema<infer E extends string> ? E : T_3 extends import("./schema").BooleanSchema ? boolean : T_3 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 2> : T_3 extends import("./schema").StringSchema ? string : T_3 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_4 ? T_4 extends P_1[any] ? T_4 extends import("./schema").EnumSchema<infer E extends string> ? E : T_4 extends import("./schema").BooleanSchema ? boolean : T_4 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 1> : T_4 extends import("./schema").StringSchema ? string : T_4 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_5 ? T_5 extends P_1[any] ? T_5 extends import("./schema").EnumSchema<infer E extends string> ? E : T_5 extends import("./schema").BooleanSchema ? boolean : T_5 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : T_5 extends import("./schema").StringSchema ? string : T_5 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : T_5 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never : never : never : T_4 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never)[] : never : never : never : T_3 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 1> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_5 ? T_5 extends P_1[any] ? T_5 extends import("./schema").EnumSchema<infer E extends string> ? E : T_5 extends import("./schema").BooleanSchema ? boolean : T_5 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : T_5 extends import("./schema").StringSchema ? string : T_5 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : T_5 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never)[] : never)[] : never : never : never : T_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 2> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_4 ? T_4 extends P_1[any] ? T_4 extends import("./schema").EnumSchema<infer E extends string> ? E : T_4 extends import("./schema").BooleanSchema ? boolean : T_4 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 1> : T_4 extends import("./schema").StringSchema ? string : T_4 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_5 ? T_5 extends P_1[any] ? T_5 extends import("./schema").EnumSchema<infer E extends string> ? E : T_5 extends import("./schema").BooleanSchema ? boolean : T_5 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : T_5 extends import("./schema").StringSchema ? string : T_5 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : T_5 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never : never : never : T_4 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never)[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 1> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_5 ? T_5 extends P_1[any] ? T_5 extends import("./schema").EnumSchema<infer E extends string> ? E : T_5 extends import("./schema").BooleanSchema ? boolean : T_5 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : T_5 extends import("./schema").StringSchema ? string : T_5 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : T_5 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never)[] : never)[] : never)[] : never : never : never : T_1 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 3> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_3 ? T_3 extends P_1[any] ? T_3 extends import("./schema").EnumSchema<infer E extends string> ? E : T_3 extends import("./schema").BooleanSchema ? boolean : T_3 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 2> : T_3 extends import("./schema").StringSchema ? string : T_3 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_4 ? T_4 extends P_1[any] ? T_4 extends import("./schema").EnumSchema<infer E extends string> ? E : T_4 extends import("./schema").BooleanSchema ? boolean : T_4 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 1> : T_4 extends import("./schema").StringSchema ? string : T_4 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_5 ? T_5 extends P_1[any] ? T_5 extends import("./schema").EnumSchema<infer E extends string> ? E : T_5 extends import("./schema").BooleanSchema ? boolean : T_5 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : T_5 extends import("./schema").StringSchema ? string : T_5 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : T_5 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never : never : never : T_4 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never)[] : never : never : never : T_3 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 1> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_5 ? T_5 extends P_1[any] ? T_5 extends import("./schema").EnumSchema<infer E extends string> ? E : T_5 extends import("./schema").BooleanSchema ? boolean : T_5 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : T_5 extends import("./schema").StringSchema ? string : T_5 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : T_5 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never)[] : never)[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 2> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_4 ? T_4 extends P_1[any] ? T_4 extends import("./schema").EnumSchema<infer E extends string> ? E : T_4 extends import("./schema").BooleanSchema ? boolean : T_4 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 1> : T_4 extends import("./schema").StringSchema ? string : T_4 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_5 ? T_5 extends P_1[any] ? T_5 extends import("./schema").EnumSchema<infer E extends string> ? E : T_5 extends import("./schema").BooleanSchema ? boolean : T_5 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : T_5 extends import("./schema").StringSchema ? string : T_5 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : T_5 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never : never : never : T_4 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never)[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 1> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_5 ? T_5 extends P_1[any] ? T_5 extends import("./schema").EnumSchema<infer E extends string> ? E : T_5 extends import("./schema").BooleanSchema ? boolean : T_5 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : T_5 extends import("./schema").StringSchema ? string : T_5 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : T_5 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, 0> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? P_1[any] extends infer T_6 ? T_6 extends P_1[any] ? T_6 extends import("./schema").EnumSchema<infer E extends string> ? E : T_6 extends import("./schema").BooleanSchema ? boolean : T_6 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : T_6 extends import("./schema").StringSchema ? string : T_6 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : T_6 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never : never : never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? (P_2 extends import("./schema").EnumSchema<infer E extends string> ? E : P_2 extends import("./schema").BooleanSchema ? boolean : P_2 extends import("./schema").ObjectSchema<infer P extends Record<string, ValueTypes>, infer R extends (keyof infer P extends Record<string, ValueTypes>)[]> ? import("./schema").ExtractObject<P, R, never> : P_2 extends import("./schema").StringSchema ? string : P_2 extends import("./schema").AnyOf<infer P_1 extends ValueTypes> ? never : P_2 extends import("./schema").ArraySchema<infer P_2 extends ValueTypes> ? never[] : never)[] : never)[] : never)[] : never)[] : never)[] : never : never : never; }>;
