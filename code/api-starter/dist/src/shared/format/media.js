"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getFileMetadata = exports.downloadMedia = void 0;
const fs_1 = require("fs");
const path_1 = require("path");
const axios_1 = __importDefault(require("axios"));
const downloadMedia = async (medias, convertFolderPath, type = 'image') => {
    let url;
    if (type === 'image') {
        url = Array.isArray(medias) ? medias[0] : medias;
    }
    else {
        url = typeof medias === 'string' ? medias : medias[0];
    }
    if (url.startsWith('data:')) {
        const matches = url.match(/^data:([A-Za-z-+\/]+);base64,(.+)$/);
        if (!matches) {
            throw new Error('Invalid base64 data URL format');
        }
        const mimeType = matches[1];
        const base64Data = matches[2];
        const extension = mimeType.split('/')[1];
        const filename = `image-${Date.now()}.${extension}`;
        await fs_1.promises.mkdir(convertFolderPath, { recursive: true });
        const filePath = (0, path_1.join)(convertFolderPath, filename);
        await fs_1.promises.writeFile(filePath, base64Data, 'base64');
        return filePath;
    }
    if (!url || typeof url !== 'string' || !/^https?:\/\//.test(url)) {
        throw new Error(`Invalid media URL: ${url}`);
    }
    const filename = (0, path_1.basename)(url);
    await fs_1.promises.mkdir(convertFolderPath, { recursive: true });
    const response = await axios_1.default.get(url, { responseType: 'arraybuffer' });
    const filePath = (0, path_1.join)(convertFolderPath, filename);
    await fs_1.promises.writeFile(filePath, response.data);
    return filePath;
};
exports.downloadMedia = downloadMedia;
const getFileMetadata = (filePath, mediaType = 'image') => {
    return {
        filename: (0, path_1.basename)(filePath),
        mimetype: `${mediaType}/${(0, path_1.extname)(filePath).toLowerCase().substring(1)}`,
        encoding: '7bit',
        path: filePath,
        createReadStream: () => (0, fs_1.createReadStream)(filePath),
    };
};
exports.getFileMetadata = getFileMetadata;
//# sourceMappingURL=media.js.map