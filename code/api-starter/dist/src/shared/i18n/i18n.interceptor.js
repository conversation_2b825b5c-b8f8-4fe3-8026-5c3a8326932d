"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.i18nRequestInterceptor = exports.i18nResponseInterceptor = void 0;
const common_1 = require("@nestjs/common");
const config_service_1 = require("../config/config.service");
const i18n_utils_1 = require("./i18n.utils");
const operators_1 = require("rxjs/operators");
const client_1 = require("@prisma/client");
const translate = (data, locale) => {
    if (data && Array.isArray(data)) {
        for (const y in data) {
            data[y] = translate(data[y], locale);
        }
        return data;
    }
    else if (data && typeof data === 'object') {
        let { translations, ...newData } = data;
        if (translations &&
            translations !== null &&
            translations[locale] !== undefined) {
            for (const i in newData) {
                if (translations[locale][i] !== undefined) {
                    newData[i] = translations[locale][i];
                }
            }
        }
        return newData;
    }
    return data;
};
class i18nResponseInterceptor {
    intercept(context, next) {
        let ctx = (0, i18n_utils_1.getContextObject)(context);
        return next.handle().pipe((0, operators_1.map)(data => translate(data, ctx.locale)));
    }
}
exports.i18nResponseInterceptor = i18nResponseInterceptor;
let i18nRequestInterceptor = class i18nRequestInterceptor {
    configService;
    fallbackLocale = 'en';
    constructor(configService) {
        this.configService = configService;
        this.fallbackLocale = configService.get('DEFAULT_LANGUAGE');
    }
    getLocale(locale) {
        let language = locale.toLowerCase().split('-')[0];
        if (client_1.Locale[language] !== undefined) {
            return language;
        }
        return this.fallbackLocale;
    }
    async intercept(context, next) {
        let ctx = (0, i18n_utils_1.getContextObject)(context);
        if (ctx) {
            let locale = ctx.headers['locale'] ||
                ctx.headers['accept-language'] ||
                this.fallbackLocale;
            if (locale && locale.indexOf(',')) {
                locale = locale.split(',')[0];
            }
            ctx.locale = this.getLocale(locale);
        }
        return next.handle();
    }
};
exports.i18nRequestInterceptor = i18nRequestInterceptor;
exports.i18nRequestInterceptor = i18nRequestInterceptor = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_service_1.ConfigService])
], i18nRequestInterceptor);
//# sourceMappingURL=i18n.interceptor.js.map