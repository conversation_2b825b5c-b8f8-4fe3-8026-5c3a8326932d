import { NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { ConfigService } from '../config/config.service';
export interface Response<T> {
    data: T;
}
export declare class i18nResponseInterceptor<T> implements NestInterceptor<T, Response<T>> {
    intercept(context: ExecutionContext, next: CallHandler): Observable<Response<T>>;
}
export declare class i18nRequestInterceptor implements NestInterceptor {
    private readonly configService;
    fallbackLocale: string;
    constructor(configService: ConfigService);
    getLocale(locale: any): any;
    intercept(context: ExecutionContext, next: CallHandler<any>): Promise<Observable<any>>;
}
