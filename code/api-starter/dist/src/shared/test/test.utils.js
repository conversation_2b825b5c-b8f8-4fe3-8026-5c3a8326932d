"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.testUseCase = exports.getUrl = exports.getTestName = exports.testStory = exports.testAuthToken = void 0;
const supertest_1 = __importDefault(require("supertest"));
const querystring_1 = __importDefault(require("querystring"));
const lodash_1 = require("lodash");
const seed_data_1 = require("../seed/seed.data");
const seed_utils_1 = require("../seed/seed.utils");
const env_utils_1 = require("../env/env.utils");
const fs_1 = require("fs");
const process_1 = require("process");
const tracer_1 = __importDefault(require("tracer"));
const acl_utils_1 = require("../../acl/acl.utils");
const testing_1 = require("@nestjs/testing");
const main_module_1 = require("../../main.module");
const app_1 = require("../../app");
const path_1 = __importDefault(require("path"));
const logDir = './logs';
if (!(0, fs_1.existsSync)(logDir)) {
    (0, fs_1.mkdirSync)(logDir);
}
else {
    const files = (0, fs_1.readdirSync)(logDir);
    files.forEach(file => {
        const filePath = path_1.default.join(logDir, file);
        try {
            if ((0, fs_1.statSync)(filePath).isFile()) {
                (0, fs_1.unlinkSync)(filePath);
            }
        }
        catch (e) {
        }
    });
}
if (!(0, env_utils_1.isTestEnv)()) {
    console.debug = () => { };
}
else {
    const logger = tracer_1.default.dailyfile({
        root: logDir,
        maxLogFiles: 1,
        format: [
            '{{timestamp}} <{{title}}> {{message}}',
            {
                debug: '{{message}}',
                info: '{{message}}',
                warn: '{{message}}',
                log: '{{message}}',
                error: '{{timestamp}} <{{title}}> {{message}} (in {{file}}:{{line}})\nCall Stack:\n{{stack}}',
            },
        ],
        dateformat: 'HH:MM:ss.L',
        preprocess: function (data) {
            data.title = data.title.toUpperCase();
        },
    });
    console.debug = logger.debug;
    console.info = logger.info;
    console.warn = logger.warn;
    console.error = logger.error;
    console.log = logger.log;
}
let port = 3000;
jest.setTimeout(240000);
let app;
let request = () => (0, supertest_1.default)(app.getHttpServer());
beforeAll(async () => {
    const module = await testing_1.Test.createTestingModule({
        imports: [main_module_1.MainModule],
    }).compile();
    app = module.createNestApplication();
    (0, app_1.useApp)(app);
    await app.init();
    const instance = await app.getHttpAdapter().getInstance();
    console.log(instance);
    await (0, seed_data_1.importSeed)();
});
afterEach(() => {
    if ((0, env_utils_1.isTestEnv)()) {
    }
});
let tokens = {};
const testAuthToken = async (param) => {
    if (tokens[param] !== undefined)
        return tokens[param];
    const auth = await (0, seed_utils_1.formatString)('seed.' + param);
    if (auth === undefined) {
        console.log('auth', auth);
        console.error('Authentification Error with user :' + param);
        (0, process_1.exit)(0);
    }
    const res = await request()
        .post(`/auth/${(0, acl_utils_1.isAdmin)(auth.result.role) ? 'admin' : 'user'}/signIn`)
        .send({ email: auth.params[0].email, password: auth.params[0].password });
    if (res.body.data !== undefined) {
        tokens[param] = 'Bearer ' + res.body.data.token;
        return tokens[param];
    }
    else {
        console.error('Authentification Error with user :' + param);
        return '';
    }
};
exports.testAuthToken = testAuthToken;
const testStory = (story) => {
    if (story.skipTest !== undefined && story.skipTest == true)
        return;
    describe(`${story.operation.summary} - ${story.route} [${story.method}]  `, () => {
        for (const responseCode in story.codes) {
            const code = story.codes[responseCode];
            let storyParams = {};
            if (code.story) {
                try {
                    if (code.story.query !== undefined) {
                        storyParams.query = (0, seed_utils_1.formatParams)(code.story.query);
                    }
                    if (code.story.body !== undefined) {
                        storyParams.body = (0, seed_utils_1.formatParams)(code.story.body);
                    }
                    if (code.story.path !== undefined) {
                        storyParams.path = (0, seed_utils_1.formatParams)(code.story.path);
                    }
                }
                catch (e) {
                    storyParams.error = e.message;
                }
            }
            const uniqueId = Math.random().toString(36).substring(7);
            const logFile = `${uniqueId}-${Date.now()}.log`;
            describe(`${code.response.description} ./logs/${logFile} - ${story.filePath} `, () => {
                let res;
                let params = {};
                (0, fs_1.writeFileSync)(`./logs/${logFile}`, `=====\n
${story.operation.summary} - ${story.route} [${story.method}]\n
Expected Status : ${code.response.status}\n
Description : ${code.response.description}\n
=====\n
${code.response.description} : \n
${JSON.stringify(code.story)}\n  
=> ${JSON.stringify(storyParams)}\n
=====\n`);
                if (code.story) {
                    it(`Returned Status is ${code.response.status} `, async () => {
                        let url = (0, exports.getUrl)(story.route, code.story);
                        if (code.story.query !== undefined) {
                            url += '?' + querystring_1.default.stringify((0, seed_utils_1.formatParams)(code.story.query));
                        }
                        if (code.story.body !== undefined) {
                            params = (0, seed_utils_1.formatParams)(code.story.body);
                        }
                        const method = story.method.toLowerCase();
                        if (code.story.auth) {
                            const token = await (0, exports.testAuthToken)(code.story.auth);
                            if (code.story.body) {
                                if (code.story.file) {
                                    res = await request()[method](url)
                                        .attach('file', (0, seed_utils_1.formatString)(code.story.file))
                                        .set('Authorization', token)
                                        .set('x-log-path', logFile)
                                        .send(params);
                                }
                                else {
                                    res = await request()[method](url)
                                        .set('Authorization', token)
                                        .set('x-log-path', logFile)
                                        .send(params);
                                }
                            }
                            else {
                                if (code.story.file) {
                                    res = await request()[method](url)
                                        .attach('file', (0, seed_utils_1.formatString)(code.story.file))
                                        .set('Authorization', token)
                                        .set('x-log-path', logFile);
                                }
                                else {
                                    res = await request()[method](url)
                                        .set('Authorization', token)
                                        .set('x-log-path', logFile);
                                }
                            }
                        }
                        else {
                            if (code.story.file) {
                                if (!(0, fs_1.existsSync)((0, seed_utils_1.formatString)(code.story.file))) {
                                    throw new Error((0, seed_utils_1.formatString)(code.story.file) + "doesn't exist ");
                                }
                            }
                            if (code.story.body) {
                                if (code.story.file) {
                                    res = await request()[method](url)
                                        .attach('file', (0, seed_utils_1.formatString)(code.story.file))
                                        .set('x-log-path', logFile)
                                        .send(params);
                                }
                                else {
                                    res = await request()[method](url).send(params);
                                }
                            }
                            else {
                                if (code.story.file) {
                                    res = await request()[method](url)
                                        .attach('file', (0, seed_utils_1.formatString)(code.story.file))
                                        .set('x-log-path', logFile);
                                }
                                else {
                                    res = await request()[method](url);
                                }
                            }
                        }
                        if (res) {
                            if ((0, env_utils_1.isTestEnv)()) {
                                (0, fs_1.appendFileSync)(`./logs/${logFile}`, `\nTest Result : ${res.statusCode.toString() === responseCode}\n`);
                                expect(res.statusCode.toString()).toBe(responseCode);
                            }
                        }
                    });
                    if (code.tests) {
                        code.tests.map(test => {
                            if (test)
                                it((0, exports.getTestName)(test), async () => {
                                    let response = { success: false, data: null };
                                    try {
                                        response = JSON.parse(res.text);
                                    }
                                    catch (e) { }
                                    if (response) {
                                        try {
                                            if (res.body)
                                                response = res.body;
                                        }
                                        catch (e) { }
                                    }
                                    if (test.success) {
                                        await (0, exports.testUseCase)(response.success, test, params);
                                    }
                                    else {
                                        await (0, exports.testUseCase)(response.data, test, params);
                                    }
                                });
                        });
                    }
                }
            });
        }
    });
};
exports.testStory = testStory;
const getTestName = test => {
    let errorText = test.type;
    try {
        switch (test.type) {
            case 'equal':
                errorText = 'Equal : ' + JSON.stringify(test.result);
                break;
            case 'exists':
                errorText = 'Exists :' + JSON.stringify(Object.keys(test.result));
                break;
            case 'contains':
                errorText = 'Contains :' + JSON.stringify(Object.keys(test.result));
                break;
            default:
                errorText = test.name;
                break;
        }
    }
    catch (e) {
        console.warn(e);
    }
    return errorText;
};
exports.getTestName = getTestName;
const getUrl = (route, story) => {
    let url = route;
    if (story.path !== undefined) {
        const params = (0, seed_utils_1.formatParams)(story.path);
        for (const i in params) {
            if (url.indexOf(':' + i) !== -1) {
                url = url.replace(':' + i, params[i]);
            }
        }
    }
    return url;
};
exports.getUrl = getUrl;
const testUseCase = async (resultToTest, test, params) => {
    const resultToHave = test.success !== undefined ? test.success : (0, seed_utils_1.formatParams)(test.data, params);
    if ((0, env_utils_1.isTestEnv)()) {
        console.log('-------------------------------');
        console.log('Test  : ', test);
        console.log('Result to test = ', resultToTest);
        console.log('Result to have = ', resultToHave);
    }
    let expectedResult;
    switch (test.type) {
        case 'equal':
            if (typeof resultToHave === 'object' || Array.isArray(resultToHave)) {
                expectedResult = {};
                console.log('resultToTest', resultToTest);
                console.log('resultToHave', resultToHave);
                for (const indexToTest in resultToHave) {
                    expect(resultToTest[indexToTest]).toBe(resultToHave[indexToTest]);
                    if (resultToTest[indexToTest]) {
                        expectedResult[indexToTest] =
                            resultToTest[indexToTest] == resultToHave[indexToTest];
                    }
                }
            }
            else {
                expect(resultToTest).toBe(resultToHave);
                expectedResult = resultToTest == resultToHave;
            }
            break;
        case 'exists':
            expectedResult = {};
            for (const indexToTest in resultToHave) {
                if (resultToHave[indexToTest] === true) {
                    expect(resultToTest[indexToTest]).toBeDefined();
                    expectedResult[indexToTest] = resultToTest[indexToTest] !== undefined;
                }
                else {
                    expect(resultToTest[indexToTest]).toBeUndefined();
                    expectedResult[indexToTest] = resultToTest[indexToTest] === undefined;
                }
            }
            break;
        case 'contains':
            if (typeof resultToTest === 'object') {
                expect(resultToTest).toMatchObject(resultToHave);
            }
            else {
                expect(resultToTest).toContain(resultToHave);
            }
            if (Array.isArray(resultToTest)) {
                for (const i in resultToTest) {
                    expectedResult[i] = (0, lodash_1.isMatch)(resultToHave[i], resultToTest[i]);
                }
            }
            else if (typeof resultToTest === 'object') {
                expectedResult = (0, lodash_1.isMatch)(resultToHave, resultToTest);
            }
            break;
    }
    if ((0, env_utils_1.isTestEnv)()) {
        if (expectedResult) {
            console.log('Test Result : ', expectedResult);
            console.log('-------------------------------');
        }
        else {
            console.debug('Test  : ', test);
            console.debug('Result to Have = ', resultToHave);
            console.debug('Result tested = ', resultToTest);
            console.debug('Expected Result : ', expectedResult);
            console.debug('-------------------------------');
        }
    }
    return expectedResult;
};
exports.testUseCase = testUseCase;
//# sourceMappingURL=test.utils.js.map