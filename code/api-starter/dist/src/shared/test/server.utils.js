"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPortFree = void 0;
const env_utils_1 = require("../env/env.utils");
const find_free_port_1 = __importDefault(require("find-free-port"));
const test_lib_1 = require("./test.lib");
const getPortFree = async (port = 3000) => {
    return new Promise((ressolve, reject) => {
        (0, find_free_port_1.default)(port, (err, freePort) => {
            if (err)
                reject(err);
            else
                ressolve(freePort);
        });
    });
};
exports.getPortFree = getPortFree;
if ((0, env_utils_1.isTestEnv)()) {
    (0, test_lib_1.fileLog)();
}
//# sourceMappingURL=server.utils.js.map