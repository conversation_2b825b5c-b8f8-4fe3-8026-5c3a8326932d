"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.fileLog = void 0;
const tracer_1 = __importDefault(require("tracer"));
const fileLog = () => {
    const logger = tracer_1.default.dailyfile({
        root: './logs',
        maxLogFiles: 1,
        format: [
            '{{timestamp}} <{{title}}> {{message}}',
            {
                debug: '{{message}}',
                info: '{{message}}',
                warn: '{{message}}',
                log: '{{message}}',
                error: '{{timestamp}} <{{title}}> {{message}} (in {{file}}:{{line}})\nCall Stack:\n{{stack}}',
            },
        ],
        dateformat: 'HH:MM:ss.L',
        preprocess: function (data) {
            data.title = data.title.toUpperCase();
        },
    });
    console.debug = logger.debug;
    console.info = logger.info;
    console.warn = logger.warn;
    console.error = logger.error;
    console.log = logger.log;
};
exports.fileLog = fileLog;
//# sourceMappingURL=test.lib.js.map