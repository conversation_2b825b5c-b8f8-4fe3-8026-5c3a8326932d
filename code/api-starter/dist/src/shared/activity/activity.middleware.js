"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityMiddleware = void 0;
const common_1 = require("@nestjs/common");
const client_node_1 = require("@kubernetes/client-node");
const dotenv_1 = __importDefault(require("dotenv"));
const env_utils_1 = require("../env/env.utils");
let ActivityMiddleware = class ActivityMiddleware {
    kc;
    k8sAppsV1Api;
    receiveRequest = false;
    constructor() {
        this.kc = new client_node_1.KubeConfig();
        this.kc.loadFromCluster();
        this.k8sAppsV1Api = this.kc.makeApiClient(client_node_1.AppsV1Api);
        if (!(0, env_utils_1.isLocalEnv)()) {
            setInterval(() => {
                if (!this.receiveRequest) {
                    console.log('Aucune requête reçue depuis la dernière vérification, tentative de suppression du déploiement...');
                    this.exitApp();
                }
                this.receiveRequest = false;
            }, 10 * 60 * 1000);
        }
    }
    use(req, res, next) {
        this.receiveRequest = true;
        next();
    }
    async exitApp() {
        let env = process.env;
        if (env.DATABASE_URL !== undefined) {
            const config = dotenv_1.default.config();
            env = config.parsed !== undefined ? config.parsed : {};
        }
        console.log({ env });
        const deploymentName = `${env.NODE_ENV}-${env.CI_PROJECT_NAME}`;
        const namespace = env.PROJECT_NAMESPACE;
        const url = `http://develop-api-deployer.default.svc.cluster.local:80/delete`;
        try {
            const response = await fetch(`${url}`, {
                method: 'GET',
                headers: {
                    App: deploymentName,
                    Namespace: namespace,
                },
            });
            if (!response.ok) {
                throw new Error(`Erreur lors de  la suppression: ${response.statusText}`);
            }
            console.log("Déploiement supprimé en raison de l'inactivité.");
        }
        catch (error) {
            console.error('Erreur lors de la tentative de suppression du déploiement:', error);
        }
    }
};
exports.ActivityMiddleware = ActivityMiddleware;
exports.ActivityMiddleware = ActivityMiddleware = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], ActivityMiddleware);
//# sourceMappingURL=activity.middleware.js.map