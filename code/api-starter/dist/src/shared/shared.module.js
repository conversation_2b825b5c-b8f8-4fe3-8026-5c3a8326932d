"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SharedModule = void 0;
const common_1 = require("@nestjs/common");
const bugsnag_service_1 = require("./bugsnag/bugsnag.service");
const config_service_1 = require("./config/config.service");
const prisma_service_1 = require("./prisma/prisma.service");
const i18n_service_1 = require("./i18n/i18n.service");
const schedule_1 = require("@nestjs/schedule");
const acl_module_1 = require("../acl/acl.module");
let SharedModule = class SharedModule {
};
exports.SharedModule = SharedModule;
exports.SharedModule = SharedModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        imports: [schedule_1.ScheduleModule.forRoot(), acl_module_1.AclModule],
        providers: [config_service_1.ConfigService, bugsnag_service_1.BugsnagService, prisma_service_1.PrismaService, i18n_service_1.I18nService],
        exports: [
            config_service_1.ConfigService,
            bugsnag_service_1.BugsnagService,
            prisma_service_1.PrismaService,
            i18n_service_1.I18nService,
            acl_module_1.AclModule,
        ],
    })
], SharedModule);
//# sourceMappingURL=shared.module.js.map