"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpExceptionFilter = void 0;
const common_1 = require("@nestjs/common");
const bugsnag_service_1 = require("../bugsnag/bugsnag.service");
const env_utils_1 = require("../env/env.utils");
const i18n_service_1 = require("../i18n/i18n.service");
let HttpExceptionFilter = class HttpExceptionFilter {
    bugsnag;
    i18n;
    constructor(bugsnag, i18n) {
        this.bugsnag = bugsnag;
        this.i18n = i18n;
    }
    catch(exception, host) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const request = ctx.getRequest();
        const status = exception.getStatus();
        const stack = exception.stack.split('\n');
        if (!(0, env_utils_1.isLocalEnv)()) {
            this.bugsnag.client.notify(exception);
        }
        if (status === common_1.HttpStatus.UNAUTHORIZED) {
            if (typeof exception.message !== 'string') {
                exception.message =
                    exception.message || this.i18n.translate('UNAUTHORIZED');
            }
        }
        let res = {
            statusCode: status,
            error: exception.name,
            message: exception.message,
            timestamp: new Date().toISOString(),
            path: request ? request.url : null,
        };
        if ((stack[1] && (0, env_utils_1.isTestEnv)()) || (0, env_utils_1.isLocalEnv)()) {
            const match = stack[1].match(/\(([^:]+):(\d+):(\d+)\)/);
            if (match) {
                res.location = match[1];
                res.line = match[2];
                res.errorPath = match[1] + ':' + match[2];
                res.stack = stack;
            }
        }
        response.status(status).json(res);
    }
};
exports.HttpExceptionFilter = HttpExceptionFilter;
exports.HttpExceptionFilter = HttpExceptionFilter = __decorate([
    (0, common_1.Catch)(common_1.HttpException),
    __metadata("design:paramtypes", [bugsnag_service_1.BugsnagService,
        i18n_service_1.I18nService])
], HttpExceptionFilter);
//# sourceMappingURL=http-exception.filter.js.map