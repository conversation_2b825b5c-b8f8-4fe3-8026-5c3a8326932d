import { ArgumentsHost, ExceptionFilter, HttpException } from '@nestjs/common';
import { BugsnagService } from 'src/shared/bugsnag/bugsnag.service';
import { I18nService } from 'src/shared/i18n/i18n.service';
export declare class HttpExceptionFilter implements ExceptionFilter {
    private readonly bugsnag;
    private readonly i18n;
    constructor(bugsnag: BugsnagService, i18n: I18nService);
    catch(exception: HttpException, host: ArgumentsHost): void;
}
