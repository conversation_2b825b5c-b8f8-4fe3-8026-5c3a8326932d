"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorInvalidPhoneNumberFormat = exports.ErrorPhoneNumberIsRequired = void 0;
exports.serializePhoneNumber = serializePhoneNumber;
exports.appendsDefaultIDDCode = appendsDefaultIDDCode;
exports.validate = validate;
exports.generateA4DigitRandomVerificationCode = generateA4DigitRandomVerificationCode;
function serializePhoneNumber(phoneNumber) {
    const phoneNumberWithIDDCode = appendsDefaultIDDCode(phoneNumber);
    return phoneNumberWithIDDCode.replace(/ |-|\(|\)/g, '');
}
function appendsDefaultIDDCode(phoneNumber) {
    return phoneNumber &&
        typeof phoneNumber === 'string' &&
        phoneNumber.charAt(0) !== '+'
        ? `+52${phoneNumber}`
        : phoneNumber;
}
function validate(phoneNumber) {
    if (!phoneNumber || typeof phoneNumber !== 'string') {
        throw new ErrorPhoneNumberIsRequired();
    }
    const error = phoneNumber
        .split('')
        .some(digit => !/[0-9]|\+| |-|\(|\)/.test(digit));
    if (error) {
        throw new ErrorInvalidPhoneNumberFormat();
    }
    return true;
}
class ErrorPhoneNumberIsRequired extends Error {
    code;
    name;
    message;
}
exports.ErrorPhoneNumberIsRequired = ErrorPhoneNumberIsRequired;
class ErrorInvalidPhoneNumberFormat extends Error {
    code;
    name;
    message;
}
exports.ErrorInvalidPhoneNumberFormat = ErrorInvalidPhoneNumberFormat;
function generateA4DigitRandomVerificationCode() {
    return Math.floor(Math.random() * 9000) + 1000;
}
//# sourceMappingURL=phone.util.js.map