export declare function serializePhoneNumber(phoneNumber: string): string;
export declare function appendsD<PERSON>aultIDDCode(phoneNumber: string): string;
export declare function validate(phoneNumber: string): boolean;
export declare class ErrorPhoneNumberIsRequired extends Error {
    readonly code: 'ErrorPhoneNumberIsRequired';
    readonly name: 'ErrorPhoneNumberIsRequired';
    readonly message: 'Phone number could not be empty';
}
export declare class ErrorInvalidPhoneNumberFormat extends Error {
    readonly code: 'ErrorInvalidPhoneNumberFormat';
    readonly name: 'ErrorInvalidPhoneNumberFormat';
    readonly message: 'Phone number can only contain numbers, spaces, dashes, parenthesis and the plus symbol';
}
export declare function generateA4DigitRandomVerificationCode(): number;
