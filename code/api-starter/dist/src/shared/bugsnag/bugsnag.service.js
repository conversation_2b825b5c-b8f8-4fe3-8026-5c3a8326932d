"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BugsnagService = void 0;
const js_1 = __importDefault(require("@bugsnag/js"));
const common_1 = require("@nestjs/common");
const config_service_1 = require("../config/config.service");
const package_json_1 = require("../../../package.json");
const plugin_express_1 = __importDefault(require("@bugsnag/plugin-express"));
let BugsnagService = class BugsnagService {
    configService;
    client;
    constructor(configService) {
        this.configService = configService;
        const config = {
            apiKey: configService.get('BUGSNAG_KEY_API'),
            appVersion: package_json_1.version,
            hostname: package_json_1.name,
            plugins: [plugin_express_1.default],
            releaseStage: process.env.NODE_STAGE,
            enabledReleaseStages: ['production', 'preprod', 'staging', 'develop'],
            onError: function (event) {
                console.error(event);
            },
        };
        this.client = js_1.default.createClient(config);
    }
};
exports.BugsnagService = BugsnagService;
exports.BugsnagService = BugsnagService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_service_1.ConfigService])
], BugsnagService);
//# sourceMappingURL=bugsnag.service.js.map