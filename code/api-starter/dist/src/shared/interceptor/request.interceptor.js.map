{"version": 3, "file": "request.interceptor.js", "sourceRoot": "", "sources": ["../../../../src/shared/interceptor/request.interceptor.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAKwB;AAExB,gDAAiD;AACjD,mDAAgD;AAMzC,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;IAO7B,KAAK,CAAC,SAAS,CACb,OAAyB,EACzB,IAAiB;QAEjB,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QACpD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QAE1B,IAAI,IAAI,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC3C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;QACxC,CAAC;QAED,IAAI,CAAC,IAAA,2BAAe,GAAE,EAAE,CAAC;YACvB,KAAK,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBACrB,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;oBAC5D,IAAI,CAAC,CAAC,CAAC,GAAG,IAAA,yBAAY,EAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;gBACtC,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;IACvB,CAAC;CACF,CAAA;AA5BY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;GACA,kBAAkB,CA4B9B"}