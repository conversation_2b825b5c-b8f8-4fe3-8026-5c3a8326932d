{"version": 3, "file": "permission.interceptor.js", "sourceRoot": "", "sources": ["../../../../src/shared/interceptor/permission.interceptor.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAKwB;AAExB,8CAAmC;AAG5B,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IAChC,SAAS,CAAC,OAAyB,EAAE,IAAiB;QAEpD,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QACpD,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAElB,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CACvB,IAAA,eAAG,EAAC,IAAI,CAAC,EAAE;YAET,IAAI,OAAO,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;gBACrC,IAAI,CAAC;oBACH,OAAO,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACzC,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACX,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACnB,CAAC;YACH,CAAC;YAGD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;CACF,CAAA;AAtBY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;GACA,qBAAqB,CAsBjC"}