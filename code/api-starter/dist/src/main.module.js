"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MainModule = exports.rootModules = void 0;
const common_1 = require("@nestjs/common");
const test_middleware_1 = require("./shared/test/test.middleware");
const serve_static_1 = require("@nestjs/serve-static");
const path_1 = require("path");
const account_module_1 = require("./modules/account/account.module");
const admin_module_1 = require("./modules/admin/admin.module");
const analytic_module_1 = require("./modules/analytic/analytic.module");
const app_controller_1 = require("./modules/app/app.controller");
const auth_module_1 = require("./modules/auth/auth.module");
const comment_module_1 = require("./modules/comment/comment.module");
const exemple_module_1 = require("./modules/exemple/exemple.module");
const iap_module_1 = require("./modules/iap/iap.module");
const like_module_1 = require("./modules/like/like.module");
const news_module_1 = require("./modules/news/news.module");
const profile_module_1 = require("./modules/profile/profile.module");
const seed_module_1 = require("./modules/seed/seed.module");
const user_module_1 = require("./modules/user/user.module");
const shared_module_1 = require("./shared/shared.module");
const field_module_1 = require("./modules/field/field.module");
const report_module_1 = require("./modules/report/report.module");
const home_module_1 = require("./modules/home/<USER>");
const rating_module_1 = require("./modules/rating/rating.module");
const cron_module_1 = require("./modules/cron/cron.module");
const env_utils_1 = require("./shared/env/env.utils");
const activity_middleware_1 = require("./shared/activity/activity.middleware");
const payment_module_1 = require("./modules/payment/payment.module");
const workflow_module_1 = require("./modules/workflow/workflow.module");
const devtools_integration_1 = require("@nestjs/devtools-integration");
const item_module_1 = require("./modules/item/item.module");
const action_module_1 = require("./modules/action/action.module");
const access_module_1 = require("./modules/access/access.module");
const chat_module_1 = require("./modules/chat/chat.module");
exports.rootModules = [
    auth_module_1.AuthModule,
    admin_module_1.AdminModule,
    user_module_1.UserModule,
    account_module_1.AccountModule,
    profile_module_1.ProfileModule,
    home_module_1.HomeModule,
    comment_module_1.CommentModule,
    report_module_1.ReportModule,
    rating_module_1.RatingModule,
    news_module_1.NewsModule,
    like_module_1.LikeModule,
    iap_module_1.IapModule,
    cron_module_1.CronModule,
    field_module_1.FieldModule,
    exemple_module_1.ExempleModule,
    analytic_module_1.AnalyticModule,
    payment_module_1.PaymentModule,
    item_module_1.ItemModule,
    workflow_module_1.WorkflowModule,
    action_module_1.ActionModule,
    access_module_1.AccessModule,
    chat_module_1.ChatModule,
    serve_static_1.ServeStaticModule.forRoot({
        rootPath: (0, path_1.join)(__dirname, '..', 'static'),
    }),
];
let MainModule = class MainModule {
    configure(consumer) {
        if ((0, env_utils_1.isTestEnv)()) {
            consumer.apply(test_middleware_1.TestMiddleware).forRoutes('*');
        }
        consumer.apply(activity_middleware_1.ActivityMiddleware).forRoutes('*');
    }
};
exports.MainModule = MainModule;
exports.MainModule = MainModule = __decorate([
    (0, common_1.Module)({
        imports: (0, env_utils_1.isLocalEnv)() || (0, env_utils_1.isDevelop)()
            ? [
                shared_module_1.SharedModule,
                ...exports.rootModules,
                seed_module_1.SeedModule,
                devtools_integration_1.DevtoolsModule.register({
                    http: process.env.NODE_STAGE === 'debug',
                }),
            ]
            : [shared_module_1.SharedModule, ...exports.rootModules],
        controllers: [app_controller_1.AppController],
        providers: [],
        exports: [],
    })
], MainModule);
//# sourceMappingURL=main.module.js.map