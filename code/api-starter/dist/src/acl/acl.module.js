"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AclModule = void 0;
const common_1 = require("@nestjs/common");
const accesscontrol_1 = require("accesscontrol");
const acl_config_json_1 = __importDefault(require("./acl.config.json"));
let AclModule = class AclModule {
};
exports.AclModule = AclModule;
exports.AclModule = AclModule = __decorate([
    (0, common_1.Module)({
        providers: [
            {
                provide: 'AccessControl',
                useFactory: () => {
                    return new accesscontrol_1.AccessControl(acl_config_json_1.default);
                },
            },
        ],
        exports: ['AccessControl'],
    })
], AclModule);
//# sourceMappingURL=acl.module.js.map