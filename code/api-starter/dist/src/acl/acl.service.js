"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AclService = void 0;
const common_1 = require("@nestjs/common");
const accesscontrol_1 = require("accesscontrol");
const acl_config_json_1 = __importDefault(require("./acl.config.json"));
let AclService = class AclService {
    ac = new accesscontrol_1.AccessControl(acl_config_json_1.default);
};
exports.AclService = AclService;
exports.AclService = AclService = __decorate([
    (0, common_1.Injectable)()
], AclService);
//# sourceMappingURL=acl.service.js.map