"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.demoSeedConfig = void 0;
const admins_demo_1 = require("./admins.demo");
const users_demo_1 = require("./users.demo");
const comment_demo_1 = require("./comment.demo");
const field_1 = require("../seed/field");
const user_1 = require("../seed/auth/user/user");
const test_1 = require("../seed/auth/user/test");
const admin_1 = require("../seed/auth/admin/admin");
const contents_demo_1 = require("./contents.demo");
const news_1 = require("./news");
const analyticToViewEvent_demo_1 = require("./analytic/analyticToViewEvent.demo");
const account_1 = require("./account");
const creatorEvent_1 = require("../seed/auth/user/creatorEvent");
const like_1 = require("./like");
const rating_demo_1 = require("./rating.demo");
const usersForDataTable_1 = require("../seed/user/usersForDataTable");
exports.demoSeedConfig = {
    fieldDynamic: field_1.fieldDynamic,
    test: test_1.test,
    admin: admin_1.admin,
    user: user_1.user,
    creatorEvent: creatorEvent_1.creatorEvent,
    userDemos: users_demo_1.userDemos,
    adminDemosCreateUseCaseWithNoPicture: admins_demo_1.adminDemosCreateUseCaseWithNoPicture,
    adminDemosCreateUseCaseWithPicture: admins_demo_1.adminDemosCreateUseCaseWithPicture,
    adminDemoCreateUseCaseExistEmail: admins_demo_1.adminDemoCreateUseCaseExistEmail,
    demoContents: contents_demo_1.demoContents,
    newsToShowOnHomeDemo: news_1.newsToShowOnHomeDemo,
    newsToCoinsDemo: news_1.newsToCoinsDemo,
    newsToSubscriptionAndOrCoinsDemo: news_1.newsToSubscriptionAndOrCoinsDemo,
    newsToSubscriptionDemo: news_1.newsToSubscriptionDemo,
    analyticToViewEventDemo: analyticToViewEvent_demo_1.analyticToViewEventDemo,
    accountHaveCoins: account_1.accountHaveCoins,
    accountHaveCoinsAndSubs: account_1.accountHaveCoinsAndSubs,
    coinsAndSubsDemo: account_1.coinsAndSubsDemo,
    accountHaveSubs: account_1.accountHaveSubs,
    accountWithoutCoinsAndSubs: account_1.accountWithoutCoinsAndSubs,
    commentDemo: comment_demo_1.commentDemo,
    commentsWithReportsDemo: comment_demo_1.commentsWithReportsDemo,
    likeOnProfileDemo: like_1.likeOnProfileDemo,
    ratingDemo: rating_demo_1.ratingDemo,
    usersForDataTable: usersForDataTable_1.usersForDataTable,
};
exports.default = exports.demoSeedConfig;
//# sourceMappingURL=index.js.map