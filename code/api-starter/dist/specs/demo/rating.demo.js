"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ratingDemo = void 0;
const objects = [
    {
        objectId: 'seed.newsToRead.result.id',
        objectType: 'news',
        count: 5,
    },
    {
        objectId: 'seed.newsToRead.result.id',
        objectType: 'news',
        count: 2,
    },
];
exports.ratingDemo = Array.from({ length: 4 }, (x, index) => [
    {
        provider: 'RatingController',
        action: 'create',
        params: [
            'auth.user',
            {
                ...objects[index],
            },
        ],
    },
    {
        provider: 'RatingController',
        action: 'create',
        params: [
            'auth.test',
            {
                ...objects[index],
            },
        ],
    },
]).flatMap(x => x);
//# sourceMappingURL=rating.demo.js.map