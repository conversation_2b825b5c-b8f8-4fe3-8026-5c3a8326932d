"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.newsToSubscriptionDemo = void 0;
const client_1 = require("@prisma/client");
exports.newsToSubscriptionDemo = [
    {
        provider: 'NewsController',
        action: 'create',
        params: [
            {
                title: 'News that needs subscription',
            },
        ],
    },
    {
        provider: 'NewsController',
        action: 'update',
        params: [
            'result.id',
            {
                imageUrl: 'mock.banner',
                description: 'mock.paragraph',
                publicationDate: 'mock.today',
                category: client_1.NewsCategory.innewss,
                payWithCoins: false,
                payWithSubscription: true,
                priceWithCoins: 0,
                priceWithSubscription: 15,
            },
        ],
    },
];
//# sourceMappingURL=newsToSubscription.demo.js.map