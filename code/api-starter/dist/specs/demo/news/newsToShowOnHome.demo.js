"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.newsToShowOnHomeDemo = void 0;
const client_1 = require("@prisma/client");
exports.newsToShowOnHomeDemo = Array.from({ length: 2 }, (x, index) => [
    {
        provider: 'NewsController',
        action: 'create',
        params: [
            {
                title: 'News on Home ' + (index + 1),
            },
        ],
    },
    {
        provider: 'NewsController',
        action: 'update',
        params: [
            'result.id',
            {
                imageUrl: 'mock.banner',
                description: 'mock.paragraph',
                publicationDate: 'mock.today',
                category: client_1.NewsCategory.innewss,
            },
        ],
    },
]).flatMap(x => x);
//# sourceMappingURL=newsToShowOnHome.demo.js.map