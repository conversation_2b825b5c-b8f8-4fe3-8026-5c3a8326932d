"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.newsToSubscriptionAndOrCoinsDemo = void 0;
const client_1 = require("@prisma/client");
exports.newsToSubscriptionAndOrCoinsDemo = [
    {
        provider: 'NewsController',
        action: 'create',
        params: [
            {
                title: 'News that needs coins AND/OR Subscription',
            },
        ],
    },
    {
        provider: 'NewsController',
        action: 'update',
        params: [
            'result.id',
            {
                imageUrl: 'mock.banner',
                description: 'mock.paragraph',
                publicationDate: 'mock.today',
                category: client_1.NewsCategory.innewss,
                payWithCoins: true,
                payWithSubscription: true,
                priceWithCoins: 10,
                priceWithSubscription: 15,
            },
        ],
    },
];
//# sourceMappingURL=newsToSubscriptionAndOrCoins.demo.js.map