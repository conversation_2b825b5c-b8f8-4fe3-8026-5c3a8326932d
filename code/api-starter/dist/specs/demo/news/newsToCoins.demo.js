"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.newsToCoinsDemo = void 0;
const client_1 = require("@prisma/client");
exports.newsToCoinsDemo = [
    {
        provider: 'NewsController',
        action: 'create',
        params: [
            {
                title: 'News that needs coins',
            },
        ],
    },
    {
        provider: 'NewsController',
        action: 'update',
        params: [
            'result.id',
            {
                imageUrl: 'mock.banner',
                description: 'mock.paragraph',
                publicationDate: 'mock.today',
                category: client_1.NewsCategory.innewss,
                payWithCoins: true,
                payWithSubscription: false,
                priceWithCoins: 20,
                priceWithSubscription: 0,
            },
        ],
    },
];
//# sourceMappingURL=newsToCoins.demo.js.map