"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.adminDemoCreateUseCaseExistEmail = exports.adminDemosCreateUseCaseWithPicture = exports.adminDemosCreateUseCaseWithNoPicture = void 0;
exports.adminDemosCreateUseCaseWithNoPicture = [
    {
        provider: 'AdminController',
        action: 'create',
        params: [
            'auth.admin',
            {
                email: 'mock.email',
                name: 'mock.name',
                contents: 'mock.contents',
                role: 'mock.role',
            },
        ],
    },
];
exports.adminDemosCreateUseCaseWithPicture = [
    {
        provider: 'AdminController',
        action: 'create',
        params: [
            'auth.admin',
            {
                email: 'mock.email',
                name: 'mock.name',
                contents: 'mock.contents',
                role: 'mock.role',
                imageUrl: 'mock.avatar',
            },
        ],
    },
];
exports.adminDemoCreateUseCaseExistEmail = [
    {
        provider: 'AdminController',
        action: 'create',
        params: [
            'auth.admin',
            {
                email: 'mock.email',
                name: 'mock.name',
                contents: 'mock.contents',
                role: 'mock.role',
            },
        ],
    },
];
//# sourceMappingURL=admins.demo.js.map