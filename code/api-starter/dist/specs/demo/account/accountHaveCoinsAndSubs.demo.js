"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.coinsAndSubsDemo = exports.accountHaveCoinsAndSubs = void 0;
exports.accountHaveCoinsAndSubs = [
    {
        provider: 'AuthUserController',
        action: 'signUp',
        saveAs: 'accountWithCoinsAndSubs',
        params: [
            {
                email: '<EMAIL>',
                password: 'studiolabs',
                name: 'mock.name',
            },
            'fr',
        ],
    }
];
exports.coinsAndSubsDemo = [
    {
        provider: 'IapController',
        action: 'buyGold',
        params: [
            {
                appType: 'ios',
                purchase: "mock.purchaseCoins"
            },
            'seed.accountWithCoinsAndSubs.result',
        ],
    },
    {
        provider: 'IapController',
        action: 'save',
        params: [
            {
                appType: 'ios',
                purchase: "mock.purchaseSubs"
            },
            'seed.accountWithCoinsAndSubs.result',
        ],
    },
];
//# sourceMappingURL=accountHaveCoinsAndSubs.demo.js.map