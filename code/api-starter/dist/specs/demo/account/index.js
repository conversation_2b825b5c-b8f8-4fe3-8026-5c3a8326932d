"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.coinsAndSubsDemo = exports.accountWithoutCoinsAndSubs = exports.accountHaveSubs = exports.accountHaveCoinsAndSubs = exports.accountHaveCoins = void 0;
var accountHaveCoins_demo_1 = require("./accountHaveCoins.demo");
Object.defineProperty(exports, "accountHaveCoins", { enumerable: true, get: function () { return accountHaveCoins_demo_1.accountHaveCoins; } });
var accountHaveCoinsAndSubs_demo_1 = require("./accountHaveCoinsAndSubs.demo");
Object.defineProperty(exports, "accountHaveCoinsAndSubs", { enumerable: true, get: function () { return accountHaveCoinsAndSubs_demo_1.accountHaveCoinsAndSubs; } });
var accountHaveSubs_demo_1 = require("./accountHaveSubs.demo");
Object.defineProperty(exports, "accountHaveSubs", { enumerable: true, get: function () { return accountHaveSubs_demo_1.accountHaveSubs; } });
var accountWithoutCoinsAndSubs_demo_1 = require("./accountWithoutCoinsAndSubs.demo");
Object.defineProperty(exports, "accountWithoutCoinsAndSubs", { enumerable: true, get: function () { return accountWithoutCoinsAndSubs_demo_1.accountWithoutCoinsAndSubs; } });
var accountHaveCoinsAndSubs_demo_2 = require("./accountHaveCoinsAndSubs.demo");
Object.defineProperty(exports, "coinsAndSubsDemo", { enumerable: true, get: function () { return accountHaveCoinsAndSubs_demo_2.coinsAndSubsDemo; } });
//# sourceMappingURL=index.js.map