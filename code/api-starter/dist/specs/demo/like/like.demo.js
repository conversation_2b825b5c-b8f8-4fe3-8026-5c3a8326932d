"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.likeOnProfileDemo = void 0;
exports.likeOnProfileDemo = Array.from({ length: 25 }, (x, index) => [
    {
        provider: 'LikeController',
        action: 'create',
        params: [
            'seed.userDemo' + index.toString() + '.result',
            {
                objectId: 'seed.test.result.id',
                objectType: 'profile',
                reaction: 1,
            },
        ],
    },
]).flatMap(x => x);
//# sourceMappingURL=like.demo.js.map