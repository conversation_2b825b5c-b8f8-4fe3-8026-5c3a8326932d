"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.userDemos = void 0;
exports.userDemos = Array.from({ length: 50 }, (x, index) => [
    {
        provider: 'AuthUserController',
        saveAs: 'userDemo' + index.toString(),
        action: 'signUp',
        params: [
            {
                email: 'user' + index + '@studiolabs.io',
                password: 'mock.password',
                name: 'mock.name',
            },
            'fr',
        ],
    },
    {
        provider: 'ProfileController',
        action: 'update',
        params: [
            'result',
            {
                imageUrl: 'mock.avatar',
                country: 'mock.country',
                gender: 'mock.gender',
            },
        ],
    },
]).flatMap(x => x);
//# sourceMappingURL=users.demo.js.map