"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.analyticToViewEventDemo = void 0;
const client_1 = require("@prisma/client");
exports.analyticToViewEventDemo = [
    {
        provider: 'AnalyticController',
        action: 'create',
        params: [
            'auth.user',
            {
                name: 'Analytic - view Event',
                objectType: 'news',
                objectId: 'seed.newsToRead.result.id',
                event: client_1.AnalyticEvent.view,
            },
        ],
    },
];
//# sourceMappingURL=analyticToViewEvent.demo.js.map