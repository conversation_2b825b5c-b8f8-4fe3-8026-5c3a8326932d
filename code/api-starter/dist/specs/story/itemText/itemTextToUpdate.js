"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.itemTextToUpdate = void 0;
exports.itemTextToUpdate = {
    filePath: __filename,
    route: '/item/text/:id',
    method: 'PATCH',
    operation: {
        summary: 'As a user, I want to update an item text',
    },
    access: {
        resource: 'itemText',
        action: 'update',
        owner: true,
    },
    codes: {
        '200': {
            response: {
                status: 200,
                description: 'Item Text updated successfully',
            },
            story: {
                auth: 'test',
                path: {
                    id: 'seed.itemToRead.result.texts[0].id',
                },
                body: {
                    text: 'Text updated successfully',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: { text: 'params.text' },
                },
            ],
        },
        '403': {
            response: {
                status: 403,
                description: 'Incorrect credentials',
            },
            story: {
                auth: 'userAlreadyRegistered',
                path: {
                    id: 'seed.itemToRead.result.texts[0].id',
                },
                body: {
                    text: 'mock.description',
                },
            },
        },
        '404': {
            response: {
                status: 404,
                description: "User doesn't exists",
            },
            story: {
                auth: 'user',
                path: {
                    id: 'unknownId',
                },
                body: {
                    text: 'mock.description',
                },
            },
        },
    },
};
//# sourceMappingURL=itemTextToUpdate.js.map