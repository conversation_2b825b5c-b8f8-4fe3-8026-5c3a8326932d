"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.itemTextToRead = void 0;
exports.itemTextToRead = {
    filePath: __filename,
    route: '/item/:id',
    method: 'GET',
    operation: {
        summary: 'I want to read a item',
    },
    access: {
        resource: 'item',
        action: 'read',
    },
    codes: {
        '200': {
            response: {
                status: 200,
                description: 'Data item',
            },
            story: {
                auth: 'user',
                path: {
                    id: 'seed.itemTextToRead.result.id',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: { name: 'seed.itemTextToRead.params[0].name' },
                },
            ],
        },
        '404': {
            response: {
                status: 404,
                description: 'Data does not exists',
            },
        },
    },
};
//# sourceMappingURL=itemTextToRead.js.map