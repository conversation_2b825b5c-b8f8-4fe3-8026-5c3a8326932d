"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.newsToCount = void 0;
exports.newsToCount = {
    filePath: __filename,
    route: '/news/count',
    method: 'POST',
    operation: {
        summary: 'I want to count found by title',
    },
    access: {
        resource: 'news',
        action: 'read',
    },
    codes: {
        '201': {
            response: {
                status: 201,
                description: 'Number of News found',
            },
            story: {
                auth: 'user',
                body: {
                    title: 'News to find',
                },
            },
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
    },
};
//# sourceMappingURL=newsToCount.js.map