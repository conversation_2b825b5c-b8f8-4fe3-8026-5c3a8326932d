"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.newsToReadWithSubscription = void 0;
exports.newsToReadWithSubscription = {
    filePath: __filename,
    route: '/news/:id',
    method: 'GET',
    operation: {
        summary: 'I want to read a news with coins',
    },
    access: {
        resource: 'news',
        action: 'read',
    },
    codes: {
        '200': {
            response: {
                status: 200,
                description: 'news needs coins',
            },
            story: {
                auth: 'user',
                path: {
                    id: 'seed.newsToCoinsDemo.result.id',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: {
                        payWithCoins: 'seed.newsToCoinsDemo.result.payWithCoins',
                        priceWithCoins: 'seed.newsToCoinsDemo.result.priceWithCoins',
                    },
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
        '404': {
            response: {
                status: 404,
                description: "news doesn't exists",
            },
            story: {
                auth: 'user',
                path: {
                    id: 'unknownewsToRead',
                },
            },
        },
    },
};
//# sourceMappingURL=newsToReadWithCoins.js.map