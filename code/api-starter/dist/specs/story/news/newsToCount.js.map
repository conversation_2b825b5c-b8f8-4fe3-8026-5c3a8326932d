{"version": 3, "file": "newsToCount.js", "sourceRoot": "", "sources": ["../../../../specs/story/news/newsToCount.ts"], "names": [], "mappings": ";;;AAEa,QAAA,WAAW,GAAc;IACpC,QAAQ,EAAE,UAAU;IACpB,KAAK,EAAE,aAAa;IACpB,MAAM,EAAE,MAAM;IACd,SAAS,EAAE;QACT,OAAO,EAAE,gCAAgC;KAC1C;IACD,MAAM,EAAE;QACN,QAAQ,EAAE,MAAM;QAChB,MAAM,EAAE,MAAM;KACf;IACD,KAAK,EAAE;QACL,KAAK,EAAE;YACL,QAAQ,EAAE;gBACR,MAAM,EAAE,GAAG;gBACX,WAAW,EAAE,sBAAsB;aACpC;YACD,KAAK,EAAE;gBACL,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE;oBACJ,KAAK,EAAE,cAAc;iBACtB;aACF;SAOF;QACD,KAAK,EAAE;YACL,QAAQ,EAAE;gBACR,MAAM,EAAE,GAAG;gBACX,WAAW,EAAE,uBAAuB;aACrC;SACF;KACF;CACF,CAAC"}