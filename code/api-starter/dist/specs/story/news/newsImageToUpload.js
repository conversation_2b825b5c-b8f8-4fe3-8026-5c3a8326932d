"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.newsImageToUpload = void 0;
exports.newsImageToUpload = {
    filePath: __filename,
    route: '/news/:id/picture',
    method: 'POST',
    operation: {
        summary: 'Update a news image. Only image files are supported (mime type image/*).',
    },
    access: {
        resource: 'news',
        action: 'create',
    },
    codes: {
        '201': {
            response: {
                status: 201,
                description: 'Picture Upload Succeed',
            },
            story: {
                auth: 'admin',
                path: {
                    id: 'seed.newsToRead.result.id',
                },
                body: { fileUrl: 'mock.image' },
            },
            tests: [
                {
                    type: 'contains',
                    data: { id: 'seed.newsToRead.result.id' },
                },
            ],
        },
        '400': {
            response: {
                status: 400,
                description: 'Required picture is empty or the file type is not an image.',
            },
        },
        '403': {
            response: {
                status: 403,
                description: 'Forbidden. You do not have the rights.',
            },
        },
        '404': {
            response: {
                status: 404,
                description: 'News not found.',
            },
        },
    },
};
//# sourceMappingURL=newsImageToUpload.js.map