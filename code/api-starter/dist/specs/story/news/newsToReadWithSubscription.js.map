{"version": 3, "file": "newsToReadWithSubscription.js", "sourceRoot": "", "sources": ["../../../../specs/story/news/newsToReadWithSubscription.ts"], "names": [], "mappings": ";;;AAEa,QAAA,0BAA0B,GAAc;IACnD,QAAQ,EAAE,UAAU;IACpB,KAAK,EAAE,WAAW;IAClB,MAAM,EAAE,KAAK;IACb,SAAS,EAAE;QACT,OAAO,EAAE,yCAAyC;KACnD;IACD,MAAM,EAAE;QACN,QAAQ,EAAE,MAAM;QAChB,MAAM,EAAE,MAAM;KACf;IACD,KAAK,EAAE;QACL,KAAK,EAAE;YACL,QAAQ,EAAE;gBACR,MAAM,EAAE,GAAG;gBACX,WAAW,EAAE,yBAAyB;aACvC;YACD,KAAK,EAAE;gBACL,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE;oBACJ,EAAE,EAAE,uCAAuC;iBAC5C;aACF;YACD,KAAK,EAAE;gBACL;oBACE,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE;wBACJ,mBAAmB,EACjB,wDAAwD;wBAC1D,qBAAqB,EACnB,0DAA0D;qBAC7D;iBACF;aACF;SACF;QACD,KAAK,EAAE;YACL,QAAQ,EAAE;gBACR,MAAM,EAAE,GAAG;gBACX,WAAW,EAAE,uBAAuB;aACrC;SACF;QACD,KAAK,EAAE;YACL,QAAQ,EAAE;gBACR,MAAM,EAAE,GAAG;gBACX,WAAW,EAAE,qBAAqB;aACnC;YACD,KAAK,EAAE;gBACL,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE;oBACJ,EAAE,EAAE,kBAAkB;iBACvB;aACF;SACF;KACF;CACF,CAAC"}