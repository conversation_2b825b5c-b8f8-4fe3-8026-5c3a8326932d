"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.newsToUpdate = void 0;
exports.newsToUpdate = {
    filePath: __filename,
    route: '/news/:id',
    method: 'PATCH',
    operation: {
        summary: 'As a admin, i want to update a news',
    },
    access: {
        resource: 'news',
        action: 'update',
    },
    codes: {
        '200': {
            response: {
                status: 200,
                description: 'News Updated',
            },
            story: {
                auth: 'admin',
                path: {
                    id: 'seed.newsToUpdate.result.id',
                },
                body: {
                    title: 'Updated News',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: { title: 'params.title' },
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
        '404': {
            response: {
                status: 404,
                description: 'News does not exists',
            },
        },
    },
};
//# sourceMappingURL=newsToUpdate.js.map