"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.newsToReadWithSubscriptionAndOrCoins = void 0;
exports.newsToReadWithSubscriptionAndOrCoins = {
    filePath: __filename,
    route: '/news/:id',
    method: 'GET',
    operation: {
        summary: 'I want to read a news with subscription AND/OR coins',
    },
    access: {
        resource: 'news',
        action: 'read',
    },
    codes: {
        '200': {
            response: {
                status: 200,
                description: 'news needs subscription AND/OR coins',
            },
            story: {
                auth: 'user',
                path: {
                    id: 'seed.newsToSubscriptionAndOrCoinsDemo.result.id',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: {
                        payWithSubscription: 'seed.newsToSubscriptionAndOrCoinsDemo.result.payWithSubscription',
                        payWithCoins: 'seed.newsToSubscriptionAndOrCoinsDemo.result.payWithCoins',
                    },
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
        '404': {
            response: {
                status: 404,
                description: "news doesn't exists",
            },
            story: {
                auth: 'user',
                path: {
                    id: 'unknownewsToRead',
                },
            },
        },
    },
};
//# sourceMappingURL=newsToReadWithSubscriptionAndOrCoins.js.map