"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.newsToFind = void 0;
exports.newsToFind = {
    filePath: __filename,
    route: '/news/find',
    method: 'POST',
    operation: {
        summary: 'I want to find news by title',
    },
    access: {
        resource: 'news',
        action: 'read',
    },
    codes: {
        '201': {
            response: {
                status: 201,
                description: 'News found',
            },
            story: {
                auth: 'user',
                body: {
                    title: 'News to find',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: { length: 1 },
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
    },
};
//# sourceMappingURL=newsToFind.js.map