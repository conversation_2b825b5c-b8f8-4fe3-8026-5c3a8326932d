"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.newsToRead = void 0;
exports.newsToRead = {
    filePath: __filename,
    route: '/news/:id',
    method: 'GET',
    operation: {
        summary: 'I want to read an news',
    },
    access: {
        resource: 'news',
        action: 'read',
    },
    codes: {
        '200': {
            response: {
                status: 200,
                description: 'News data',
            },
            story: {
                auth: 'user',
                path: {
                    id: 'seed.newsToRead.result.id',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: { title: 'seed.newsToRead.params[0].title' },
                },
            ],
        },
        '404': {
            response: {
                status: 404,
                description: 'News does not exists',
            },
        },
    },
};
//# sourceMappingURL=newsToRead.js.map