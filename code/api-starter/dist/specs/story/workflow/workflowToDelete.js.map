{"version": 3, "file": "workflowToDelete.js", "sourceRoot": "", "sources": ["../../../../specs/story/workflow/workflowToDelete.ts"], "names": [], "mappings": ";;;AAEa,QAAA,gBAAgB,GAAc;IACzC,QAAQ,EAAE,UAAU;IACpB,KAAK,EAAE,eAAe;IACtB,MAAM,EAAE,QAAQ;IAChB,SAAS,EAAE;QACT,OAAO,EAAE,yCAAyC;KACnD;IACD,MAAM,EAAE;QACN,QAAQ,EAAE,UAAU;QACpB,MAAM,EAAE,QAAQ;QAChB,KAAK,EAAE,IAAI;KACZ;IACD,KAAK,EAAE;QACL,KAAK,EAAE;YACL,QAAQ,EAAE;gBACR,MAAM,EAAE,GAAG;gBACX,WAAW,EAAE,iBAAiB;aAC/B;YACD,KAAK,EAAE;gBACL,IAAI,EAAE,MAAM;gBAEZ,IAAI,EAAE;oBACJ,EAAE,EAAE,iCAAiC;iBACtC;aACF;YACD,KAAK,EAAE;gBACL;oBACE,IAAI,EAAE,OAAO;oBACb,OAAO,EAAE,IAAI;iBACd;aACF;SACF;QACD,KAAK,EAAE;YACL,QAAQ,EAAE;gBACR,MAAM,EAAE,GAAG;gBACX,WAAW,EAAE,uBAAuB;aACrC;YACD,KAAK,EAAE;gBACL,IAAI,EAAE,uBAAuB;gBAC7B,IAAI,EAAE;oBACJ,EAAE,EAAE,gCAAgC;iBACrC;aACF;SACF;QACD,KAAK,EAAE;YACL,QAAQ,EAAE;gBACR,MAAM,EAAE,GAAG;gBACX,WAAW,EAAE,yBAAyB;aACvC;YACD,KAAK,EAAE;gBACL,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE;oBACJ,EAAE,EAAE,iCAAiC;iBACtC;aACF;SACF;KACF;CACF,CAAC"}