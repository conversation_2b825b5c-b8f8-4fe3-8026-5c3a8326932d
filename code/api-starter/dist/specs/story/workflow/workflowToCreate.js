"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.workflowToCreate = void 0;
exports.workflowToCreate = {
    filePath: __filename,
    route: '/workflow',
    method: 'POST',
    operation: {
        summary: 'As a user, i want to create an workflow',
    },
    access: {
        resource: 'workflow',
        action: 'create',
    },
    codes: {
        '201': {
            response: {
                status: 201,
                description: 'Workflow created',
            },
            story: {
                auth: 'user',
                body: {
                    name: 'mock.name',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: { name: 'params.name' },
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
    },
};
//# sourceMappingURL=workflowToCreate.js.map