"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.workflowToCount = void 0;
exports.workflowToCount = {
    filePath: __filename,
    route: '/workflow/count',
    method: 'POST',
    operation: {
        summary: 'As a user, i want to count workflows found by name',
    },
    access: {
        resource: 'workflow',
        action: 'read',
    },
    codes: {
        '201': {
            response: {
                status: 201,
                description: 'Number of Workflows found',
            },
            story: {
                auth: 'user',
                body: {
                    name: 'Workflow to find',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: 2,
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
    },
};
//# sourceMappingURL=workflowToCount.js.map