"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.workflowToUpdate = void 0;
exports.workflowToUpdate = {
    filePath: __filename,
    route: '/workflow/:id',
    method: 'PATCH',
    operation: {
        summary: 'as a user, i want to update an workflow',
    },
    access: {
        resource: 'workflow',
        action: 'update',
        owner: true,
    },
    codes: {
        '200': {
            response: {
                status: 200,
                description: 'Workflow Updated',
            },
            story: {
                auth: 'user',
                path: {
                    id: 'seed.workflowToUpdate.result.id',
                },
                body: {
                    name: 'Updated Workflow',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: { name: 'params.name' },
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
        '404': {
            response: {
                status: 404,
                description: "Workflow doesn't exists",
            },
            story: {
                auth: 'user',
                path: {
                    id: 'unknowWorkflowToRead',
                },
            },
        },
    },
};
//# sourceMappingURL=workflowToUpdate.js.map