{"version": 3, "file": "workflowToUpdate.js", "sourceRoot": "", "sources": ["../../../../specs/story/workflow/workflowToUpdate.ts"], "names": [], "mappings": ";;;AAEa,QAAA,gBAAgB,GAAc;IACzC,QAAQ,EAAE,UAAU;IACpB,KAAK,EAAE,eAAe;IACtB,MAAM,EAAE,OAAO;IACf,SAAS,EAAE;QACT,OAAO,EAAE,yCAAyC;KACnD;IACD,MAAM,EAAE;QACN,QAAQ,EAAE,UAAU;QACpB,MAAM,EAAE,QAAQ;QAChB,KAAK,EAAE,IAAI;KACZ;IACD,KAAK,EAAE;QACL,KAAK,EAAE;YACL,QAAQ,EAAE;gBACR,MAAM,EAAE,GAAG;gBACX,WAAW,EAAE,kBAAkB;aAChC;YACD,KAAK,EAAE;gBACL,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE;oBACJ,EAAE,EAAE,iCAAiC;iBACtC;gBACD,IAAI,EAAE;oBACJ,IAAI,EAAE,kBAAkB;iBACzB;aACF;YACD,KAAK,EAAE;gBACL;oBACE,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,EAAC,IAAI,EAAE,aAAa,EAAC;iBAC5B;aACF;SACF;QACD,KAAK,EAAE;YACL,QAAQ,EAAE;gBACR,MAAM,EAAE,GAAG;gBACX,WAAW,EAAE,uBAAuB;aACrC;SACF;QACD,KAAK,EAAE;YACL,QAAQ,EAAE;gBACR,MAAM,EAAE,GAAG;gBACX,WAAW,EAAE,yBAAyB;aACvC;YACD,KAAK,EAAE;gBACL,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE;oBACJ,EAAE,EAAE,sBAAsB;iBAC3B;aACF;SACF;KACF;CACF,CAAC"}