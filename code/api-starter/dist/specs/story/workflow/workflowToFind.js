"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.workflowToFind = void 0;
exports.workflowToFind = {
    filePath: __filename,
    route: '/workflow/find',
    method: 'POST',
    operation: {
        summary: 'As a user, i want to find by name',
    },
    access: {
        resource: 'workflow',
        action: 'read',
    },
    codes: {
        '201': {
            response: {
                status: 201,
                description: 'Workflows found',
            },
            story: {
                auth: 'user',
                body: {
                    name: 'Workflow to find',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: { length: 2 },
                },
            ],
        },
    },
};
//# sourceMappingURL=workflowToFind.js.map