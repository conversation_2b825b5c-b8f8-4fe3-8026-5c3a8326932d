{"version": 3, "file": "workflowToRead.js", "sourceRoot": "", "sources": ["../../../../specs/story/workflow/workflowToRead.ts"], "names": [], "mappings": ";;;AAEa,QAAA,cAAc,GAAc;IACvC,QAAQ,EAAE,UAAU;IACpB,KAAK,EAAE,eAAe;IACtB,MAAM,EAAE,KAAK;IACb,SAAS,EAAE;QACT,OAAO,EAAE,uCAAuC;KACjD;IACD,MAAM,EAAE;QACN,QAAQ,EAAE,UAAU;QACpB,MAAM,EAAE,MAAM;KACf;IACD,KAAK,EAAE;QACL,KAAK,EAAE;YACL,QAAQ,EAAE;gBACR,MAAM,EAAE,GAAG;gBACX,WAAW,EAAE,iBAAiB;aAC/B;YACD,KAAK,EAAE;gBACL,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE;oBACJ,EAAE,EAAE,+BAA+B;iBACpC;aACF;YACD,KAAK,EAAE;gBACL;oBACE,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,EAAC,IAAI,EAAE,oCAAoC,EAAC;iBACnD;aACF;SACF;QACD,KAAK,EAAE;YACL,QAAQ,EAAE;gBACR,MAAM,EAAE,GAAG;gBACX,WAAW,EAAE,uBAAuB;aACrC;SACF;QACD,KAAK,EAAE;YACL,QAAQ,EAAE;gBACR,MAAM,EAAE,GAAG;gBACX,WAAW,EAAE,yBAAyB;aACvC;SACF;KACF;CACF,CAAC"}