{"version": 3, "file": "workflowToCount.js", "sourceRoot": "", "sources": ["../../../../specs/story/workflow/workflowToCount.ts"], "names": [], "mappings": ";;;AAEa,QAAA,eAAe,GAAc;IACxC,QAAQ,EAAE,UAAU;IACpB,KAAK,EAAE,iBAAiB;IACxB,MAAM,EAAE,MAAM;IACd,SAAS,EAAE;QACT,OAAO,EAAE,oDAAoD;KAC9D;IACD,MAAM,EAAE;QACN,QAAQ,EAAE,UAAU;QACpB,MAAM,EAAE,MAAM;KACf;IACD,KAAK,EAAE;QACL,KAAK,EAAE;YACL,QAAQ,EAAE;gBACR,MAAM,EAAE,GAAG;gBACX,WAAW,EAAE,2BAA2B;aACzC;YACD,KAAK,EAAE;gBACL,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE;oBACJ,IAAI,EAAE,kBAAkB;iBACzB;aACF;YACD,KAAK,EAAE;gBACL;oBACE,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,CAAC;iBACR;aACF;SACF;QACD,KAAK,EAAE;YACL,QAAQ,EAAE;gBACR,MAAM,EAAE,GAAG;gBACX,WAAW,EAAE,uBAAuB;aACrC;SACF;KACF;CACF,CAAC"}