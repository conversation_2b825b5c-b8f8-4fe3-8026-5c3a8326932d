"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.agentToRead = void 0;
exports.agentToRead = {
    filePath: __filename,
    route: '/chat/agent/:id',
    method: 'GET',
    operation: {
        summary: 'Get agent by ID',
    },
    access: {
        resource: 'agent',
        action: 'read',
    },
    codes: {
        '200': {
            response: {
                status: 200,
                description: 'Agent details',
            },
            story: {
                auth: 'admin',
                path: {
                    id: 'seed.agentToRead.result.id',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: { id: 'params.id' },
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Unauthorized',
            },
        },
        '404': {
            response: {
                status: 404,
                description: 'Agent not found',
            },
        },
    },
};
//# sourceMappingURL=agentToRead.js.map