"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.agentToDelete = void 0;
exports.agentToDelete = {
    filePath: __filename,
    route: '/chat/agent/delete:id',
    method: 'DELETE',
    operation: {
        summary: 'Delete an agent',
    },
    access: {
        resource: 'agent',
        action: 'delete',
    },
    codes: {
        '200': {
            response: {
                status: 200,
                description: 'Agent deleted successfully',
            },
            story: {
                auth: 'admin',
                path: {
                    id: 'seed.agentToDelete.result.id',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: { success: true },
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Unauthorized',
            },
        },
        '404': {
            response: {
                status: 404,
                description: 'Agent not found',
            },
        },
    },
};
//# sourceMappingURL=agentToDelete.js.map