"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.agentToFind = void 0;
exports.agentToFind = {
    filePath: __filename,
    route: '/chat/agent/find',
    method: 'POST',
    operation: {
        summary: 'Find agents by criteria',
    },
    access: {
        resource: 'agent',
        action: 'read',
    },
    codes: {
        '200': {
            response: {
                status: 200,
                description: 'List of agents matching criteria',
            },
            story: {
                auth: 'admin',
                body: {
                    name: 'Spanish',
                    take: 10,
                    skip: 0,
                },
            },
            tests: [
                {
                    type: 'exists',
                    data: { length: true },
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Unauthorized',
            },
        },
    },
};
//# sourceMappingURL=agentToFind.js.map