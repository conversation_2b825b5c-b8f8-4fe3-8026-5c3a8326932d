"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.agentToCreate = void 0;
exports.agentToCreate = {
    filePath: __filename,
    route: '/chat/agent/create',
    method: 'POST',
    operation: {
        summary: 'Create a new agent',
    },
    access: {
        resource: 'agent',
        action: 'create',
    },
    codes: {
        '201': {
            response: {
                status: 201,
                description: 'Agent created successfully',
            },
            story: {
                auth: 'admin',
                body: {
                    systemPrompt: 'You are a Spanish language teacher. You help users learn Spanish vocabulary, grammar, and cultural aspects. You can provide translations, explain grammar rules, and engage in Spanish conversation practice.',
                },
            },
        },
        '401': {
            response: {
                status: 401,
                description: 'Unauthorized',
            },
        },
    },
};
//# sourceMappingURL=agentToCreate.js.map