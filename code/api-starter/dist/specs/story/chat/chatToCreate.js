"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.chatToCreate = void 0;
exports.chatToCreate = {
    filePath: __filename,
    route: '/chat/chatConversation/create',
    method: 'POST',
    operation: {
        summary: 'Create a new conversation with system prompt',
    },
    access: {
        resource: 'chatConversation',
        action: 'create',
    },
    codes: {
        '201': {
            response: {
                status: 201,
                description: 'Conversation created successfully',
                schema: {
                    type: 'object',
                    properties: {
                        id: {
                            type: 'string',
                            description: 'Conversation ID',
                        },
                        title: {
                            type: 'string',
                            description: 'Conversation title',
                        },
                        systemPrompt: {
                            type: 'string',
                            description: 'System prompt used to initialize the conversation',
                        },
                    },
                },
            },
            story: {
                auth: 'user',
                body: {
                    systemPrompt: 'You are a helpful assistant that specializes in JavaScript.',
                    title: 'JavaScript Help',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: { systemPrompt: 'params.systemPrompt' },
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Unauthorized',
            },
        },
    },
};
//# sourceMappingURL=chatToCreate.js.map