"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.chatToRead = void 0;
exports.chatToRead = {
    filePath: __filename,
    route: '/chat/chatConversation/:id',
    method: 'GET',
    operation: {
        summary: 'Get chat conversation',
    },
    access: {
        resource: 'chatConversation',
        action: 'read',
    },
    codes: {
        '200': {
            response: {
                status: 200,
                description: 'Conversation retrieved successfully',
            },
            story: {
                auth: 'user',
                path: {
                    id: 'seed.conversation.result.id',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: { id: 'params.id' },
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
        '404': {
            response: {
                status: 404,
                description: 'Conversation not found',
            },
        },
    },
};
//# sourceMappingURL=chatToRead.js.map