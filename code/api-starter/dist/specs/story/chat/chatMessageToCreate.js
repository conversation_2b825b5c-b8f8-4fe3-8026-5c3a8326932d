"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.chatMessageToCreate = void 0;
exports.chatMessageToCreate = {
    filePath: __filename,
    route: '/chat/chatMessage/create',
    method: 'POST',
    operation: {
        summary: 'Create a new conversation with initial message',
    },
    access: {
        resource: 'chatMessage',
        action: 'create',
    },
    codes: {
        '201': {
            response: {
                status: 201,
                description: 'Conversation created successfully with initial messages',
                schema: {
                    type: 'object',
                    properties: {
                        id: {
                            type: 'string',
                            description: 'Conversation ID',
                        },
                        title: {
                            type: 'string',
                            description: 'Conversation title',
                        },
                        prompt: {
                            type: 'string',
                            description: 'System prompt used to initialize the conversation',
                        },
                        messages: {
                            type: 'array',
                            items: {
                                type: 'object',
                                properties: {
                                    id: {
                                        type: 'string',
                                        description: 'Message ID',
                                    },
                                    role: {
                                        type: 'string',
                                        description: 'Message role (user, assistant, or system)',
                                    },
                                    content: {
                                        type: 'string',
                                        description: 'Message content',
                                    },
                                    messageType: {
                                        type: 'string',
                                        description: 'Type of message (text, audio, etc.)',
                                    },
                                },
                            },
                            description: 'Messages in the conversation',
                        },
                    },
                },
            },
            story: {
                auth: 'user',
                body: {
                    prompt: 'How do I learn JavaScript?',
                    title: 'JavaScript Help',
                    agentId: 'seed.agent.result.id',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: { prompt: 'params.prompt' },
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Unauthorized',
            },
        },
    },
};
//# sourceMappingURL=chatMessageToCreate.js.map