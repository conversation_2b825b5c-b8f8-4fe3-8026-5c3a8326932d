"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.chatToDelete = void 0;
exports.chatToDelete = {
    filePath: __filename,
    route: '/chat/chatConversation/delete/:id',
    method: 'DELETE',
    operation: {
        summary: 'Delete chat conversation',
    },
    access: {
        resource: 'chatConversation',
        action: 'delete',
    },
    codes: {
        '200': {
            response: {
                status: 200,
                description: 'Chat deleted successfully',
            },
            story: {
                auth: 'user',
                path: {
                    id: 'seed.chat.result.id',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: { deleted: true },
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Unauthorized access',
            },
        },
        '404': {
            response: {
                status: 404,
                description: 'Chat not found',
            },
        },
    },
};
//# sourceMappingURL=chatToDelete.js.map