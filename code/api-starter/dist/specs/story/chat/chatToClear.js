"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.chatToClear = void 0;
exports.chatToClear = {
    filePath: __filename,
    route: '/chat/chatConversation/clear/:id',
    method: 'DELETE',
    operation: {
        summary: 'Clear all messages in a chat conversation',
    },
    access: {
        resource: 'chatConversation',
        action: 'update',
    },
    codes: {
        '200': {
            response: {
                status: 200,
                description: 'Chat messages cleared successfully',
            },
            story: {
                auth: 'user',
                path: {
                    id: 'seed.chat.result.id',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: { cleared: true },
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Unauthorized access',
            },
        },
        '404': {
            response: {
                status: 404,
                description: 'Chat not found',
            },
        },
    },
};
//# sourceMappingURL=chatToClear.js.map