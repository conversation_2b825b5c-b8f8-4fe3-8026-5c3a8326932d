"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.chatToUpdate = void 0;
exports.chatToUpdate = {
    filePath: __filename,
    route: '/chat/chatConversation/update/:id',
    method: 'PATCH',
    operation: {
        summary: 'Update chat conversation status',
    },
    access: {
        resource: 'chatConversation',
        action: 'update',
    },
    codes: {
        '200': {
            response: {
                status: 200,
                description: 'Conversation status updated successfully',
            },
            story: {
                auth: 'user',
                path: {
                    id: 'seed.conversation.result.id',
                },
                body: {
                    status: 'archived',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: { status: 'params.status' },
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
        '404': {
            response: {
                status: 404,
                description: 'Conversation not found',
            },
        },
    },
};
//# sourceMappingURL=chatToUpdate.js.map