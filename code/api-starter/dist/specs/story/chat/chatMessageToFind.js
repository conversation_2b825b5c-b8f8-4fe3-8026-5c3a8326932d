"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.chatMessageToFind = void 0;
exports.chatMessageToFind = {
    filePath: __filename,
    route: '/chat/chatMessage/find',
    method: 'POST',
    operation: {
        summary: 'Find chat messages',
    },
    access: {
        resource: 'chatMessage',
        action: 'read',
    },
    codes: {
        '200': {
            response: {
                status: 200,
                description: 'List of messages retrieved successfully',
            },
            story: {
                auth: 'user',
            },
            tests: [
                {
                    type: 'equal',
                    data: { length: 1 },
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
    },
};
//# sourceMappingURL=chatMessageToFind.js.map