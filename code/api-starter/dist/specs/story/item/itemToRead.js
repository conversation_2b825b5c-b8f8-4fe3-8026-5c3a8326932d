"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.itemToRead = void 0;
exports.itemToRead = {
    filePath: __filename,
    route: '/item/:id',
    method: 'GET',
    operation: {
        summary: 'I want to read a item',
    },
    access: {
        resource: 'item',
        action: 'read',
    },
    codes: {
        '200': {
            response: {
                status: 200,
                description: 'Data item',
            },
            story: {
                auth: 'user',
                path: {
                    id: 'seed.itemToRead.result.id',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: { name: 'seed.itemToRead.params[0].name' },
                },
            ],
        },
        '404': {
            response: {
                status: 404,
                description: 'Data does not exists',
            },
        },
    },
};
//# sourceMappingURL=itemToRead.js.map