"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.itemToGenerate = void 0;
exports.itemToGenerate = {
    filePath: __filename,
    route: '/item/:projectId/:itemId',
    method: 'POST',
    operation: {
        summary: 'As a user, i want to generate a item',
    },
    access: {
        resource: 'item',
        action: 'update',
    },
    codes: {
        '200': {
            response: {
                status: 200,
                description: 'Data Updated',
            },
            story: {
                auth: 'admin',
                path: {
                    id: 'seed.itemToUpdate.result.id',
                },
                body: {
                    title: 'Updated Data',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: { title: 'params.title' },
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
        '404': {
            response: {
                status: 404,
                description: 'Data does not exists',
            },
        },
    },
};
//# sourceMappingURL=itemToGenerate.js.map