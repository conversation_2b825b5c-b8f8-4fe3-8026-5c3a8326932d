"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.itemToDelete = void 0;
exports.itemToDelete = {
    filePath: __filename,
    route: '/item/:id',
    method: 'DELETE',
    operation: {
        summary: 'As a admin, i want to delete a item for a market',
    },
    access: {
        resource: 'item',
        action: 'delete',
    },
    codes: {
        '200': {
            response: {
                status: 200,
                description: 'item delete',
            },
            story: {
                auth: 'admin',
                path: {
                    id: 'seed.itemToDelete.result.id',
                },
            },
            tests: [
                {
                    type: 'equal',
                    success: true,
                },
            ],
        },
        '403': {
            response: {
                status: 403,
                description: 'Incorrect credentials',
            },
            story: {
                auth: 'userAlreadyRegistered',
                path: {
                    id: 'seed.itemToFind.result.id',
                },
            },
        },
        '404': {
            response: {
                status: 404,
                description: 'Data does not exists',
            },
            story: {
                auth: 'admin',
                path: {
                    id: 'seed.itemToDelete.result.id',
                },
            },
        },
    },
};
//# sourceMappingURL=itemToDelete.js.map