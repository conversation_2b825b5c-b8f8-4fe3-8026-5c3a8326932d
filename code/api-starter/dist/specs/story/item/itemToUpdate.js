"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.itemToUpdate = void 0;
exports.itemToUpdate = {
    filePath: __filename,
    route: '/item/:id',
    method: 'PATCH',
    operation: {
        summary: 'As a admin, i want to update a item',
    },
    access: {
        resource: 'item',
        action: 'update',
    },
    codes: {
        '200': {
            response: {
                status: 200,
                description: 'Data Updated',
            },
            story: {
                auth: 'admin',
                path: {
                    id: 'seed.itemToUpdate.result.id',
                },
                body: {
                    title: 'Updated Data',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: { title: 'params.title' },
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
        '404': {
            response: {
                status: 404,
                description: 'Data does not exists',
            },
        },
    },
};
//# sourceMappingURL=itemToUpdate.js.map