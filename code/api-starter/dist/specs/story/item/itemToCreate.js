"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.itemToCreate = void 0;
exports.itemToCreate = {
    filePath: __filename,
    route: '/item/:ownerId/:ownerType',
    method: 'POST',
    operation: {
        summary: 'As a admin, i want to create a Item for a market',
    },
    access: {
        resource: 'item',
        action: 'create',
    },
    codes: {
        '201': {
            response: {
                status: 201,
                description: 'Data created',
            },
            story: {
                auth: 'admin',
                body: {
                    name: 'Blog',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: {
                        name: 'Blog',
                    },
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
    },
};
//# sourceMappingURL=itemToCreate.js.map