"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.idToRead = void 0;
exports.idToRead = {
    filePath: __filename,
    route: '/posts/:id',
    method: 'GET',
    operation: {
        summary: 'As a user, I want to read a :id',
    },
    access: {
        resource: ':id',
        action: 'read',
        owner: false
    },
    codes: {
        '200': {
            response: {
                status: 200,
                description: ":id's data",
            },
            story: {
                auth: "user",
                path: { "id": "seed.:idToRead.result.id" }
            },
            tests: [],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
        '404': {
            response: {
                status: 404,
                description: ":id doesn't exist",
            },
        },
    },
};
//# sourceMappingURL=:idToRead.js.map