"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.paymentToDelete = void 0;
exports.paymentToDelete = {
    filePath: __filename,
    route: '/payment/:id',
    method: 'DELETE',
    operation: {
        summary: 'As a admin, i want to delete a payment',
    },
    access: {
        resource: 'payment',
        action: 'delete',
    },
    codes: {
        '200': {
            response: {
                status: 200,
                description: 'payment delete',
            },
            story: {
                auth: 'admin',
                path: {
                    id: 'seed.paymentToDelete.result.id',
                },
            },
            tests: [
                {
                    type: 'equal',
                    success: true,
                },
            ],
        },
        '403': {
            response: {
                status: 403,
                description: 'Incorrect credentials',
            },
            story: {
                auth: 'userAlreadyRegistered',
                path: {
                    id: 'seed.paymentToFind.result.id',
                },
            },
        },
        '404': {
            response: {
                status: 404,
                description: 'Payment does not exists',
            },
            story: {
                auth: 'admin',
                path: {
                    id: 'seed.paymentToDelete.result.id',
                },
            },
        },
    },
};
//# sourceMappingURL=paymentToDelete.js.map