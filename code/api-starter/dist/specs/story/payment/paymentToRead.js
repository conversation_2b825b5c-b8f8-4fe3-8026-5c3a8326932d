"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.paymentToRead = void 0;
exports.paymentToRead = {
    filePath: __filename,
    route: '/payment/:id',
    method: 'GET',
    operation: {
        summary: 'I want to read an payment',
    },
    access: {
        resource: 'payment',
        action: 'read',
    },
    codes: {
        '200': {
            response: {
                status: 200,
                description: 'Payment data',
            },
            story: {
                auth: 'user',
                path: {
                    id: 'seed.paymentToRead.result.id',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: { name: 'seed.paymentToRead.params[0].name' },
                },
            ],
        },
        '404': {
            response: {
                status: 404,
                description: 'Payment does not exists',
            },
        },
    },
};
//# sourceMappingURL=paymentToRead.js.map