"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.paymentToUpdate = void 0;
exports.paymentToUpdate = {
    filePath: __filename,
    route: '/payment/:id',
    method: 'PATCH',
    operation: {
        summary: 'As a admin, i want to update a payment',
    },
    access: {
        resource: 'payment',
        action: 'update',
    },
    codes: {
        '200': {
            response: {
                status: 200,
                description: 'Payment Updated',
            },
            story: {
                auth: 'admin',
                path: {
                    id: 'seed.paymentToUpdate.result.id',
                },
                body: {
                    title: 'Updated Payment',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: { title: 'params.title' },
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
        '404': {
            response: {
                status: 404,
                description: 'Payment does not exists',
            },
        },
    },
};
//# sourceMappingURL=paymentToUpdate.js.map