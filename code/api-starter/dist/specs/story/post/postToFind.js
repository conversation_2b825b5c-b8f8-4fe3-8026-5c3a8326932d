"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.postToFind = void 0;
exports.postToFind = {
    filePath: __filename,
    route: '/workflow/post/find',
    method: 'POST',
    operation: {
        summary: 'As a user, I want to find a post',
    },
    access: {
        resource: 'post',
        action: 'read',
        owner: false
    },
    codes: {
        '201': {
            response: {
                status: 201,
                description: 'post found',
            },
            story: {
                auth: "user"
            },
            tests: [{ "type": "equal", "data": { "length": 0 } }],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
        '404': {
            response: {
                status: 404,
                description: "post doesn't exist",
            },
        },
    },
};
//# sourceMappingURL=postToFind.js.map