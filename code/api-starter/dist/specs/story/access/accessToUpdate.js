"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.accessToUpdate = void 0;
exports.accessToUpdate = {
    filePath: __filename,
    route: '/access/:id',
    method: 'PATCH',
    operation: {
        summary: 'As an admin, I want to update a access',
    },
    access: {
        resource: 'access',
        action: 'update',
    },
    codes: {
        200: {
            response: {
                status: 200,
                description: 'access updated',
            },
        },
        401: {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
        404: {
            response: {
                status: 404,
                description: 'access not found',
            },
        },
    },
};
//# sourceMappingURL=accessToUpdate.js.map