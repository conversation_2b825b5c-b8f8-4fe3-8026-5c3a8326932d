{"version": 3, "file": "accessToRead.js", "sourceRoot": "", "sources": ["../../../../specs/story/access/accessToRead.ts"], "names": [], "mappings": ";;;AAEa,QAAA,YAAY,GAAc;IACrC,QAAQ,EAAE,UAAU;IACpB,KAAK,EAAE,aAAa;IACpB,MAAM,EAAE,KAAK;IACb,SAAS,EAAE;QACT,OAAO,EAAE,6CAA6C;KACvD;IACD,MAAM,EAAE;QACN,QAAQ,EAAE,QAAQ;QAClB,MAAM,EAAE,MAAM;KACf;IACD,KAAK,EAAE;QACL,GAAG,EAAE;YACH,QAAQ,EAAE;gBACR,MAAM,EAAE,GAAG;gBACX,WAAW,EAAE,wBAAwB;aACtC;SACF;QACD,GAAG,EAAE;YACH,QAAQ,EAAE;gBACR,MAAM,EAAE,GAAG;gBACX,WAAW,EAAE,kBAAkB;aAChC;SACF;KACF;CACF,CAAC"}