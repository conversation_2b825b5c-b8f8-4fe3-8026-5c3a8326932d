"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.accessToDeleteAll = void 0;
exports.accessToDeleteAll = {
    filePath: __filename,
    route: '/access/delete',
    method: 'DELETE',
    operation: {
        summary: 'As an admin, I want to delete all access records',
    },
    access: {
        resource: 'access',
        action: 'delete',
    },
    codes: {
        200: {
            response: {
                status: 200,
                description: 'All access records deleted',
            },
        },
        403: {
            response: {
                status: 403,
                description: 'Incorrect credentials',
            },
        },
    },
};
//# sourceMappingURL=accessToDeleteAll.js.map