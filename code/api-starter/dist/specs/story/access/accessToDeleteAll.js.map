{"version": 3, "file": "accessToDeleteAll.js", "sourceRoot": "", "sources": ["../../../../specs/story/access/accessToDeleteAll.ts"], "names": [], "mappings": ";;;AAEa,QAAA,iBAAiB,GAAc;IAC1C,QAAQ,EAAE,UAAU;IACpB,KAAK,EAAE,gBAAgB;IACvB,MAAM,EAAE,QAAQ;IAChB,SAAS,EAAE;QACT,OAAO,EAAE,kDAAkD;KAC5D;IACD,MAAM,EAAE;QACN,QAAQ,EAAE,QAAQ;QAClB,MAAM,EAAE,QAAQ;KACjB;IACD,KAAK,EAAE;QACL,GAAG,EAAE;YACH,QAAQ,EAAE;gBACR,MAAM,EAAE,GAAG;gBACX,WAAW,EAAE,4BAA4B;aAC1C;SACF;QACD,GAAG,EAAE;YACH,QAAQ,EAAE;gBACR,MAAM,EAAE,GAAG;gBACX,WAAW,EAAE,uBAAuB;aACrC;SACF;KACF;CACF,CAAC"}