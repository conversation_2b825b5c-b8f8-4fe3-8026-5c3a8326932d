{"version": 3, "file": "accessToDelete.js", "sourceRoot": "", "sources": ["../../../../specs/story/access/accessToDelete.ts"], "names": [], "mappings": ";;;AAEa,QAAA,cAAc,GAAc;IACvC,QAAQ,EAAE,UAAU;IACpB,KAAK,EAAE,aAAa;IACpB,MAAM,EAAE,QAAQ;IAChB,SAAS,EAAE;QACT,OAAO,EAAE,wCAAwC;KAClD;IACD,MAAM,EAAE;QACN,QAAQ,EAAE,QAAQ;QAClB,MAAM,EAAE,QAAQ;KACjB;IACD,KAAK,EAAE;QACL,GAAG,EAAE;YACH,QAAQ,EAAE;gBACR,MAAM,EAAE,GAAG;gBACX,WAAW,EAAE,gBAAgB;aAC9B;SACF;QACD,GAAG,EAAE;YACH,QAAQ,EAAE;gBACR,MAAM,EAAE,GAAG;gBACX,WAAW,EAAE,uBAAuB;aACrC;SACF;QACD,GAAG,EAAE;YACH,QAAQ,EAAE;gBACR,MAAM,EAAE,GAAG;gBACX,WAAW,EAAE,kBAAkB;aAChC;SACF;KACF;CACF,CAAC"}