"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.imageToRead = void 0;
exports.imageToRead = {
    filePath: __filename,
    route: '/workflow/image/read',
    method: 'POST',
    operation: {
        summary: 'As a user, I want to read a image',
    },
    access: {
        resource: 'image',
        action: 'read',
        owner: true,
    },
    codes: {
        '200': {
            response: {
                status: 201,
                description: 'image read',
            },
            story: {
                auth: 'user',
            },
            tests: [{ type: 'equal', data: { length: 0 } }],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
        '403': {
            response: {
                status: 403,
                description: 'Forbidden',
            },
        },
        '404': {
            response: {
                status: 404,
                description: "image doesn't exist",
            },
        },
    },
};
//# sourceMappingURL=imageToRead.js.map