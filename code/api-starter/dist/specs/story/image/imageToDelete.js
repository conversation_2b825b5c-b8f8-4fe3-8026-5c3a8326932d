"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.imageToDelete = void 0;
exports.imageToDelete = {
    filePath: __filename,
    route: '/workflow/image/delete',
    method: 'POST',
    operation: {
        summary: 'As a user, I want to delete a image',
    },
    access: {
        resource: 'image',
        action: 'delete',
        owner: true,
    },
    codes: {
        '201': {
            response: {
                status: 201,
                description: 'image deleted',
            },
            story: {
                auth: 'user',
            },
            tests: [{ type: 'equal', data: { length: 0 } }],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
        '403': {
            response: {
                status: 403,
                description: 'Forbidden',
            },
        },
        '404': {
            response: {
                status: 404,
                description: "image doesn't exist",
            },
        },
    },
};
//# sourceMappingURL=imageToDelete.js.map