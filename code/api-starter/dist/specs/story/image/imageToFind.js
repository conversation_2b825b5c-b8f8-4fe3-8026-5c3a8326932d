"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.imageToFind = void 0;
exports.imageToFind = {
    filePath: __filename,
    route: '/workflow/image/find',
    method: 'POST',
    operation: {
        summary: 'As a user, I want to find a image',
    },
    access: {
        resource: 'image',
        action: 'read',
        owner: true,
    },
    codes: {
        '201': {
            response: {
                status: 201,
                description: 'image found',
            },
            story: {
                auth: 'user',
            },
            tests: [{ type: 'equal', data: { length: 0 } }],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
        '404': {
            response: {
                status: 404,
                description: "image doesn't exist",
            },
        },
    },
};
//# sourceMappingURL=imageToFind.js.map