"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.videoToCreate = void 0;
exports.videoToCreate = {
    filePath: __filename,
    route: '/workflow/video/create',
    method: 'POST',
    operation: {
        summary: 'As a user, I want to create a video',
    },
    access: {
        resource: 'video',
        action: 'create',
        owner: true,
    },
    codes: {
        '201': {
            response: {
                status: 201,
                description: 'video created',
            },
            story: {
                auth: 'user',
            },
            tests: [{ type: 'equal', data: { length: 0 } }],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
        '403': {
            response: {
                status: 403,
                description: 'Forbidden',
            },
        },
        '404': {
            response: {
                status: 404,
                description: "video doesn't exist",
            },
        },
    },
};
//# sourceMappingURL=videoToCreate.js.map