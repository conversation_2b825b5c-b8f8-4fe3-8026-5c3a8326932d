"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.videoToUpscale = void 0;
exports.videoToUpscale = {
    filePath: __filename,
    route: '/workflow/video/upscale',
    method: 'POST',
    operation: {
        summary: 'As a user, I want to upscale a video',
    },
    access: {
        resource: 'video',
        action: 'create',
        owner: false,
    },
    codes: {
        '201': {
            response: {
                status: 201,
                description: 'video upscaled',
            },
            story: {
                auth: 'user',
            },
            tests: [{ type: 'equal', data: { length: 0 } }],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
        '403': {
            response: {
                status: 403,
                description: 'Forbidden',
            },
        },
        '404': {
            response: {
                status: 404,
                description: "video doesn't exist",
            },
        },
    },
};
//# sourceMappingURL=videoToUpscale.js.map