"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.highlightToUpdate = void 0;
exports.highlightToUpdate = {
    filePath: __filename,
    route: '/highlight/:id',
    method: 'PATCH',
    operation: {
        summary: 'as a user, i want to update an highlight',
    },
    access: {
        resource: 'highlight',
        action: 'update',
    },
    codes: {
        '200': {
            response: {
                status: 200,
                description: 'Highlight section Updated',
            },
            story: {
                auth: 'admin',
                path: {
                    id: 'seed.highlightToUpdate.result.id',
                },
                body: {
                    value: { updated: true },
                    location: 'mock.name',
                },
            },
            tests: [
                {
                    type: 'contains',
                    data: { value: { updated: true } },
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
    },
};
//# sourceMappingURL=highlightToUpdate.js.map