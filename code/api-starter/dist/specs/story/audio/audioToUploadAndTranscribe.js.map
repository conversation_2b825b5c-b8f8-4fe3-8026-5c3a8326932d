{"version": 3, "file": "audioToUploadAndTranscribe.js", "sourceRoot": "", "sources": ["../../../../specs/story/audio/audioToUploadAndTranscribe.ts"], "names": [], "mappings": ";;;AAEa,QAAA,0BAA0B,GAAc;IACnD,QAAQ,EAAE,UAAU;IACpB,KAAK,EAAE,4BAA4B;IACnC,MAAM,EAAE,MAAM;IACd,SAAS,EAAE;QACT,OAAO,EAAE,qCAAqC;KAC/C;IACD,MAAM,EAAE;QACN,QAAQ,EAAE,OAAO;QACjB,MAAM,EAAE,QAAQ;KACjB;IACD,KAAK,EAAE;QACL,KAAK,EAAE;YACL,QAAQ,EAAE;gBACR,MAAM,EAAE,GAAG;gBACX,WAAW,EAAE,uDAAuD;aACrE;YACD,KAAK,EAAE;gBACL,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE;oBACJ,IAAI,EAAE,YAAY;iBACnB;aACF;SACF;QACD,KAAK,EAAE;YACL,QAAQ,EAAE;gBA<PERSON>,MAAM,EAAE,GAAG;gBACX,WAAW,EAAE,cAAc;aAC5B;SACF;KACF;CACF,CAAC"}