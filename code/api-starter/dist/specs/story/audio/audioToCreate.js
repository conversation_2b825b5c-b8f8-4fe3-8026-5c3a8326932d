"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.audioToCreate = void 0;
exports.audioToCreate = {
    filePath: __filename,
    route: '/workflow/audio/create',
    method: 'POST',
    operation: {
        summary: 'Create an audio file with transcription',
    },
    access: {
        resource: 'audio',
        action: 'create',
    },
    codes: {
        '201': {
            response: {
                status: 201,
                description: 'The workflow ID for the audio creation task',
            },
            story: {
                auth: 'user',
                body: {
                    transcription: 'mock.text',
                },
            },
        },
        '401': {
            response: {
                status: 401,
                description: 'Unauthorized',
            },
        },
    },
};
//# sourceMappingURL=audioToCreate.js.map