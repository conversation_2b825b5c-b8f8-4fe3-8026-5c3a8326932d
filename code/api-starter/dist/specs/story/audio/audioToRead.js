"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.audioToRead = void 0;
exports.audioToRead = {
    filePath: __filename,
    route: '/workflow/audio/:id',
    method: 'GET',
    operation: {
        summary: 'Get an audio file',
    },
    access: {
        resource: 'audio',
        action: 'read',
    },
    codes: {
        '200': {
            response: {
                status: 200,
                description: 'The audio file',
            },
            story: {
                auth: 'user',
                path: {
                    id: 'seed.audioToRead.result.id',
                },
            },
        },
        '401': {
            response: {
                status: 401,
                description: 'Unauthorized',
            },
        },
        '404': {
            response: {
                status: 404,
                description: 'Audio file not found',
            },
        },
    },
};
//# sourceMappingURL=audioToRead.js.map