"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.actionToReadAll = void 0;
exports.actionToReadAll = {
    filePath: __filename,
    route: '/action/all',
    method: 'GET',
    operation: {
        summary: 'As an admin, I want to get all action',
    },
    access: {
        resource: 'action',
        action: 'read',
    },
    codes: {
        200: {
            response: {
                status: 200,
                description: 'All action fetched',
            },
        },
        404: {
            response: {
                status: 404,
                description: 'actions does not exists',
            },
        },
    },
};
//# sourceMappingURL=actionToReadAll.js.map