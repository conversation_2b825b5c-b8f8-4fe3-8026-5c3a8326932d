"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.actionToFind = void 0;
exports.actionToFind = {
    filePath: __filename,
    route: '/action/find',
    method: 'POST',
    operation: {
        summary: 'As a user, I want to find a specific action by criteria',
    },
    access: {
        resource: 'action',
        action: 'read',
    },
    codes: {
        '201': {
            response: {
                status: 201,
                description: 'Action found',
            },
            story: {
                auth: 'user',
                body: {
                    name: 'Action to find',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: { length: 2 },
                },
            ],
        },
        404: {
            response: {
                status: 404,
                description: 'action not found based on criteria',
            },
        },
    },
};
//# sourceMappingURL=actionToFind.js.map