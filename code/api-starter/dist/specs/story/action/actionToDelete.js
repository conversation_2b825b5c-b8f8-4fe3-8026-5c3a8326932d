"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.actionToDelete = void 0;
exports.actionToDelete = {
    filePath: __filename,
    route: '/action',
    method: 'DELETE',
    operation: {
        summary: 'As an admin, I want to delete a action',
    },
    access: {
        resource: 'action',
        action: 'delete',
    },
    codes: {
        200: {
            response: {
                status: 200,
                description: 'action deleted',
            },
        },
        403: {
            response: {
                status: 403,
                description: 'Incorrect credentials',
            },
        },
        404: {
            response: {
                status: 404,
                description: 'action not found',
            },
        },
    },
};
//# sourceMappingURL=actionToDelete.js.map