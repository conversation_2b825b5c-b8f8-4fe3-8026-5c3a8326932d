"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createToCreate = void 0;
exports.createToCreate = {
    filePath: __filename,
    route: '/comment',
    method: 'POST',
    operation: {
        summary: 'As a user, I want to create a create',
    },
    access: {
        resource: 'create',
        action: 'create',
        owner: false
    },
    codes: {
        '201': {
            response: {
                status: 201,
                description: 'create created',
            },
            story: {
                auth: "user",
                body: { "content": "User's new comment", "objectId": ":id", "objectType": "post" }
            },
            tests: [{ "type": "equal", "data": { "content": "params.content" } }],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
    },
};
//# sourceMappingURL=createToCreate.js.map