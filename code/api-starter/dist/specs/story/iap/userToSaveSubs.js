"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.userToSaveSubs = void 0;
exports.userToSaveSubs = {
    filePath: __filename,
    route: '/iap/saveSubs',
    method: 'POST',
    operation: {
        summary: 'As an user, i want to save subs',
    },
    access: {
        resource: 'subscription',
        action: 'create',
    },
    codes: {
        '201': {
            response: {
                status: 201,
                description: 'user save subscription',
            },
            story: {
                auth: 'accountHaveSubs',
                body: {
                    appType: 'ios',
                    purchase: 'mock.purchaseSubs',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: {
                        productId: 'seed.accountHaveCoins.param[1].productId',
                    },
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
    },
};
//# sourceMappingURL=userToSaveSubs.js.map