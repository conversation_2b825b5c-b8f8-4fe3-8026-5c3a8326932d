"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.userToSaveCoins = void 0;
exports.userToSaveCoins = {
    filePath: __filename,
    route: '/iap/saveCoins',
    method: 'POST',
    operation: {
        summary: 'As an user, i want to save subs',
    },
    access: {
        resource: 'subscription',
        action: 'create',
    },
    codes: {
        '201': {
            response: {
                status: 201,
                description: 'user save subscription',
            },
            story: {
                auth: 'accountHaveSubs',
                body: {
                    appType: 'ios',
                    purchase: 'mock.purchaseCoins',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: {
                        productId: 'seed.accountHaveCoins.param[1].productId',
                    },
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
    },
};
//# sourceMappingURL=userToBuyCoins.js.map