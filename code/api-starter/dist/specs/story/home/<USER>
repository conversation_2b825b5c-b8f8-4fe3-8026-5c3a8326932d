"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.homeToUpdate = void 0;
exports.homeToUpdate = {
    filePath: __filename,
    route: '/home/<USER>',
    method: 'PATCH',
    operation: {
        summary: 'as a user, i want to update an home',
    },
    access: {
        resource: 'home',
        action: 'update',
    },
    codes: {
        '200': {
            response: {
                status: 200,
                description: 'Home section Updated',
            },
            story: {
                auth: 'admin',
                path: {
                    id: 'seed.homeToUpdate.result.id',
                },
                body: {
                    value: { updated: true },
                },
            },
            tests: [
                {
                    type: 'contains',
                    data: { value: { updated: true } },
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
    },
};
//# sourceMappingURL=homeToUpdate.js.map