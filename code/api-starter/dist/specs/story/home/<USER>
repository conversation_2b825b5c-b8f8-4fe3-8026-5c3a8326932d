"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.homeToRead = void 0;
exports.homeToRead = {
    filePath: __filename,
    route: '/home/<USER>',
    method: 'GET',
    operation: {
        summary: 'As a user, i want to read an home',
    },
    access: {
        resource: 'home',
        action: 'read',
    },
    codes: {
        '200': {
            response: {
                status: 200,
                description: "Home's data",
            },
            story: {
                path: {
                    id: 'seed.homeToRead.result.id',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: { id: 'seed.homeToRead.params[0].id' },
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
        '404': {
            response: {
                status: 404,
                description: "Home section doesn't exists",
            },
        },
    },
};
//# sourceMappingURL=homeToRead.js.map