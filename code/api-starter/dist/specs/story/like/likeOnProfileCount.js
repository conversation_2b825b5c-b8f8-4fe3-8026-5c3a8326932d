"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.likeOnProfileCount = void 0;
exports.likeOnProfileCount = {
    filePath: __filename,
    route: '/like/profile/count',
    method: 'POST',
    operation: {
        summary: 'As a User I want to display a number of like/unlike for an ownerId for a dancer profile',
    },
    access: {
        resource: 'like',
        action: 'read',
    },
    codes: {
        '201': {
            response: {
                status: 201,
                description: 'likes count',
            },
            story: {
                auth: 'user',
                body: {
                    ownerId: 'seed.likeOnProfile.params[1].ownerId',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: 1,
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
    },
};
//# sourceMappingURL=likeOnProfileCount.js.map