"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.likeToUpdate = void 0;
exports.likeToUpdate = {
    filePath: __filename,
    route: '/like/:id',
    method: 'PATCH',
    operation: {
        summary: 'As a admin, i want to update a like',
    },
    access: {
        resource: 'like',
        action: 'update',
    },
    codes: {
        '200': {
            response: {
                status: 200,
                description: 'Like Updated',
            },
            story: {
                auth: 'admin',
                path: {
                    id: 'seed.likeToUpdate.result.id',
                },
                body: {
                    title: 'Updated Like',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: { title: 'params.title' },
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
        '404': {
            response: {
                status: 404,
                description: 'Like does not exists',
            },
        },
    },
};
//# sourceMappingURL=likeToUpdate.js.map