"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.likeToCreate = void 0;
exports.likeToCreate = {
    filePath: __filename,
    route: '/like',
    method: 'POST',
    operation: {
        summary: 'As a User I want to create a like/unlike for an object',
    },
    access: {
        resource: 'like',
        action: 'create',
    },
    codes: {
        '201': {
            response: {
                status: 201,
                description: 'Like created',
            },
            story: {
                auth: 'user',
                body: {
                    ownerId: 'mock.id',
                    ownerType: 'news',
                    reaction: 1,
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: { ownerId: 'params.ownerId', ownerType: 'news' },
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
    },
};
//# sourceMappingURL=likeToCreate.js.map