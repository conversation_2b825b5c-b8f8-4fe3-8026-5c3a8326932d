"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ratingToCreate = void 0;
exports.ratingToCreate = {
    filePath: __filename,
    route: '/rating',
    method: 'POST',
    operation: {
        summary: 'As a user, i want to create a rating for a content object',
    },
    access: {
        resource: 'rating',
        action: 'create',
        owner: true,
    },
    codes: {
        '201': {
            response: {
                status: 201,
                description: 'Rating created',
            },
            story: {
                auth: 'user',
                body: {
                    objectId: 'seed.newsToRead.result.id',
                    objectType: 'news',
                    count: 4,
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: {
                        objectId: 'params.objectId',
                        objectType: 'params.objectType',
                    },
                },
                {
                    type: 'contains',
                    data: {
                        averageRating: { count: 4 },
                    },
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
    },
};
//# sourceMappingURL=ratingToCreate.js.map