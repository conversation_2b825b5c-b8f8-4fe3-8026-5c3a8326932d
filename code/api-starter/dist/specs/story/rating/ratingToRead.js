"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ratingToRead = void 0;
exports.ratingToRead = {
    filePath: __filename,
    route: '/rating/rate/:id',
    method: 'GET',
    operation: {
        summary: 'As a admin, i want to read a rating',
    },
    access: {
        resource: 'rating',
        action: 'read',
    },
    codes: {
        '200': {
            response: {
                status: 200,
                description: "Rating's data",
            },
            story: {
                auth: 'admin',
                path: {
                    id: 'seed.ratingToRead.result.id',
                },
            },
            tests: [
                {
                    type: 'equal',
                    data: { count: 'seed.ratingToRead.params[1].count' },
                },
            ],
        },
        '401': {
            response: {
                status: 401,
                description: 'Incorrect credentials',
            },
        },
        '404': {
            story: {
                auth: 'admin',
                path: {
                    id: 'unknownRateId',
                },
            },
            response: {
                status: 404,
                description: "Rating doesn't exists",
            },
        },
    },
};
//# sourceMappingURL=ratingToRead.js.map