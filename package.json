{"name": "worker-appstudio", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@google/genai": "^0.10.0", "axios": "^1.7.9", "dotenv": "^16.4.5", "openai": "^4.73.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "executePatch": "git apply --recount --ignore-space-change --ignore-whitespace", "specs:project": "node src/scripts/projectSpecs/projectSpecs.js", "specs:feature": "node src/scripts/featureSpecs/featureSpecs.js", "specs:screen": "node src/scripts/screenSpecs/screenSpecs.js", "specs:core": "npm run specs:project && npm run specs:feature && npm run specs:screen", "specs:navigation": "node src/scripts/navigationSpecs/navigationSpecs.js", "specs:form": "node src/scripts/formSpecs/formSpecs.js", "specs:notification": "node src/scripts/notificationTemplates/notificationTemplates.js", "specs:home": "node src/scripts/homeRoutesSpecs/homeRoutesSpecs.js", "specs:step": "node src/scripts/stepsSpecs/stepsSpecs.js", "specs:workflow": "node src/scripts/workflowOrchestrator/workflowOrchestrator.js", "specs:ui": "npm run specs:navigation && npm run specs:form && npm run specs:home && npm run specs:notification && npm run specs:step", "specs:all": "npm run specs:core && node src/scripts/utils/delay.js && npm run specs:ui", "server:generate": "node src/scripts/serverCode/generateServer.js", "client:generate": "node src/scripts/clientCode/generateScreens.js", "build:client": "node src/scripts/clientCode/generateScreens.js && cd code/front-starter && npm run api:local && npm run local", "build:server": "node src/scripts/serverCode/generateServer.js && cd code/api-starter && npm run prisma:sync && npm run db:reset && npm run seed && npm run dev", "stripe:listen": "stripe login && stripe listen --forward-to localhost:3000/payment/webhook", "login": "stripe login", "listen": "stripe listen --forward-to localhost:3000/payment/webhook", "test:generate": "node src/scripts/tests/feature.test.js", "test:client": "cd code/front-starter && npm run wdio", "serve:api": "cd code/api-starter && npm run prisma:sync && npm run db:reset && npm run seed && npm run dev", "serve:web": "cd code/front-starter && npm run api:local && npm run local", "serve:all": "concurrently \"npm run serve:api\" \"npm run serve:web\" \"npm run stripe:listen\" --names \"API,WEB,STRIPE\" --prefix-colors \"blue,green,yellow\"", "launch": "node src/scripts/workflowOrchestrator/workflowOrchestrator.js"}, "devDependencies": {"jscodeshift": "^17.1.1"}}