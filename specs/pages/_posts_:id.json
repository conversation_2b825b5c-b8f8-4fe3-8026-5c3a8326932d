{"screenSpecs": {"name": "FullPostDetail", "type": "page", "path": "/posts/:id", "description": "This screen displays the full content of a single AI-generated blog post after it has been unlocked using credits. Users can view, interact with, and comment on the post.", "userExperience": [{"who": "App", "if": {"post": {"hasAccess": false}}, "action": {"type": "open", "modal": "/paywall"}, "where": "/posts/:id"}, {"who": "App", "if": {"post": {"hasAccess": true}}, "action": {"type": "load", "request": {"requestId": "readPost_request_1", "notify": null}}, "where": "/posts/:id"}, {"who": "App", "action": {"type": "load", "request": {"requestId": "getPostComments_request_1"}}, "where": "/posts/:id"}, {"who": "User", "action": {"type": "type", "element": {"type": "textarea", "eventId": "comment_input"}}, "where": "/posts/:id"}, {"who": "User", "action": {"type": "click", "element": {"type": "button", "eventId": "submit_comment_button"}}, "where": "/posts/:id"}, {"who": "App", "action": {"type": "send", "request": {"requestId": "createComment_request_2", "notify": {"target": "owner", "title": "New Comment on Your Post", "message": "A new comment has been added to your post.", "link": "/posts/:id"}}}, "where": "/posts/:id"}], "dataSchemas": {"post": {"owner": true, "canBeCommented": true, "description": "A detailed description of the Post entity, reflecting the user's created posts with functionality for content management and user association.", "fields": {"id": {"type": "String", "required": true, "isUser": false, "description": "A unique identifier for the post, typically a UUID."}, "title": {"type": "String", "required": true, "isUser": false, "description": "The title of the post."}, "summary": {"type": "String", "required": true, "isUser": false, "description": "A brief summary of the post."}, "content": {"type": "String", "required": true, "isUser": false, "description": "The full content of the post."}, "userId": {"type": "String", "required": true, "isUser": true, "description": "The identifier of the user who owns the post."}, "createdAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp of when the post was created."}, "updatedAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp of when the post was last updated."}, "topic": {"type": "String", "required": true, "isUser": false, "description": "The topic or category of the post."}, "workflowId": {"type": "String", "required": false, "isUser": false, "description": "Optional workflow identifier associated with the post."}, "isPremium": {"type": "Boolean", "required": true, "isUser": false, "description": "Indicates whether the post is premium content."}, "creditsRequired": {"type": "Int", "required": true, "isUser": false, "description": "The number of credits required to access the post if it is premium."}, "hasAccess": {"type": "Boolean", "required": true, "isUser": false, "description": "Indicates whether access to the post is available."}}}, "comment": {"canBeCommented": false, "usePayment": false, "description": "Entity for user comments on a blog post.", "fields": {"id": {"type": "string", "required": true, "description": "Unique identifier for the comment."}, "content": {"type": "string", "required": true, "description": "The text content of the comment."}, "authorId": {"type": "string", "required": true, "isUser": true, "description": "ID of the user who posted the comment."}, "objectId": {"type": "string", "required": true, "description": "The ID of the entity (e.g., post) being commented on."}, "objectType": {"type": "string", "required": true, "description": "The type of the entity (e.g., 'post') being commented on."}, "createdAt": {"type": "string", "required": false, "description": "Timestamp when the comment was created."}}}}, "requests": {"readPost_request_1": {"useAi": null, "useWorkflow": null, "dataResult": {"content": "Full blog post content here."}, "type": "GET", "params": {"id": "post-uuid-here"}, "body": {}, "path": "/posts/:id", "onSuccess": null}, "getPostComments_request_1": {"useAi": null, "useWorkflow": null, "dataResult": {"comments": [{"id": "comment1", "authorId": "user123", "content": "Great post!"}]}, "type": "GET", "params": {}, "body": {}, "path": "/comments/find", "onSuccess": null}, "createComment_request_2": {"useAi": null, "useWorkflow": null, "dataResult": {"id": "newCommentId"}, "type": "POST", "params": {}, "body": {"content": "This is a new comment.", "objectId": "post-id-here", "objectType": "post"}, "notifyLink": "/posts/:id", "path": "/comments/create", "onSuccess": null}}, "layouts": [{"layoutType": "Vertical", "groups": [{"groupName": "PostContent", "layoutType": "Vertical", "description": "Displays the main content of the blog post.", "elements": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Displays the full content of the blog post.", "dataSource": "readPost_request_1", "inputs": [], "dataRequest": {"type": "GET", "path": "/posts/content/:id", "fields": {"BlogContent": "content"}}}]}, {"groupName": "CommentsSection", "layoutType": "Vertical", "description": "Section for displaying and submitting comments.", "elements": [{"name": "CommentList", "description": "List of comments on the post.", "dataSource": "getPostComments_request_1", "items": true, "components": [{"eventId": "comment_component_1", "component": "Text", "data": "content"}]}, {"name": "NewComment", "description": "Form to submit a new comment.", "dataSource": "", "inputs": [{"eventId": "comment_input", "componentName": "TextArea", "fieldName": "content"}, {"eventId": "submit_comment_button", "componentName": "<PERSON><PERSON>", "type": "submit"}]}]}]}]}}