{"screenSpecs": {"name": "BlogPostSummary", "type": "page", "path": "/posts", "description": "Displays a list of blog posts, showing only public information like titles and summaries, with an indication if they require unlocking", "userExperience": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "findPosts_request_1", "notify": null}}, "where": "/posts"}, {"who": "User", "action": {"type": "select", "element": {"type": "list:item", "eventId": "post_entry"}}, "where": "/posts"}, {"who": "App", "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "click", "element": {"type": "list:item", "eventId": "post_entry"}}, "where": "/posts"}], "dataSchemas": {"post": {"owner": true, "canBeCommented": true, "description": "A detailed description of the Post entity, reflecting the user's created posts with functionality for content management and user association.", "fields": {"id": {"type": "String", "required": true, "isUser": false, "description": "A unique identifier for the post, typically a UUID."}, "title": {"type": "String", "required": true, "isUser": false, "description": "The title of the post."}, "summary": {"type": "String", "required": true, "isUser": false, "description": "A brief summary of the post."}, "content": {"type": "String", "required": true, "isUser": false, "description": "The full content of the post."}}}}, "requests": {"findPosts_request_1": {"type": "POST", "dataResult": {"items": [{"id": "postId1", "title": "Sample Post 1", "summary": "An overview of sample post 1", "isPremium": true, "hasAccess": false}, {"id": "postId2", "title": "Sample Post 2", "summary": "An overview of sample post 2", "isPremium": false, "hasAccess": true}]}, "path": "/posts/findPosts", "onSuccess": {"actionType": "load", "requestId": "findPosts_request_1"}}}, "layouts": [{"layoutType": "Vertical", "groups": [{"groupName": "PostList", "layoutType": "Vertical", "description": "Displays a list of blog posts with summaries", "elements": [{"name": "PostItem", "description": "Displays individual post with title and summary", "dataSource": "findPosts_request_1", "items": true, "dataRequest": {"type": "GET", "path": "/posts/findPosts", "fields": {"Title": "title", "Summary": "summary", "AccessIndicator": "hasAccess"}}, "components": [{"eventId": "post_entry", "component": "<PERSON><PERSON>", "texts": {"titleText": "Unlock"}}]}]}]}]}}