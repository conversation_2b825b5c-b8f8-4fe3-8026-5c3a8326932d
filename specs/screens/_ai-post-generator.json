{"screenSpecs": {"path": "/ai-post-generator", "description": "The primary screen where users input a topic or prompt to trigger AI-powered blog post generation", "dataSchemas": {"post": {"owner": true, "canBeCommented": true, "description": "A detailed description of the Post entity, reflecting the user's created posts with functionality for content management and user association.", "fields": {"id": {"type": "String", "required": true, "isUser": false, "description": "A unique identifier for the post, typically a UUID."}, "title": {"type": "String", "required": true, "isUser": false, "description": "The title of the post."}, "summary": {"type": "String", "required": true, "isUser": false, "description": "A brief summary of the post."}, "content": {"type": "String", "required": true, "isUser": false, "description": "The full content of the post."}, "userId": {"type": "String", "required": true, "isUser": true, "description": "The identifier of the user who owns the post."}, "createdAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp of when the post was created."}, "updatedAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp of when the post was last updated."}, "topic": {"type": "String", "required": true, "isUser": false, "description": "The topic or category of the post."}, "workflowId": {"type": "String", "required": false, "isUser": false, "description": "Optional workflow identifier associated with the post."}, "isPremium": {"type": "Boolean", "required": true, "isUser": false, "description": "Indicates whether the post is premium content."}, "creditsRequired": {"type": "Int", "required": true, "isUser": false, "description": "The number of credits required to access the post if it is premium."}, "hasAccess": {"type": "Boolean", "required": true, "isUser": false, "description": "Indicates whether access to the post is available."}}}}, "userExperience": {"GeneratePost": [{"who": "App", "if": null, "action": {"type": "load", "path": "/ai-post-generator"}}, {"who": "User", "action": {"type": "fill", "element": {"type": "input:text", "eventId": "post_topic_input"}}}, {"who": "User", "action": {"type": "click", "element": {"type": "button", "eventId": "generate_post_button"}}}, {"who": "App", "action": {"type": "send", "request": {"requestId": "generatePost_create_1", "notify": {"target": "me", "title": "Post Generated", "message": "Your blog post has been successfully created!", "link": "/posts/:id"}}}, "when": {"type": "click", "element": {"type": "button", "eventId": "generate_post_button"}}}, {"who": "App", "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "send", "request": {"requestId": "generatePost_create_1"}}}]}, "requests": {"generatePost_create_1": {"requestId": "generatePost_create_1", "useWorkflow": {"tasks": [{"name": "postWorkflowService.generatePost", "uiDescription": "Generating your blog post using AI...", "dependencies": [], "next": [{"name": "postWorkflowService.storePost", "uiDescription": "Saving your generated post...", "dependencies": ["postWorkflowService.generatePost"], "connect": ["postWorkflowService.generatePost"]}]}]}, "useAi": {"generationType": ["text"], "role": "Content Creator Assistant", "objective": "To generate a complete, coherent, and engaging blog post based on a user-provided topic."}, "dataSchema": "post", "type": "Create", "params": {}, "body": {"topic": "string"}, "notifyLink": "/posts/:id"}}}}