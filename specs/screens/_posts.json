{"screenSpecs": {"path": "/posts", "description": "Displays a list of blog posts, showing only public information like titles and summaries, with an indication if they require unlocking", "dataSchemas": {"post": {"owner": true, "canBeCommented": true, "description": "A detailed description of the Post entity, reflecting the user's created posts with functionality for content management and user association.", "fields": {"id": {"type": "String", "required": true, "isUser": false, "description": "A unique identifier for the post, typically a UUID."}, "title": {"type": "String", "required": true, "isUser": false, "description": "The title of the post."}, "summary": {"type": "String", "required": true, "isUser": false, "description": "A brief summary of the post."}, "content": {"type": "String", "required": true, "isUser": false, "description": "The full content of the post."}, "userId": {"type": "String", "required": true, "isUser": true, "description": "The identifier of the user who owns the post."}, "createdAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp of when the post was created."}, "updatedAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp of when the post was last updated."}, "topic": {"type": "String", "required": true, "isUser": false, "description": "The topic or category of the post."}, "workflowId": {"type": "String", "required": false, "isUser": false, "description": "Optional workflow identifier associated with the post."}, "isPremium": {"type": "Boolean", "required": true, "isUser": false, "description": "Indicates whether the post is premium content."}, "creditsRequired": {"type": "Int", "required": true, "isUser": false, "description": "The number of credits required to access the post if it is premium."}, "hasAccess": {"type": "Boolean", "required": true, "isUser": false, "description": "Indicates whether access to the post is available."}}}}, "userExperience": {"ViewPost": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "findPosts_request_1"}}}, {"who": "User", "action": {"type": "select", "element": {"type": "list:item", "eventId": "post_entry"}}}, {"who": "App", "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "click", "element": {"type": "list:item", "eventId": "post_entry"}}}]}, "requests": {"findPosts_request_1": {"requestId": "findPosts_request_1", "isArray": true, "useWorkflow": null, "useAi": null, "dataSchema": "post", "type": "Find", "params": {}, "body": {}, "notifyLink": null, "useAuthStore": null}}}}