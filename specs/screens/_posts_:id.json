{"screenSpecs": {"path": "/posts/:id", "description": "The screen displaying the full content of the AI-generated blog post. Dedicated screen displaying the full content of a single blog post after it has been unlocked by credits. Integrated within the post detail screen, this section allows users to view existing comments and submit new ones", "dataSchemas": {"post": {"owner": true, "canBeCommented": true, "description": "A detailed description of the Post entity, reflecting the user's created posts with functionality for content management and user association.", "fields": {"id": {"type": "String", "required": true, "isUser": false, "description": "A unique identifier for the post, typically a UUID."}, "title": {"type": "String", "required": true, "isUser": false, "description": "The title of the post."}, "summary": {"type": "String", "required": true, "isUser": false, "description": "A brief summary of the post."}, "content": {"type": "String", "required": true, "isUser": false, "description": "The full content of the post."}, "userId": {"type": "String", "required": true, "isUser": true, "description": "The identifier of the user who owns the post."}, "createdAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp of when the post was created."}, "updatedAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp of when the post was last updated."}, "topic": {"type": "String", "required": true, "isUser": false, "description": "The topic or category of the post."}, "workflowId": {"type": "String", "required": false, "isUser": false, "description": "Optional workflow identifier associated with the post."}, "isPremium": {"type": "Boolean", "required": true, "isUser": false, "description": "Indicates whether the post is premium content."}, "creditsRequired": {"type": "Int", "required": true, "isUser": false, "description": "The number of credits required to access the post if it is premium."}, "hasAccess": {"type": "Boolean", "required": true, "isUser": false, "description": "Indicates whether access to the post is available."}}}, "comment": {"canBeCommented": false, "usePayment": false, "description": "Entity for user comments on a blog post.", "fields": {"id": {"type": "string", "required": true, "description": "Unique identifier for the comment."}, "content": {"type": "string", "required": true, "description": "The text content of the comment."}, "authorId": {"type": "string", "required": true, "isUser": true, "description": "ID of the user who posted the comment."}, "objectId": {"type": "string", "required": true, "description": "The ID of the entity (e.g., post) being commented on."}, "objectType": {"type": "string", "required": true, "description": "The type of the entity (e.g., 'post') being commented on."}, "createdAt": {"type": "string", "required": false, "description": "Timestamp when the comment was created."}}}}, "userExperience": {"GeneratePost": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "readPost_request_1"}}}], "ViewPost": [{"who": "App", "if": {"post": {"hasAccess": false}}, "action": {"type": "open", "modal": "/paywall"}}, {"who": "App", "if": {"post": {"hasAccess": true}}, "action": {"type": "load", "request": {"requestId": "readPost_request_3"}}}], "AddComment": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "getPostComments_request_1"}}}, {"who": "User", "action": {"type": "fill", "element": {"type": "textarea", "eventId": "comment_input"}}}, {"who": "User", "action": {"type": "click", "element": {"type": "button", "eventId": "submit_comment_button"}}}, {"who": "App", "action": {"type": "send", "request": {"requestId": "createComment_request_2", "notify": {"target": "owner", "title": "New Comment on Your Post", "message": "A new comment has been added to your post.", "link": "/posts/:id"}}}, "when": {"type": "click", "element": {"type": "button", "eventId": "submit_comment_button"}}}, {"who": "App", "action": {"type": "load", "request": {"requestId": "getPostComments_request_1"}}}]}, "requests": {"readPost_request_1": {"requestId": "readPost_request_1", "dataSchema": "post", "type": "Read", "params": {"id": "string"}}, "readPost_request_3": {"requestId": "readPost_request_3", "useWorkflow": null, "useAi": null, "dataSchema": "post", "type": "Read", "params": {"id": "string"}, "body": {}, "notifyLink": null, "useAuthStore": null}, "getPostComments_request_1": {"requestId": "getPostComments_request_1", "isArray": true, "useWorkflow": null, "useAi": null, "dataSchema": "comment", "type": "Find", "params": {}, "body": {"objectId": "string", "objectType": "string"}, "notifyLink": null}, "createComment_request_2": {"requestId": "createComment_request_2", "isArray": false, "useWorkflow": null, "useAi": null, "dataSchema": "comment", "type": "Create", "params": {}, "body": {"content": "string", "objectId": "string", "objectType": "string"}, "notifyLink": "/posts/:id"}}}}