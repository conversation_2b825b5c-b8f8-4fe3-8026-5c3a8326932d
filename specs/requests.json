{"posts": [{"type": "POST", "path": "/posts/findPosts", "useWorkflow": {}, "params": {}, "body": {}, "dataResult": {"items": [{"id": "postId1", "title": "Sample Post 1", "summary": "An overview of sample post 1", "isPremium": true, "hasAccess": false}, {"id": "postId2", "title": "Sample Post 2", "summary": "An overview of sample post 2", "isPremium": false, "hasAccess": true}]}}, {"type": "GET", "path": "/posts/:id", "useAi": null, "useWorkflow": {}, "params": {"id": "post-uuid-here"}, "body": {}, "dataResult": {"content": "Full blog post content here."}}], "comments": [{"type": "GET", "path": "/comments/find", "useAi": null, "useWorkflow": {}, "params": {}, "body": {}, "dataResult": {"comments": [{"id": "comment1", "authorId": "user123", "content": "Great post!"}]}}, {"type": "POST", "path": "/comments/create", "useAi": null, "useWorkflow": {}, "params": {}, "body": {"content": "This is a new comment.", "objectId": "post-id-here", "objectType": "post"}, "dataResult": {"id": "newCommentId"}, "notifyLink": "/posts/:id"}]}